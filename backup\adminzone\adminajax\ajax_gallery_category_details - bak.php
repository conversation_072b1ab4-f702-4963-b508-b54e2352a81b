<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $cat_id;
	public $cat_name;
	public $cat_presedence;
	public $cat_photo;
	public $path_upload_image;
	public $maxfilesize;
		
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		//--- Set Current Module Name ---
		$this->module_name = "Add Category";
		$this->cur_page_url = "ajax_gallery_category_details.php";
		$this->list_page_url = "gallery_category_details.php";
		$this->path_upload_image = "../../gallery_cat_images/";
		$this->maxfilesize = 2*1024*1024;
		
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
				
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->cat_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->cat_id = 0;
			}
		}
		
		return 1;
	}


	//==== Create New Record ====
	public function createPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editPart()
	{ 
		//--- Get Record Id ---
		$this->getRecordId();
		

		if ($this->cat_id == 0)
		{
		
			$_SESSION['app_error'] = "Record not found123..";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
			
		}
		

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{		
		$this->cat_id= intval(CommonFunctions::replaceChars($_POST["cat_id"], 0));
		$this->cat_name = CommonFunctions::replaceChars($_POST["cat_name"], 11);
	
		return 1;
	}
	
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";
		$file_ext = "jpg,jpeg,png";
		
		if($this->cat_name == "")
		{
			$myerr = "Category Name is not specified";
		}
		else if(DatabaseManager::iexistsInTable($this->link_id, "tbl_photo_category", "cat_id <> " . $this->cat_id . " and cat_name = '" . $this->cat_name . "' ") == 1)
		{
			$myerr = "Category Name: " . $this->cat_name . " already exists.";
		}
			
	   else if(!empty($_FILES['announcement_upload']['name']))
			{
				
				if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['announcement_upload']['name']), $file_ext) == 0)
				{
					$myerr = "Un-supported file format";
				}
				else if(($_FILES['announcement_upload']['size'] > $this->maxfilesize))
				{
					$myerr = "File size should not be more than 4MB";
				}
				
			}		
			
		if($myerr != "")
		{			
			echo $myerr;
			return 0;
			exit();
		}
	
		return 1;
		
	}
	
	//==== Save Data ====
	public function saveData()
	{
		
		$myerr = "";
			
		if($this->task == 1)
		{	
		
			//--- Begin Transaction
		   $this->link_id->autocommit(FALSE);

			//--- Get New Id
			$this->cat_id = DatabaseManager::igetNewId($this->link_id, "tbl_photo_category", "cat_id", "");
			
			if(isset($_FILES['announcement_upload']['name']) && $_FILES['announcement_upload']['name'] !="")
			{
				
					$photo="";
					$ext = strtolower(substr(strrchr($_FILES['announcement_upload']['name'], "."), 1));
					$name = strtolower(substr($_FILES['announcement_upload']['name'], 0, strpos($_FILES['announcement_upload']['name'], ".")));
					
					$path = $this->path_upload_image . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
					$medium_path = $this->path_upload_image ."medium_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
					
					$small_path = $this->path_upload_image ."thumb_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
										
					$photo = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
										
					move_uploaded_file($_FILES['announcement_upload']['tmp_name'], $path);
					$resizeObj = new resize($path);
					
					$resizeObj -> resizeImage(600, 450 ,'auto');
					$resizeObj -> saveImage($path, 100);
					
					$resizeObj -> resizeImage(151, 130 ,'crop');
					$resizeObj -> saveImage($medium_path, 100);	
									
					$resizeObj -> resizeImage(120, 90 ,'crop');
					$resizeObj -> saveImage($small_path, 100);
					
					 //--- Insert Data
					$tmpSql = "INSERT INTO tbl_photo_category(cat_id, cat_name,cat_photo)"
							   ."VALUES(".$this->cat_id.",'".$this->cat_name."','".$photo."')";
					//return $tmpSql;		   
			}else{
			
				 //--- Insert Data
					$tmpSql = "INSERT INTO tbl_photo_category(cat_id, cat_name)"
							   ."VALUES(".$this->cat_id.",'".$this->cat_name."')";
			}

			
			$rs = $this->link_id->query($tmpSql);
			if($this->link_id->affected_rows == -1)
			{				echo "inside1"; die();
				$myerr = "Data could not be saved";
				$this->link_id->rollback();
				echo $myerr;
				exit();
			}	
		
			else
			{
								
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully saved";
				echo '1';
				exit();				
			}
			
		}
		else if($this->task == 2)
		{
			
			if(isset($_FILES['announcement_upload']['name']) && $_FILES['announcement_upload']['name'] !="")
			{
			$edit_upload_image = DatabaseManager::igetDataFromTable($this->link_id, "tbl_photo_gallery", "cat_photo", "", "cat_is = " . $this->cat_id) ;
			if($edit_upload_image != "")
					{
						CommonFunctions::removeFiles($this->path_upload_image, $edit_upload_image);	
						CommonFunctions::removeFiles($this->path_upload_image, "medium_".$edit_upload_image);			
						CommonFunctions::removeFiles($this->path_upload_image, "thumb_".$edit_upload_image);
					}
					
					$photo="";
					$ext = strtolower(substr(strrchr($_FILES['announcement_upload']['name'], "."), 1));
					$name = strtolower(substr($_FILES['announcement_upload']['name'], 0, strpos($_FILES['announcement_upload']['name'], ".")));
					
					$path = $this->path_upload_image . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
					$medium_path = $this->path_upload_image ."medium_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
					$thumbpath = $this->path_upload_image ."thumb_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
										
					$photo = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
										
					move_uploaded_file($_FILES['announcement_upload']['tmp_name'], $path);
					$resizeObj = new resize($path);
					$resizeObj -> resizeImage(600, 600 ,'auto');
					$resizeObj -> saveImage($path, 100);
					
					$resizeObj -> resizeImage(151, 130 ,'crop');
					$resizeObj -> saveImage($medium_path, 100);
					
					$resizeObj -> resizeImage(100, 100 ,'crop');
					$resizeObj -> saveImage($thumbpath, 100);
			
						        		
		           //--- Update Data
			       $tmpSql = "UPDATE tbl_photo_category SET " 
					        ."cat_name = '" . addslashes($this->cat_name) . "'"
					        .",cat_photo= '" . $photo ."'"
							. " WHERE cat_id = '" . $this->cat_id . "'";
					
							
							
			      $rs = $this->link_id->query($tmpSql);
			

		}else{
				 //--- Update Data if no photo
			       $tmpSql1 = "UPDATE tbl_photo_category SET " 
					        ."cat_name = '" . addslashes($this->cat_name) . "'"
					       	. " WHERE cat_id = '" . $this->cat_id . "'";
									
							
			      $rs = $this->link_id->query($tmpSql1);
		
			
		}
		
				 if($this->link_id->affected_rows == -1)
			      {
				   $myerr = "Data could not be saved";				
				   $this->link_id->rollback();			
				   echo $myerr;
				   exit();
			      }
			   
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo "1";				
				exit();				
			}
		
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}
$objCurPage = new CurrentPage();
?>