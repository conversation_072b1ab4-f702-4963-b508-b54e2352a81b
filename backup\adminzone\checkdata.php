<?php

require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
$host = $config["DB_HOST"];
$user = $config["DB_USER_ID"];
$pass = $config["DB_PASSWORD"];

mysql_connect($host, $user, $pass);

mysql_select_db($config["DB_NAME"]);
if(isset($_POST['user_email']))
{
	$emailId=$_POST['user_email'];

	$checkdata=" SELECT user_email FROM admin_details WHERE user_email='".$emailId."' ";

	$query=mysql_query($checkdata);

	if(mysql_num_rows($query)>0)
	{
		echo "Email Already Exist";
	}
	else
	{
		//echo "OK";
	}
exit();
}

if (isset($_GET['term'])){
	//connect with the database
    $db = new mysqli($config["DB_HOST"],$config["DB_USER_ID"],$config["DB_PASSWORD"],$config["DB_NAME"]);
    
    //get search term
    $searchTerm = $_GET['term'];
    
    //get matched data from skills table
    $query = $db->query("SELECT DISTINCT hob_list FROM tbl_hobbies WHERE hob_list LIKE '%".$searchTerm."%' ORDER BY hob_list ASC");
    while ($row = $query->fetch_assoc()) {
        $data[] = $row['hob_list'];
    }
    
    //return json data
    echo json_encode($data);
}

?>
<?php

function checkValues($value)
{
	 // Use this function on all those values where you want to check for both sql injection and cross site scripting
	 //Trim the value
	 $value = trim($value);
	 
	// Stripslashes
	if (get_magic_quotes_gpc()) {
		$value = stripslashes($value);
	}
	
	 // Convert all &lt;, &gt; etc. to normal html and then strip these
	 $value = strtr($value,array_flip(get_html_translation_table(HTML_ENTITIES)));
	
	 // Strip HTML Tags
	 $value = strip_tags($value);
	
	// Quote the value
	$value = mysql_real_escape_string($value);
	return $value;
	
}	

if (!isset($_GET['term'])){
$rec = checkValues($_REQUEST['val']);
//get table contents
if($rec !="")
{
if($rec )
{
	$sql = "select * from tbl_hobbies where hob_list like '%$rec%' LIMIT 4";
}
else
{
	$sql = "select * from tbl_hobbies";
}
$rsd = mysql_query($sql);
$total =  mysql_num_rows($rsd);
?>
<?php
while ($rows = mysql_fetch_assoc($rsd))
{
	$user_id = $rows['user_id'];
	if($user_id !="")
	{
		$sql1 = "select * from  admin_details where user_id =".$user_id." ";
		//echo $sql1;
		$rsd1 = mysql_query($sql1);
		$total1 =  mysql_num_rows($rsd1);
		while ($rows1 = mysql_fetch_assoc($rsd1))
		{
	?>
<div class="each_rec"><?php echo "Name: ". $rows1["user_name"]."<br>Age ". $rows1["user_age"]."<br>Gender: ".$rows1["user_gender"]."<br>Contact No: ".$rows1["user_contact_no"]."<br> Address". $rows1["user_address"] ?></div>
<?php
} } }
if($total==0){ echo '<div class="no-rec">No Record Found !</div>';}}}?>
