<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	
	public $app_config;
	
	public $prec_edit;
	
	public $video_id;
	public $title;
	public $embed_code;
	
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		//--- Set Current Module Name ---
		$this->module_name = "Download Details";
		$this->cur_page_url = "ajax_downloads.php";
		$this->list_page_url = "ajax_downloads.php";
		
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
				
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}

	}
	
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->video_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->video_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->video_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->video_id = 0;
			}
		}
		return 1;
	}
	//==== Create New Record ====
	public function createPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}
		return 1;
	}
	//==== Edit Existing Record ====
	public function editPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
        
		if ($this->video_id == 0)

		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}
		return 1;
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{			
	
						
		$this->title = 	CommonFunctions::replaceChars($_POST["title"], 0);	
		$this->embed_code = CommonFunctions::replaceChars($_POST["embed_code"], 0);				
					
		/*if(isset($_POST["embed_code"]))
		{
			$this->embed_code = $_POST["embed_code"];			
		}*/
		return 1;
	}
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{						
		$myerr = "";
		
		if(trim($this->title) == "")
		{
			$myerr = "Please specify Title";
		}
		else if(trim($this->embed_code) == "")
		{
			$myerr = "Please Specify Embed Code";
		}
		

		if($myerr != "")
		{			
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		//--- Begin Transaction		
		$this->link_id->autocommit(FALSE);
				
		if($this->task == 1)
		{  	
					
			//--- Insert Data			
			$tmpSql = "INSERT INTO tbl_video(id, title, embed_code) 
					   VALUES(null, "
					   ."'".addslashes($this->title). "','"
					   .addslashes($this->embed_code)."')";
					   
			$rs = $this->link_id->query($tmpSql);				
			
			$this->video_id = mysqli_insert_id($this->link_id);
									
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}			
			else
			{				
				$tmpSql2 = "UPDATE tbl_video SET embed_code = '" . addslashes($this->embed_code) ."'"
					    . " WHERE id = " . $this->video_id;
				
				$rs2 = $this->link_id->query($tmpSql2);
						
				$this->link_id->commit();			
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
		}
		else if($this->task == 2)
		{
			$tmpSql = "UPDATE tbl_video SET " 					
					. "  title = '" . addslashes($this->title) ."'"
					. ", embed_code = '" . addslashes($this->embed_code) ."'"
					. "  WHERE id = " . $this->video_id;
					
			//echo $tmpSql; die('sdsd');			
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();				
			}
		
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{			
			return 1;
		}
	}
	
}

$objCurPage = new CurrentPage();

?>