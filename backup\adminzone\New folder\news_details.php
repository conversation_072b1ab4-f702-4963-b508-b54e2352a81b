<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $app_config;
	public $news_id;
	public $news_date,$news_headline,$news_details,$news_upload;
	public $file_path;
	

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		//--- Set Current Module Name ---
		$this->module_name = "News Details";
		$this->cur_page_url = "news_details.php";
		$this->list_page_url = "news.php";
		
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
		
				
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		$this->file_path = "../news_files/";
						
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
			//echo($this->task); exit;	
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createDetail();
				break;
			case 2:
				//--- Edit Record
				$this->editDetail();
				break;
			default:
				//echo("i'm 0"); exit;
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				die("_");
		}
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		return 1;
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->news_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->news_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->news_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->news_id = 0;
			}
		}
		
		return 1;
	}


	//==== Create New Record ====
	public function createDetail()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}		
		return 1;
	}
	//==== Edit Existing Record ====
	public function editDetail()
	{
		//--- Get Record Id ---
		$this->getRecordId();
        
		if ($this->news_id == 0)

		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			die("_");
		}
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		return 1;
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			$this->news_id = "";	
			$this->news_headline = "";	
			$this->news_details = "";	
			$this->news_date = date('m-d-Y');			
			
		}
		else if($this->task == 2) 
		{
		   if($this->news_id != 0)
		   {
			   
			//--- Edit Mode. Initialize fields by getting values from Database
				$tmpSql = "SELECT news_id,heading,news_date,details,attachmemt,is_new "
					   . " FROM news WHERE news_id = '" . $this->news_id  . "'";
				//echo $tmpSql; die();
				$rs = $this->link_id->query($tmpSql);
							
				if( (!$rs) || (!($rec = $rs->fetch_array())) )
				{
					$_SESSION['app_error'] = "Record not found...";
					header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec);
					die("_");
				}
				else
				{		
					$this->news_id = $rec["news_id"];	
					$this->news_date = CommonFunctions::formatMyDateTime($rec["news_date"],"m-d-Y");		
					$this->news_headline = $rec["heading"];	
					$this->news_details =  $rec["details"];	
					$this->news_upload = $rec["attachmemt"];
				}
		    }		
		}
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{	
		return 1;
	}
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";										
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";					
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link rel="stylesheet" href="css/admin.css" type="text/css" />
<script type="text/javascript" src="js/tcal.js"></script>
<script type="text/javascript" src="js/jquery-1.10.1.min.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script type="text/javascript" src="js/tcal.js"></script>
<link href="css/tcal.css" rel="stylesheet" type="text/css" />
<link href="images/logo.png" rel="icon" type="image/x-icon" />
<script language="javascript" type="text/javascript">
$(document).ready(function(){
	$("#loadingImage").hide();	
	$('#cmdSave').click(function()
	{	
	    $("#loadingImage").hide();	
		validateSave();
	});
});
function validateSave()
{
	$("#loaderImg").html('<img src="images/progress-bar.gif" />');
	$("#loaderImg").show();
	$('#frmNewsDetails').submit();
}
$('#frmNewsDetails').ajaxForm({	
	target: '',
	success: function(data){
		$("#loadingImage").hide();
		if(data == 1)
		{	
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			location.href='<?php echo $objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&msg=1"; ?>';
			return false;					
		}
		else
		{
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			$('#showError').html(data);
			return false;
		}
	}			
});

/*---------------------------------------------------------*/

$("#addMore").click(function(){
	
	alert("aadsf");
	//alert($('.data-div-row').size());
			
		if($('.data-div-row').size() >= 2){
			$("#addMore").hide();
		}

		var divStr = '<div class="row data-div-row">' 
				 + '<div class="col-sm-12">' 
				 + '<div class="form-group">'
				 + '<input type="text" class="tcal form-control" name="news_date[]" idx="" value="" placeholder="Date"/>' 
				 + '</div>' 
				 
				 + '<div class="form-group">' 
				 + '<input type="text" class="form-control" name="heading[]" idx="" placeholder="Headline"/>' 
				 + '</div>' 
				 
				 + '<div class="form-group">' 
				 + '<textarea name="details[]" idx="" class="form-control" placeholder="Description"> </textarea>'
				 + '</div>' 
				 
				 + '<div class="form-group">' 
				 + '<input type="file" class="file" name="news_upload[]" idx="" placeholder="Upload File" onchange="checkFileSize(this);"/>' 
				 + '<input name="news_upload_HX[]" value="1" type="hidden">'
				 + '</div>'
				 + '</div>' 
				 
				 + '<div class="col-sm-2 remove_div">' 
				 + '<button class="remove_recp_doc remove" onclick="removeDivRow(this)" title="Remove" >-</button>' 
				 + '</div>' 
				 
				 + '</div>';		
				 
		
		$("#recp_doc_main_div .recp_doc_body").append(divStr);
					 
		var new_div = $('.data-div-row').last();			 
		
		new_div.find("input[type='text'][name='news_date[]']").each(function(index, element) {		   
		   $(this).attr('idx', $('.data-div-row').size());
		   $(this).attr('id', 'news_date_' + $('.data-div-row').size());
		   $(this).attr('placeholder', 'Date ' + $('.data-div-row').size());
			
		});
		new_div.find("input[type='text'][name='heading[]']").each(function(index, element) {		   
		   $(this).attr('idx', $('.data-div-row').size());
		   $(this).attr('id', 'heading_' + $('.data-div-row').size());
		   $(this).attr('placeholder', 'Headline ' + $('.data-div-row').size());
			
		});
		new_div.find("input[type='text'][name='details[]']").each(function(index, element) {		   
		   $(this).attr('idx', $('.data-div-row').size());
		   $(this).attr('id', 'heading_' + $('.data-div-row').size());
		   $(this).attr('placeholder', 'Description ' + $('.data-div-row').size());
			
		});
		new_div.find("textarea[name='news_upload[]']").each(function(index, element) {		   
		   $(this).attr('idx', $('.data-div-row').size());
		   $(this).attr('id', 'news_upload_' + $('.data-div-row').size());
		   $(this).attr('name', 'news_upload_' + $('.data-div-row').size());
			
		});
		
										
	});


</script>
</head>
<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:520px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-sm-8 maincontent" style="border:1px solid #ccc; padding:2px;">
      <div class="col-sm-6">
      <div id="show_error">
        <?php CommonFunctions::displayErrMsg(); ?>
      </div>
      <div id="showError" style="text-align:center; color:#F00;"></div>
        <div class="tableRow" align="center">
          <div class="imgLoader" id="loaderImg" style="display:none;"></div>
        </div>
        <h1>
          <?php if($objCurPage->task == 1) { echo "Add"; }else{ echo "Edit"; } ?>
          News</h1>
          
          
          
          <div class="f-row" id="recp_doc_main_div">
                      <div class="full">
                       <form name="frmNewsDetails" id="frmNewsDetails" method="post" action="adminajax/add_news_details.php" enctype="multipart/form-data" >
                       
                         <div class="recp_doc_body">
                            
                             <div class="row data-div-row">
                                    
                                      <div class="form-group"> 
                                          <input type="text" name="news_date[]" id="news_date_1" idx="1" class="tcal form-control" value="" placeholder="Date 1" readonly="readonly" />		
                                      </div>
                                      
                                      <div class="form-group"> 
                                          <input type="text" name="heading[]" id="heading_1" class="form-control" placeholder="Headline 1" value=""  />
                                      </div>
                                      
                                      <div class="form-group"> 
                                          <textarea name="details" id="details" class="form-control" placeholder="Description 1" style="height: 150px !important;"> </textarea>
                                      </div>
                                      
                                      <div class="form-group">
                                      <input type="file" name="news_upload_1" id="news_upload_1" idx="1" class="file" maxlength="100" placeholder="Upload File" onchange="checkFileSize(this);" />	
                                      <input name="news_upload_HX[]" value="1" type="hidden">				
                                      </div>
                                    
                            </div>
                        
                         </div>
                        
                         <div class="row divrow-btm-mar">
                                   <a class="add_more" id="addMore" title="Add More">Add more recipe files</a>
                            </div>
                        
                          <div class="f-row full">
                            <!--<button type="submit" class="btn btn-info" name="cmdSaveRecipeDoc" id="cmdSaveRecipeDoc">Submit</button> -->
                            
                            <input type="hidden" name="recid" id="recid" value="<?php echo $objCurPage->news_id; ?>" />
                              <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />
                              <input name="sortby" type="hidden" id="sortby" value="<?php echo $objCurPage->sortby; ?>" />
                              <input name="sortmode" type="hidden" id="sortmode" value="<?php echo $objCurPage->sortmode; ?>" />
                              <input type="hidden" name="postMe" id="postMe" value="Y" />
                              <input type="button" name="cmdSave" id="cmdSave" value="<?php if($objCurPage->task==1) {echo("Save");} else {echo("Update");} ?>" class="cButton" />
                              <input name="cmdRevert" type="button" class="cButton" id="cmdRevert" value="Revert" onClick="window.location.href='<?php echo($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&recid=" . $objCurPage->news_id . "&task=". $objCurPage->task ); ?>'" />
                              <input name="cmdExit" type="submit" tabindex="9" class="cButton" id="cmdExit" onClick="window.location.href='<?php echo($objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode ); ?>'" value="Exit" />
                            
                            
                            
                         </div>  
                      </form>  
                      
                    </div>
                  </div>
          
          
          
          
        <!--<form name="frmNewsDetails" id="frmNewsDetails" method="post" action="adminajax/add_news_details.php"  enctype="multipart/form-data">
          <div class="form-group">
            <label for="news_date">Date:<sup class="reqd">*</sup></label>
            <input type="text" class="tcal form-control" name="news_date" id="news_date"  readonly="readonly" value="< ?php echo $objCurPage->news_date;?>" />
          </div>
          <div class="form-group">
            <label for="news_date">Headline:<sup class="reqd">*</sup></label>
            <input type="text" name="heading" id="heading" class="form-control"  value="< ?php echo $objCurPage->news_headline; ?>"  />
          </div>
          <div class="form-group">
            <label for="details">Description:</label>
            <textarea name="details" id="details" class="form-control">< ?php echo $objCurPage->news_details; ?></textarea>
          </div>
          <div class="form-group">
            <label for="email">Upload File</label>
            <input name="news_upload" type="file"  id="news_upload" class="form-control" value="< ?php echo $objCurPage->news_upload;?>" />
            <br />
            <font color="#003399"><small>(File type: .jpg,.jpeg,.doc, .text, .pdf, .xls, .docx, .xlsx&nbsp;&nbsp;&nbsp;maxsize:4MB)</small></font>
            <input name="news_upload_edit" type="hidden" id="news_upload_edit" value="< ?php echo $objCurPage->news_upload;?>" />
            &nbsp;&nbsp;
            < ? if($objCurPage->news_upload != "") { ?>
            <br/>
            <i><font color="#003399"><small>(Last file: <b><a target="_blank" href="< ?php echo $objCurPage->file_path . $objCurPage->news_upload ;  ?>" class="generalink">View</a></b>)</small></font></i>
            < ? } ?>
          </div>
          <input type="hidden" name="recid" id="recid" value="< ?php echo $objCurPage->news_id; ?>" />
          <input type="hidden" name="task" id="task" value="< ?php echo $objCurPage->task; ?>" />
          <input name="sortby" type="hidden" id="sortby" value="< ?php echo $objCurPage->sortby; ?>" />
          <input name="sortmode" type="hidden" id="sortmode" value="< ?php echo $objCurPage->sortmode; ?>" />
          <input type="hidden" name="postMe" value="Y" />
          <input type="button" name="cmdSave" id="cmdSave" value="< ?php if($objCurPage->task==1) {echo("Save");} else {echo("Update");} ?>" class="cButton" />
          <input name="cmdRevert" type="button" class="cButton" id="cmdRevert" value="Revert" onClick="window.location.href='< ?php echo($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&recid=" . $objCurPage->news_id . "&task=". $objCurPage->task ); ?>'" />
          <input name="cmdExit" type="button" tabindex="9" class="cButton" id="cmdExit" onClick="window.location.href='< ?php echo($objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode ); ?>'" value="Exit" />
        </form>-->
        
        
      </div>
      <div class="col-sm-6">&nbsp;</div>
    </div>
    <div class="col-sm-1">&nbsp;</div>
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>