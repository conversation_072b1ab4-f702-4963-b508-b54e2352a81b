*, *:before, *:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: 'Nunito', sans-serif;
  /*font-family:Georgia, "Times New Roman", Times, serif !important;*/
  color: #384047;
}

form {
  max-width: 300px;
  margin: 10px auto;
  padding: 10px 20px;
  background: rgba(0,0,0,0.5);
  border-radius: 8px;
}

fieldset {
  margin-bottom: 30px;
	border:1px solid #fff
}
h1 {
  margin: 0 0 30px 0;
  text-align: center;
}

input[type="text"],
input[type="password"],
input[type="date"],
input[type="datetime"],
input[type="email"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="time"],
input[type="url"],
textarea,
select {
  background:transparent !important;
  border: none;
  font-size: 16px;
  height: auto;
  margin: 0;
  outline: 0;
  padding: 15px;
  width: 100%;
  background-color: #e8eeef;
  color: #fff;
  box-shadow: 0 1px 0 rgba(0,0,0,0.03) inset;
  margin-bottom: 10px;
  font-family:Verdana, Geneva, sans-serif;
  border:1px solid rgba(255,255,255,0.2);
  text-align:center;
}

input[type="radio"],
input[type="checkbox"] {
  margin: 0 4px 8px 0;
}

select {
  padding: 6px;
  height: 32px;
  border-radius: 2px;
}
.login_frm{color:#fff}
.tbl_reg input[type="button"],.login_frm input[type="submit"],
button, .buttons , .goback{
  padding: 19px 39px 18px 39px;
  color: #FFF;
  background:rgba(56, 130, 227, 0.6) none repeat scroll 0 0;
  font-size: 18px;
  text-align: center;
  font-style: normal;
  width: 100%;
  border: none;
  text-transform:uppercase;
  box-shadow: 0 -1px 0 rgba(255,255,255,0.1) inset;
  margin-bottom: 10px;
  cursor:pointer;
}
.buttons{padding:2%; text-decoration:none}



legend {
  font-size: 1.4em;
  margin-bottom: 10px;
  color:#fff; 
  text-transform:uppercase;
  text-align:center;
  font-family:"good times";
}

label {
  display: block;
  margin-bottom: 8px;
}

label.light {
  font-weight: 300;
  display: inline;
}

.number {
  background-color: #5fcf80;
  color: #fff;
  height: 30px;
  width: 30px;
  display: inline-block;
  font-size: 0.8em;
  margin-right: 4px;
  line-height: 30px;
  text-align: center;
  text-shadow: 0 1px 0 rgba(255,255,255,0.2);
  border-radius: 100%;
}

@media screen and (min-width: 480px) {

  form {
    max-width: 480px;
  }

}

.tbl_reg{width:100%; padding:5px}
.hobbies 
{
	width:40%; 
	float:left;
	/*padding-top: 13px;*/
}

.tbl_reg textarea{max-height:100px; height:100px; resize:none}
/* SQUARED THREE */
body {
	background:url("../images/building-opacity.jpg");
	background-repeat:no-repeat;
	background-size:cover;
}

input[type=checkbox] {
	visibility: hidden;
}

/* SQUARED THREE */
.squaredThree {	
	margin: 20px auto;
	position: relative;
}

.squaredThree label {
	cursor: pointer;
	position: absolute;
	width: 20px;
	height: 20px;
	top: 0;
	border-radius: 4px;

	-webkit-box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,.4);
	-moz-box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,.4);
	box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,.4);

	background: -webkit-linear-gradient(top, #222 0%, #45484d 100%);
	background: -moz-linear-gradient(top, #222 0%, #45484d 100%);
	background: -o-linear-gradient(top, #222 0%, #45484d 100%);
	background: -ms-linear-gradient(top, #222 0%, #45484d 100%);
	background: linear-gradient(top, #222 0%, #45484d 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#222', endColorstr='#45484d',GradientType=0 );
}

.squaredThree label:after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	opacity: 0;
	content: '';
	position: absolute;
	width: 9px;
	height: 5px;
	background: transparent;
	top: 7px;
	left: 5px;
	border: 3px solid #fcfff4;
	border-top: none;
	border-right: none;

	-webkit-transform: rotate(-45deg);
	-moz-transform: rotate(-45deg);
	-o-transform: rotate(-45deg);
	-ms-transform: rotate(-45deg);
	transform: rotate(-45deg);
}

.squaredThree label:hover::after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
	filter: alpha(opacity=30);
	opacity: 0.3;
}

.squaredThree input[type=checkbox]:checked + label:after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: alpha(opacity=100);
	opacity: 1;
}
.squaredThree span{padding-left:5px}
.showError{
font-family:Arial, Helvetica, sans-serif;	
font-size : 14px;
font-weight:bold;
color:#FF3300;
text-align:center;
height:20px;
}


