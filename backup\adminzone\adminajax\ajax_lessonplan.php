<?php
//print_r($_POST); die();
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $record_id;
	public $branch;
	public $branch_name;
	public $lessonplan_title;
	public $record_date;

	public $upload_file;
	public $path_upload_file;
	public $arr_lessonplan_title, $arr_lessonplan_file;
	public $lessonplan_title_edit, $maxfilesize;
	
			
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		
		//--- Set Current Module Name ---
		$this->module_name = "Ajax Lesson Plan Details";
		$this->cur_page_url = "ajax_lessonplan.php";
		$this->list_page_url = "ajax_lessonplan.php";
				
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
			
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		$this->path_upload_file = "../../lessonplan_files/";
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		$this->maxfilesize = 4*1024*1024;
								
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createNews();
				break;
			case 2:
				//--- Edit Record
				$this->editNews();
				break;
			default:
				$this->task = 0;
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
			
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->record_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->record_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->record_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->record_id = 0;
			}
		}
		
		//echo $this->stud_news_id;die("tendetid");
		
		return 1;
	}


	//==== Create New Record ====
	public function createNews()
	{
	
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editNews()
	{
		//--- Get Record Id ---
		$this->getRecordId();
         	
		if ($this->record_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		$this->arr_lessonplan_title = array();
		$this->arr_record_date = array();	
		$this->arr_lessonplan_file = array();
		$this->record_id = "";	
		$this->branch = "";	
		$this->branchname = "";	

		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{ 	
		if(isset($_POST["branch"]))
		{
			$this->branch = $_POST["branch"];
			$this->branchname = $_POST["branchname"];
		}
		
		if(isset($_POST["lessonplan_title"]))
		{
			$i = 0;
			foreach($_POST["lessonplan_title"] as $val)
			{	
				$this->arr_lessonplan_title[$i] = CommonFunctions::replaceChars($val, 0);
				$this->arr_record_date[$i] = date('Y-m-d');	
				$i++;
			}
		}

		//print_r($this->arr_news_date);exit();
		
		if($this->task == 1)
		{
			$i = 0;
			foreach($_POST["lessonplan_file_HX"] as $val)
			{
				if(!empty($val))
				{
					if($i > 0)
					{
						$this->arr_lessonplan_file[$i] = $val;
					}
				}
				$i++;
			}		
		}
		
		if($this->task == 2){
			
			$this->lessonplan_title_edit = CommonFunctions::replaceChars($_POST["lessonplan_title_edit"], 0);
			//$this->news_file = CommonFunctions::replaceChars($_POST["news_file_edit"], 0);
			
			
			$this->lessonplan_file_edit = "";
			
			
			if(isset($_POST["lessonplan_file_edit"]))
			{
				$this->lessonplan_file_edit = $_POST["lessonplan_file_edit"];			
			}	
		
		}
							
		return 1;
	}	

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";
		//$file_ext = "jpg,jpeg,pdf,doc,docx,xsl,xlsx";
		$file_ext = "pdf,doc,docx";
		
		if($this->task == 1){
			
			if($myerr == "")	
			{
				if($this->branch == "")
				{
					$myerr = "Please specify branch";
				}
			}
			
			if($myerr == "")	
			{
				for($x = 1; $x < count($this->arr_lessonplan_title); $x++)
				{
					if($this->arr_lessonplan_title[$x] == "")
					{
						$myerr = "Please specify lesson plan title -".$x;
						break;
					}
				}
			}
			
			/*if($myerr == "")	
			{
				for($j = 1; $j<count($this->arr_news_date); $j++)
				{
					if($this->arr_news_date[$j] == "")
					{
						$myerr = "Please Select Date -".$j;
						break;
					}
				}	
			}	*/			
					
			if($myerr == "")	
			{								
				for($i=1; $i<=count($this->arr_lessonplan_file); $i++)
				{			  
				   if(empty($_FILES['lessonplan_file_' . $i]['name']) && $this->task == 1)
					{
						$myerr = "Please upload file -".$i;
					}			   
					 if(!empty($_FILES['lessonplan_file_' . $i]['name']))
					 {
						if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['lessonplan_file_' . $i]['name']), $file_ext) == 0)
						{
							$myerr = "Un-supported file format , please upload files type - " . $file_ext;
						}
						else if(($_FILES['lessonplan_file_' . $i]['size'] > $this->maxfilesize))
						{
							$myerr = "File size should not be more than 4MB";
							break;
						}
					}	
				}
			}	
	    }
		
		if($this->task == 2){

			if($myerr == "")	
			{
				if($this->branch == "")
				{
					$myerr = "Please specify branch";
				}
				
				if($this->lessonplan_title_edit == "")
				{
					$myerr = "Please specify lesson plan title";
				}
			}	

			/*if($myerr == "")	
			{								
				for($i=1; $i<=count($this->arr_lessonplan_file); $i++)
				{			  
					if(empty($_FILES['lessonplan_file_' . $i]['name']) && $this->task == 1)
					{
						$myerr = "Please upload file";
					}			   
					if(!empty($_FILES['lessonplan_file_' . $i]['name']))
					{
						if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['lessonplan_file_' . $i]['name']), $file_ext) == 0)
						{
							$myerr = "Un-supported file format of photo, please upload files type - " . $file_ext;
						}
						else if(($_FILES['lessonplan_file_' . $i]['size'] > $this->maxfilesize))
						{
							$myerr = "File size should not be more than 4MB";
							break;
						}
					}	
				}
			}	*/
		}
			
		if($myerr != "")
		{
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction 
		$this->link_id->autocommit(FALSE);
			
		if($this->task == 1)
		{
			//print_r($this->arr_news_title); die('ee');
			for($i=1; $i<count($this->arr_lessonplan_title); $i++)
			{
				if(isset($_FILES["lessonplan_file_" . $i]["name"]) && $_FILES["lessonplan_file_" . $i]["name"] != "")
				{   
					$file_name="";
					$ext = strtolower(substr(strrchr($_FILES["lessonplan_file_" . $i]["name"], "."), 1));
					$name = strtolower(substr($_FILES["lessonplan_file_" . $i]["name"], 0, strpos($_FILES["lessonplan_file_" . $i]["name"], ".")));
					
					$path = $this->path_upload_file . "/lessonplan_branchid-" . $this->branch . "/" . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
					
					//$medium_path = $this->path_upload_file ."medium_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
					//$small_path = $this->path_upload_file ."thumb_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
										
					$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
										
					move_uploaded_file($_FILES["lessonplan_file_" . $i]["tmp_name"], $path);
					
					//--- Insert Data
					$tmpSql = "INSERT INTO tbl_lessonplan(record_id, branch_id, branch_name, heading, attachment, record_date)" 
							. " VALUES(null, '" . addslashes($this->branch) . "'"
										. ", '" . addslashes($this->branchname) . "'"
										. ", '" . addslashes($this->arr_lessonplan_title[$i]) . "'" 
										. ", '" . $file_name . "'"
										. ", '" . $this->arr_record_date[$i] . "')";
							
					//echo $tmpSql;die("aaa");					
									
					$rs = $this->link_id->query($tmpSql);
					
					if($this->link_id->affected_rows == -1)
					{								
						break;
					}			
				}
				else
				{
				   $tmpSql = "INSERT INTO tbl_lessonplan(record_id, branch_id, branch_name, heading, attachment, record_date)" 
							. " VALUES(null, '" . addslashes($this->branch) . "'" 
										. ", '" . addslashes($this->branchname) . "'"
										. ", '" . addslashes($this->arr_lessonplan_title[$i]) . "'" 
										. ", ''"
										. ", '" . $this->arr_record_date[$i] . "')";
										
                // echo $tmpSql; die('ss');
				  $rs = $this->link_id->query($tmpSql);	
			    }
			
			}//exit;
			
			if($myerr != "")
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();				
			}			
			else
			{
				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
			
		}
		else if($this->task == 2)
		{
			$upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "tbl_lessonplan", "attachment", "","record_id=".$this->record_id);
			$path = $this->path_upload_file . "/lessonplan_branchid-" . $this->branch . "/" ;
			
			//echo $this->path_upload_file.$upload_old_file;die();
		
			if(isset($_FILES['lessonplan_file']['name']))
			{
				if(file_exists($path.$upload_old_file))
				{
					@unlink($path.$upload_old_file);
				} 
				
				$this->file_upload();
			}
			
			$tmpSql = "UPDATE tbl_lessonplan SET " 	
					. "  heading = '" . addslashes($this->lessonplan_title_edit) ."'"
					. ",  branch_id = '" . addslashes($this->branch) ."'"				
					. ",  branch_name = '" . addslashes($this->branchname) ."'"
					//. ", record_date = '" . $this->lessonplan_date_edit ."'"
					. ", attachment = '" . $this->lessonplan_file_edit ."'"
					. "  WHERE record_id = " . $this->record_id;
					
		  // echo $tmpSql	;die("ddd");	
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();
			}	
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			echo '2';
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
		
  //Upload File
   public function file_upload()
   {
		if(isset($_FILES['lessonplan_file']['name']))
		{	
		    $file_name="";
			$ext = strtolower(substr(strrchr($_FILES["lessonplan_file"]["name"], "."), 1));
			$name = strtolower(substr($_FILES["lessonplan_file"]["name"], 0, strpos($_FILES["lessonplan_file"]["name"], ".")));
			
			
			
			$path = $this->path_upload_file . "/lessonplan_branchid-" . $this->branch . "/" . str_replace(" ", "_", $name) . "_" .strtotime("now") . "." . $ext;
					
			$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
			
			if(move_uploaded_file($_FILES['lessonplan_file']['tmp_name'], $path))
			{
				$this->lessonplan_file_edit = $file_name;
			}
			else
			{
				$myerr = "File couldn't be uploaded";
			}
		}
	}	
}

$objCurPage = new CurrentPage();
?>