<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

class CurrentPage implements AppPage
{
	public $link_id;	// Database Link
	public $module_name;	// Current Module name
	public $app_config;		// Application Configuration Settings
	
	public $email, $password; 
	public $email2, $password2; 
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module name ---
		$this->module_name = "Alumni";
		$this->cur_page_url = "ajax_alumni_login.php";
		$this->list_page_url = "ajax_alumni_login.php";
		//--- Application Configuration Settings ---
		$this->app_config = $config;

		if(!isset($_POST["postMe"]))
		{
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]))
		{
			$this->readFormData();
			
			if($this->validateFormData() == 0)
			{
				
			}
			else
			{		
			    	
				$this->doLogin();						
			}			
		}	

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	    
	public function initFormData()
	{
		$this->email="";
		$this->password="";
		$this->email2="";
		$this->password2="";
		
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{	
		if(isset($_POST["email"]))
		{   
			$this->email = $_POST["email"];	
			$this->password = CommonFunctions::replaceChars($_POST["password"], 0); 
		}
		else if(isset($_POST["email2"]))
		{
			$this->email = $_POST["email2"];	
			$this->password = CommonFunctions::replaceChars($_POST["password2"], 0); 
		}
        // print_r($_POST);die("AAA");
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = ""; 
		
	    if($myerr!="")
		{			
			echo $myerr;
			return 0;
		}
		else
		{
		return 1;
		}
	}
	
	public function doLogin()
	{	
	    $secretKey = $this->app_config['SECRET_KEY'];
		$token = $_POST['recaptcha_token'];	

		//$url = 'https://www.google.com/recaptcha/api/siteverify';
		$data = [
			'secret' => $secretKey,
			'response' => $token,
			'remoteip' => $_SERVER['REMOTE_ADDR']
		];
		
		$ch = curl_init('https://www.google.com/recaptcha/api/siteverify');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
		
		// Execute the POST request
		$response = curl_exec($ch);
		
		// Check for cURL errors
		if (curl_errno($ch)) {
			echo 'cURL error: ' . curl_error($ch);
			exit();
			return 0;
		}
		
		curl_close($ch);
		
		// Decode JSON response
		$captchaResponse = json_decode($response);
		// print_r($captchaResponse); //die();
		
		// Check if score is acceptable (adjust threshold as needed)
		if ($captchaResponse->success && $captchaResponse->score >= 0.5) {
    		$myerr = "";
    		$sql="Select * from tbl_alumni where email='".addslashes($this->email)."' and password=md5('".addslashes($this->password)."') and status=1";
            //echo $sql;die('BIJU');
    		   $rs = $this->link_id->query($sql);
    			if(!($rs)||(!($rec = $rs->fetch_array())))
    			{
    				$myerr="Invalid Username/Password";
    			}
    			else
    			{
    				$curLogonUser = new LogonUser();
    				$curLogonUser->cur_user_num_id = $rec["alumni_id"];
    				$curLogonUser->cur_user_login_name = $rec["email"];				
    				$curLogonUser->cur_user_full_name = $rec["prefix"]  . " " .$rec["first_name"] . " " .$rec["middle_name"] . " " .$rec["last_name"];
    				$curLogonUser->cur_user_first_name = $rec["first_name"];
    				$curLogonUser->cur_user_group_type = "member";
    				$curLogonUser->cur_user_group_name = "member";
    				$_SESSION["currentLogonUser"] = $curLogonUser;
    				//print_r($_SESSION["currentLogonUser"]);die('ok');
    				//--- Set Cookie for CKEditor (CKFinder)
    				setcookie("ck_authorized", "true", 0, "/");
    				echo "1";				
    				exit();	
    		
    		}
    		if($myerr != "")
    		{
    			echo $myerr;
    			return 0;
    
    		}
    		else
    		{
    			echo $rec["alumni_id"];
    			return 1;
    		}
		}
		else{
            echo "CAPTCHA failed or suspicious activity detected.";
            exit();
            return 0;
		}
    }	 
	
}

$objCurPage = new CurrentPage();

?>