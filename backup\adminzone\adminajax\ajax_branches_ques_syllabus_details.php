<?php
//print_r($_POST); die();
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
     public $link_id;          // Database Link

     public $module_name;     // Current Module Name
     public $task;               // Type of Task (1: Add, 2: Edit, 0: Nothing....)
     public $cur_page_url;     // Current Page URL
     public $list_page_url;     // List Page URL
     public $sortby;               //--- sortby: 1/2/3/4...    
     public $sortmode;          //--- sortmode: asc/desc
     public $crec;               //--- current record set position
     public $app_config;          // Application Configuration Settings

     public $branches_id;
     public $upload_file, $questionbank_file_edit, $questionbank_file_update, $syllabus_file_edit, $syllabus_file_update;
     public $path_upload_file;
     public $maxfilesize;
     public $branch;

     //=== Class Constructor ===/
     function __construct()
     {
          global $config;
          //--- Connect TO Database ---
          $this->link_id = DatabaseManager::iconnectDB();

          if (!$this->link_id) {
               die(DatabaseManager::isqlError());
          }
          $this->app_config = $config;


          //--- Set Current Module Name ---
          $this->module_name = "Ajax Question Bank And Syllabus";
          $this->cur_page_url = "ajax_branches_ques_syllabus_details.php";
          $this->list_page_url = "ajax_branches_ques_syllabus_details.php";

          //--- Check User Access permissions
          UserAccess::checkUserLogin("", "admin");

          //--- Get Sort Index and Sort Order
          $this->sortby = CommonFunctions::getSortIndex(1);
          $this->sortmode = CommonFunctions::getSortDirection("asc");

          $this->path_upload_file = "../../branches_ques_syllabus/";

          //--- Get Task Type (Add/Edit) and Current Record Id ---
          $this->getTaskType();

          $this->maxfilesize = 2 * 1024 * 1024;

          //--- Execute a Task ---
          switch ($this->task) {
               case 1:
                    //--- Add Record
                    $this->createQuestionBankSyllabus();
                    break;
               case 2:
                    //--- Edit Record
                    $this->editQuestionBankSyllabus();
                    break;
               default:
                    $this->task = 0;
                    exit();
          }
     }

     //=== Class Destructor ===/
     function __destruct()
     {
          //--- Disconnect from Database ---
          //DatabaseManager::disconnectDB($this->link_id);
     }

     //==== Get Task Type (1:Add, 2:Edit) ====
     public function getTaskType()
     {

          if (isset($_POST["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
          } else if (isset($_GET["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
          } else {
               $this->task = 0;
          }
          if ($this->task == 0) {
               $this->task = 1;
          }

          return 1;
     }


     //==== Get Current Record Id ====
     public function getRecordId()
     {
          if ($this->task == 1) {
               $this->branches_id = 0;
          } else if ($this->task == 2) {
               if (isset($_GET["recid"])) {
                    $this->branches_id = CommonFunctions::replaceChars($_GET["recid"], 0);
               } else if (isset($_POST["recid"])) {
                    $this->branches_id = CommonFunctions::replaceChars($_POST["recid"], 0);
               } else {
                    $this->branches_id = 0;
               }
          }

          //echo $this->branches_id;die("tendetid");

          return 1;
     }


     //==== Create New Record ====
     public function createQuestionBankSyllabus()
     {

          //--- Get Record Id ---
          $this->getRecordId();

          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          } else if (isset($_POST["postMe"]) && $_POST["postMe"] == "Y") {
               //--- Form is posted
               $this->readFormData();
               if ($this->validateFormData() == 0) {
                    //--- do nothing
               } else {
                    $this->saveData();
               }
          }

          return 1;
     }


     //==== Edit Existing Record ====
     public function editQuestionBankSyllabus()
     {
          //--- Get Record Id ---
          $this->getRecordId();

          if ($this->branches_id == 0) {
               $_SESSION['app_error'] = "Record not found...";
               exit();
          }

          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          } else if (isset($_POST["postMe"]) && $_POST["postMe"] == "Y") {
               //--- Form is posted
               $this->readFormData();
               if ($this->validateFormData() == 0) {
                    //--- do nothing
               } else {
                    $this->saveData();
               }
          }

          return 1;
     }

     //=== Initialize Form Data ====
     public function initFormData()
     {
          $this->branch = "";
          $this->branches_id = "";
          return 1;
     }

     //=== Read Form Data ====
     public function readFormData()
     {
          if (isset($_POST["branch"])) {
               $this->branch = CommonFunctions::replaceChars($_POST["branch"], 0);
          }
          if ($this->task == 2) {
               if (isset($_POST["questionbank_file_edit"])) {
                    $this->questionbank_file_edit = $_POST["questionbank_file_edit"];
               }
			   if (isset($_POST["syllabus_file_edit"])) {
                    $this->syllabus_file_edit = $_POST["syllabus_file_edit"];
               }
          }
		  
		  //print_r($_POST);die('aaaaa');

          return 1;
     }

     //==== Validate Form Data (Returns: 1 / 0) ====
     public function validateFormData()
     {
          $myerr = "";
          $file_ext = "pdf,doc,docx,xls,xlsx";

          if ($this->task == 1) {

               if ($myerr == "") {
                    if ($this->branch == "") {
                         $myerr = "Please select branch";
                    }
					else if(DatabaseManager::iexistsInTable($this->link_id, "tbl_branches_ques_syllabus", "branches_id <> " . $this->branches_id . " and branch = '" . $this->branch . "' ") == 1)
					{
						$myerr = "Branch: ".$this->branch. " already exists.";
					}
               }
			   
               if ($myerr == "") {
                    /*if (isset($_FILES['questionbank_file_0']['name']) == FALSE) {
                         $myerr = "Please upload question bank file";
                    }*/
                    if (!empty($_FILES['questionbank_file_0']['name'])) {
                         if (CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['questionbank_file_0']['name']), $file_ext) == 0) {
                              $myerr = "Un-supported file format , please upload files type - " . $file_ext;
                         } else if (($_FILES['questionbank_file_0']['size'] > $this->maxfilesize)) {
                              $myerr = "File size should not be more than 2MB";
                         }
                    }
               }
			   
			   if ($myerr == "") {
                    /*if (isset($_FILES['syllabus_file_0']['name']) == FALSE) {
                         $myerr = "Please upload syllabus file";
                    }*/
                    if (!empty($_FILES['syllabus_file_0']['name'])) {
                         if (CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['syllabus_file_0']['name']), $file_ext) == 0) {
                              $myerr = "Un-supported file format , please upload files type - " . $file_ext;
                         } else if (($_FILES['syllabus_file_0']['size'] > $this->maxfilesize)) {
                              $myerr = "File size should not be more than 2MB";
                         }
                    }
               }
          }

          if ($this->task == 2) {
               
			   if ($myerr == "") {
                    if ($this->branch == "") {
                         $myerr = "Please select branch";
                    }
					/*else if(DatabaseManager::iexistsInTable($this->link_id, "tbl_branches_ques_syllabus", "branches_id <> " . $this->branches_id . " and branch = '" . $this->branch . "' ") == 1)
					{
						$myerr = "Branch: ".$this->branch. " already exists.";
					}*/
               }
               if ($myerr == "") {
                    if (!empty($_FILES['questionbank_file']['name'])) {
                         if (CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['questionbank_file']['name']), $file_ext) == 0) {
                              $myerr = "Un-supported file format, please upload files type - " . $file_ext;
                         } else if (($_FILES['questionbank_file']['size'] > $this->maxfilesize)) {
                              $myerr = "File size should not be more than 2MB";
                         }
                    }
               }
			   
			   if ($myerr == "") {
                    if (!empty($_FILES['syllabus_file']['name'])) {
                         if (CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['syllabus_file']['name']), $file_ext) == 0) {
                              $myerr = "Un-supported file format, please upload files type - " . $file_ext;
                         } else if (($_FILES['syllabus_file']['size'] > $this->maxfilesize)) {
                              $myerr = "File size should not be more than 2MB";
                         }
                    }
               }
          }

          if ($myerr != "") {
               echo $myerr;
               exit();
               return 0;
          } else {
               return 1;
          }
     }

     //==== Save Data ====
     public function saveData()
     {
          $myerr = "";

          //--- Begin Transaction 
          $this->link_id->autocommit(FALSE);

          if ($this->task == 1) {
               //print_r($this->arr_news_title); die('ee');

               if (isset($_FILES["questionbank_file_0"]["name"]) && $_FILES["questionbank_file_0"]["name"] != "") {
                    $file_name = "";
                    $ext = strtolower(substr(strrchr($_FILES["questionbank_file_0"]["name"], "."), 1));
                    $name = strtolower(substr($_FILES["questionbank_file_0"]["name"], 0, strpos($_FILES["questionbank_file_0"]["name"], ".")));
                    $path = $this->path_upload_file . str_replace(" ", "_", $name) . "_quesbank_" . strtotime("now") . "." . $ext;
                    $file_name = str_replace(" ", "_", $name) . "_quesbank_" . strtotime("now") . "." . $ext;
                    move_uploaded_file($_FILES["questionbank_file_0"]["tmp_name"], $path);
				}else{
					$file_name = "";
				}
				if (isset($_FILES["syllabus_file_0"]["name"]) && $_FILES["syllabus_file_0"]["name"] != "") {
					$syllabus_file_name = "";
					$ext_syllabus = strtolower(substr(strrchr($_FILES["syllabus_file_0"]["name"], "."), 1));
					$name_syllabus = strtolower(substr($_FILES["syllabus_file_0"]["name"], 0, strpos($_FILES["syllabus_file_0"]["name"], ".")));
					$path_syllabus = $this->path_upload_file . str_replace(" ", "_", $name_syllabus) . "_syllabus_" . strtotime("now") . "." . $ext_syllabus;
					$syllabus_file_name = str_replace(" ", "_", $name_syllabus) . "_syllabus_" . strtotime("now") . "." . $ext_syllabus;
					move_uploaded_file($_FILES["syllabus_file_0"]["tmp_name"], $path_syllabus);
				}else{
					$syllabus_file_name = "";
				}
				
               //--- Insert Data
				$tmpSql = "INSERT INTO tbl_branches_ques_syllabus(branches_id, branch, ques_attachmemt, syllabus_attachmemt	)"
					 . " VALUES(null, '" . addslashes($this->branch) . "'"
					 . ", '" . $file_name . "'"
					 . ", '" . $syllabus_file_name . "')";

			   // echo $tmpSql;die('sss');					
				$rs = $this->link_id->query($tmpSql);

				if ($this->link_id->affected_rows == -1) {
					 $myerr = "error";
				}
					
               if ($myerr != "") {
                    $myerr = "Data could not be saved";
                    $this->link_id->rollback();
               } else {

                    $this->link_id->commit();
                    $_SESSION["app_message"] = "Data saved successfully";
                    echo '1';
                    exit();
               }
          } else if ($this->task == 2) {
               $upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "tbl_branches_ques_syllabus", "ques_attachmemt", "", "branches_id=" . $this->branches_id);
			   $upload_old_syllabusfile = DatabaseManager::igetDataFromTable($this->link_id, "tbl_branches_ques_syllabus", "syllabus_attachmemt	", "", "branches_id=" . $this->branches_id);
               //echo $this->path_upload_file.$upload_old_file;die();

               if (isset($_FILES['questionbank_file']['name'])) {
                    if (file_exists($this->path_upload_file . $upload_old_file)) {
                         @unlink($this->path_upload_file . $upload_old_file);
                    }
					
				   $file_name = "";
				   $ext = strtolower(substr(strrchr($_FILES["questionbank_file"]["name"], "."), 1));
				   $name = strtolower(substr($_FILES["questionbank_file"]["name"], 0, strpos($_FILES["questionbank_file"]["name"], ".")));
				   $path = $this->path_upload_file . str_replace(" ", "_", $name) . "_quesbank_" . strtotime("now") . "." . $ext;
	
				   $file_name = str_replace(" ", "_", $name) . "_quesbank_" . strtotime("now") . "." . $ext;
				   //echo $file_name;die('ddd');
				   if (move_uploaded_file($_FILES['questionbank_file']['tmp_name'], $path)) {
						//$this->questionbank_file_edit = $file_name;
						$this->questionbank_file_update = $file_name;
				   } else {
						$myerr = "File couldn't be uploaded";
				   }
               }else{
				      $this->questionbank_file_update = $this->questionbank_file_edit;
			   }
			   
			   if (isset($_FILES['syllabus_file']['name'])) {
                    if (file_exists($this->path_upload_file . $upload_old_syllabusfile)) {
                         @unlink($this->path_upload_file . $upload_old_syllabusfile);
                    }
					
				   $file_name_syllabus = "";
				   $ext_syllabus = strtolower(substr(strrchr($_FILES["syllabus_file"]["name"], "."), 1));
				   $name_syllabus = strtolower(substr($_FILES["syllabus_file"]["name"], 0, strpos($_FILES["syllabus_file"]["name"], ".")));
				   $path_syllabus = $this->path_upload_file . str_replace(" ", "_", $name_syllabus) . "_syllabus_" . strtotime("now") . "." . $ext_syllabus;
	
				   $file_name_syllabus = str_replace(" ", "_", $name_syllabus) . "_syllabus_" . strtotime("now") . "." . $ext_syllabus;
				   //echo $file_name;die('ddd');
				   if (move_uploaded_file($_FILES['syllabus_file']['tmp_name'], $path_syllabus)) {
						//$this->questionbank_file_edit = $file_name;
						$this->syllabus_file_update = $file_name_syllabus;
				   } else {
						$myerr = "File couldn't be uploaded";
				   }
               }else{
				      $this->syllabus_file_update = $this->syllabus_file_edit;
			   }
               
			  // echo $this->questionbank_file_update."+++++++++++".$this->syllabus_file_update;die('aaaaa');
               $tmpSql = "UPDATE tbl_branches_ques_syllabus SET "
                    . "  branch = '" . addslashes($this->branch) . "'"
				    . ", ques_attachmemt = '" . $this->questionbank_file_update . "'"
					. ", syllabus_attachmemt = '" . $this->syllabus_file_update . "'"
                    . "  WHERE branches_id = " . $this->branches_id;

              // echo $tmpSql;die("ddd");
               $rs = $this->link_id->query($tmpSql);

               if ($this->link_id->affected_rows == -1) {
                    $myerr = "Data could not be saved";
                    $this->link_id->rollback();
                    echo $myerr;
                    exit();
               } else {
                    $this->link_id->commit();
                    $_SESSION["app_message"] = "Data successfully updated";
                    echo '1';
                    exit();
               }
          }

          //--- In case of any error set the Session variable ---
          if ($myerr != "") {
               $_SESSION['app_error'] = $myerr;
               echo '2';
               exit();
               return 0;
          } else {
               return 1;
          }
     }

}

$objCurPage = new CurrentPage();
