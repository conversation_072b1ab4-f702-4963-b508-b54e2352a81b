<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $cat_id;
	public $cat_name;
	public $cat_photo;
	public $cat_presedence;
	public $path_upload_image;

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Add Category";
		$this->cur_page_url = "gallery_category_details.php";
		$this->list_page_url = "managephotocategory_list.php";

		//--- Check User Access permissions	
		UserAccess::checkUserLogin("", "admin");
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(2);/**/
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		$this->maxfilesize = 2*1024*1024;
		$this->path_upload_image = "../gallery_cat_images/";
		
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
		}
	}


	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Record Position (crec) passed to this page ====
	public function getRecordPostion()
	{
		/*if(isset($_POST["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_POST["crec"], 0));}
		else if(isset($_GET["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_GET["crec"], 0));}
		else
			{$this->crec=0;}
		
		return 1;*/
	}
	

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}

		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->cat_id = 0;
		}
		else if($this->task == 2)
		{
		
			if(isset($_GET["cat_id"]))
			{
				$this->cat_id = intval(CommonFunctions::replaceChars($_GET["cat_id"], 0));
			}
			else if(isset($_POST["cat_id"]))
			{
				$this->cat_id = intval(CommonFunctions::replaceChars($_POST["cat_id"], 0));
			}
			else
			{
				$this->cat_id = 0;
			}
		}
		
				
		return 1;
	}


//==== Create New Record ====
	public function createPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editPart()
	{ 
		//--- Get Record Id ---
		$this->getRecordId();
		
		if ($this->cat_id == 0)
		{
		
			$_SESSION['app_error'] = "Record not found123..";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
			
		}
		

		return 1;
	}
	
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			//--- Add Mode. Initialize field values to blank or zero or null
			$this->cat_name = "";
		}
		else if($this->task == 2)
		{
			//--- Edit Mode. Initialize fields by getting values from Database
				
			$tmpSql = "SELECT cat_id, cat_name,cat_photo,displayorder"
				. " FROM tbl_photo_category"
				. " WHERE cat_id = '" . $this->cat_id  . "'";
			
			$rs = $this->link_id->query($tmpSql);
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found...";
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
			else
			{
				$this->cat_id = $rec["cat_id"];
				$this->cat_name = stripslashes($rec["cat_name"]);
				$this->cat_photo=$rec["cat_photo"];
				$this->cat_presedence=$rec["displayorder"];
			}
			
		}
		
		return 1;
	}



	//=== Read Form Data ====
	public function readFormData()
	{
			
		return 1;
	}
	


	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
											
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}

	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
						
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
		
	}
	
}

$objCurPage = new CurrentPage();

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="images/logo.png" rel="icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script language="javascript" type="text/javascript">
function moveRec(frm, crecVal)
{
	frm.crec.value=crecVal;
}
	
function sortPage(sortidx)
{
	var frm, sortmode;
	frm = eval("document.frmEvent");
	//frm.sortby.value == sortidx && 
	if (frm.sortmode.value == "asc")
		{
		sortmode = "desc";
		}
	else
		{
		sortmode = "asc";
		}

	frm.action = "<?php echo $objCurPage->cur_page_url; ?>" + "?sortby=" + sortidx + "&sortmode=" + sortmode;
	frm.submit();
}

</script>
<script language="javascript" type="text/javascript">
$(document).ready(function(){
	
	//$("#loadingImage").hide();	
		
	$('#cmdSave').click(function()
	{	
	    //$("#loadingImage").hide();	
		validateSave();
	});
});

function validateSave()
{
	
	$("#loaderImg").html('<img src="images/progress-bar.gif" />');
	$("#loaderImg").show();
	$('#frmList').submit();
}

$('#frmList').ajaxForm({	
	target: '',
	success: function(data){
			
		$("#loadingImage").hide();
		if(data == 1)
		{
			alert("Successfull Inserted");
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			location.href='<?php echo $objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&msg=1"; ?>';
			return false;					
		}
		else
		{
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			
			$('#showError').html(data);
			
			return false;
		}
	}			
});
</script>
</head>

<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:515px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-8 maincontent" style="border:1px solid #ccc; padding:8px;">
      <h1>
        <?php if($objCurPage->task == 1) { echo "Add"; } else{ echo "Edit"; }?>
        Photo Category</h1>
      <div id="show_error">
        <?php CommonFunctions::displayErrMsg(); ?>
      </div>
      <div id="showError" style="text-align:center; color:#F00;"></div>
      <div class="col-sm-6">
        <div class="tableRow" align="center" style="margin:0 auto">
          <div class="imgLoader" id="loaderImg" style="display:none;"></div>
        </div>
        <form role="form" name="frmList" method="post" id="frmList" action="adminajax/ajax_gallery_category_details.php" enctype="multipart/form-data">
          <div class="form-group">
            <label for="cat_name">Category Name:</label>
            <input name="cat_name" type="text" class="form-control" id="cat_name" size="30" value="<?php echo ($objCurPage->cat_name);?>" tabindex="1"  style="width:331px;"/>
          </div>
          
          <!--<div class="form-group">
  			  <label for="email">precedence:</label>
              <input name="display_order" type="text" class="form-control" id="display_order" size="30" value="<?php echo ($objCurPage->cat_presedence);?>" tabindex="1"  style="width:331px;"/>
  			</div>-->
          
          <div class="form-group">
            <label for="announcement_upload">Select Photo:</label>
            <input name="announcement_upload" type="file"  id="announcement_upload" class="form-control" value="<?php echo($objCurPage->cat_photo); ?>" />
            <p style=" color:#F48212; font-size:12px; margin-top:5px;">Allowed files Only jpeg/jpg, png</p>
          </div>
          <?php if(!empty($objCurPage->cat_photo)){?>
          <div class="form-group">
            <p>Last File: <a href="<?php echo $objCurPage->path_upload_image . $objCurPage->cat_photo; ?>" target="_blank">View</a></p>
          </div>
          <?php } ?>
          <input name="cmdSave" type="button" class="btn btn-default" id="cmdSave" value="Save" />
          &nbsp;
          <input name="cmdRevert" type="button" class="btn btn-default" id="cmdRevert" value="Revert" onclick="window.location.href='<?php echo($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&cat_id=" . $objCurPage->cat_id . "&task=" . $objCurPage->task); ?>'" />
          &nbsp;
          <input name="cmdExit" type="button" class="btn btn-default" id="cmdExit" value="Exit"  onclick="window.location.href='<?php echo($objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec); ?>'">
          <input name="cat_id" type="hidden" id="cat_id" value="<?php echo $objCurPage->cat_id ?>" />
          <input name="recid" type="hidden" id="recid" value="<?php echo $objCurPage->cat_id ;?>">
          <input name="task" type="hidden" id="task" value="<?php echo $objCurPage->task ?>" />
          <input name="sortby" type="hidden" id="sortby" value="<?php echo $objCurPage->sortby ?>" />
          <input name="sortmode" type="hidden" id="sortmode" value="<?php echo $objCurPage->sortmode ?>" />
          <input name="postMe" type="hidden" id="postMe" value="Y" />
        </form>
      </div>
      <div class="col-sm-6">&nbsp;</div>
    </div>
    <div class="col-sm-1">&nbsp;</div>
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>