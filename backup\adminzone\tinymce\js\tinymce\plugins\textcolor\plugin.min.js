tinymce.PluginManager.add("textcolor",function(e){function t(){var t,n,a=[];for(n=e.settings.textcolor_map||["000000","Black","993300","Burnt orange","333300","Dark olive","003300","Dark green","003366","Dark azure","000080","Navy Blue","333399","Indigo","333333","Very dark gray","800000","Maroon","FF6600","Orange","808000","Olive","008000","Green","008080","Teal","0000FF","Blue","666699","Grayish blue","808080","Gray","FF0000","Red","FF9900","Amber","99CC00","Yellow green","339966","Sea green","33CCCC","Turquoise","3366FF","Royal blue","800080","Purple","999999","Medium gray","FF00FF","Magenta","FFCC00","Gold","FFFF00","Yellow","00FF00","Lime","00FFFF","Aqua","00CCFF","Sky blue","993366","Brown","C0C0C0","Silver","FF99CC","Pink","FFCC99","Peach","FFFF99","Light yellow","CCFFCC","Pale green","CCFFFF","Pale cyan","99CCFF","Light sky blue","CC99FF","Plum","FFFFFF","White"],t=0;t<n.length;t+=2)a.push({text:n[t+1],color:n[t]});return a}function n(){var n,a,i,r,o,l,c,s,d,u=this;for(n=t(),i='<table class="mce-grid mce-grid-border mce-colorbutton-grid" role="presentation" cellspacing="0"><tbody>',r=n.length-1,o=e.settings.textcolor_rows||5,l=e.settings.textcolor_cols||8,s=0;o>s;s++){for(i+="<tr>",c=0;l>c;c++)d=s*l+c,d>r?i+="<td></td>":(a=n[d],i+='<td><div id="'+u._id+"-"+d+'"'+' data-mce-color="'+a.color+'"'+' role="option"'+' tabIndex="-1"'+' style="'+(a?"background-color: #"+a.color:"")+'"'+' title="'+a.text+'">'+"</div>"+"</td>");i+="</tr>"}return i+="</tbody></table>"}function a(t){var n,a=this.parent();(n=t.target.getAttribute("data-mce-color"))&&(a.hidePanel(),n="#"+n,a.color(n),e.execCommand(a.settings.selectcmd,!1,n))}function i(){var t=this;t._color&&e.execCommand(t.settings.selectcmd,!1,t._color)}e.addButton("forecolor",{type:"colorbutton",tooltip:"Text color",popoverAlign:"bc-tl",selectcmd:"ForeColor",panel:{html:n,onclick:a},onclick:i}),e.addButton("backcolor",{type:"colorbutton",tooltip:"Background color",popoverAlign:"bc-tl",selectcmd:"HiliteColor",panel:{html:n,onclick:a},onclick:i})});