<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

class CurrentPage implements AppPage
{
	public $link_id;	// Database Link
	public $module_name;	// Current Module Name
	public $app_config;		// Application Configuration Settings
	public $userid;
	public $usrpwd;
	public $currentDate;
	public $user_name;
	public $user_gen;
	public $th_content;
	public $by_;
	public $th_id;
	
	public $description;
	public $title;
	public $status;
	public $event_id;
	public $task;
	public $count;
	
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
				
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		//--- Set Current Module Name ---
		$this->module_name = "Events Home";
		$this->cur_page_url = "events.php";
		$this->list_page_url = "events.php";
			
		if(empty($_SESSION["currentLogonUser"])){
			header('location:index.php');
			die();	
		}
		
		$this->task = 1;
		
		if(isset($_GET['event_id'])){
		 	
			$this->event_id = $_GET['event_id'];
			$this->task = 2;
			$this->getDescription();
		}
		
		if(isset($_GET['status'])){
			
			$this->status = intval($_GET['status']);
			$this->event_id = md5(1);
			
			if($this->status == 0){
				$this->status = 1;	
			}else if($this->status == 1){
				$this->status = 0;	
			}
			$this->updateStatus();
			
		}
		
	

		
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		DatabaseManager::idisconnectDB($this->link_id);
	}
	
	public function getDescription(){
		$this->count = 1;
		$tsql = "SELECT id, title, description, status FROM tbl_events WHERE md5(id) = '" . $this->event_id ."'";
		$rs = $this->link_id->query($tsql);
		if( (!$rs) || (!($rec = $rs->fetch_array())) ){	
		}else{
			$this->description = html_entity_decode($rec['description']);
			$this->title = stripslashes($rec['title']);
			$this->status = $rec['status'];	
		}
		
		return 1;
	}
	
	public function updateStatus(){
		
		$myerr = "";
			
		$tsql = "UPDATE tbl_events SET status = " . $this->status ." WHERE md5(id) = '" . $this->event_id . "'";
		$rs = $this->link_id->query($tsql);
		if($this->link_id->affected_rows == -1){
			$myerr = "Failed To update status";		
		}
		
		if($myerr !=""){
				$_SESSION["app_message"] = $myerr;
		}
		
		return 1;
		
	}
	

}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Welcome To SVM Admin Zone</title>
<link href="css/admin.css" rel="stylesheet" />
<script type="text/javascript" src="js/jquery-1.10.1.min.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script language="javascript" type="text/javascript">
$(document).ready(function(){
	$("#loadingImage").hide();	
	$('#cmdSave').click(function()
	{	
		$('#hdn_events').val(tinyMCE.get('events').getContent());
	    $("#loadingImage").hide();	
		validateSave();
	});
});

function validateSave()
{
	$("#loaderImg").html('<img src="images/bar-circle.gif" />');
	$("#loaderImg").show();
	$('#frmthrought').submit();
}
$('#frmthrought').ajaxForm({	
	target: '',
	success: function(data){
		$("#loadingImage").hide();
		if(data == 1)
		{	alert("Update Successfully");
			window.location="events.php";
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			return false;					
		}
		else
		{
			$("#loaderImg").empty();
			$("#loaderImg").hide();	
			$('#showError').html(data);
			return false;
		}
	}			
});
</script>
</head>
<body>
<div id="header">
	<div class="container">
  <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
   
    <!--left_menu ends here-->
    <div class="col-sm-8 maincontent">
      <div class="err_msg">
      </div>
      
      <div class="col-lg-12" style="border:1px solid #ccc; border-radius:5px;">
      	<h3>Admission & Events List</h3>
			<table width="100%">
            	<tr style="background-color:#0CC; color:#fff;">
                <th style="35">Title</th>
                <th style="max-width:40%;">Description</th>
                <th style="max-width:15%">Status</th>
                <th style="max-width:10%">TASK</th>
                
            	</tr>
                <?php 
                	$tsql = "SELECT id, title, description, status FROM tbl_events ORDER BY id ASC LIMIT 1";
					$rs =  $objCurPage->link_id->query($tsql);
					if( (!$rs) || (!($rec = $rs->fetch_array())) )
						{
							$objCurPage->count = 1;
				?>
                	 <tr>
               		 	<td colspan="4">No data in table</td>
                
               		 </tr>
                
                <?php			
							}
						else
						{
				
							
				?>
                	<tr>
                    	<td><?php echo stripslashes($rec['title']); ?></td>
                    	<td style="font-size:16px;"><?php echo stripslashes($rec['description']); ?></td>
                    	<td><a href="events.php?status=<?php echo $rec['status']; ?>"><?php if($rec['status'] == 0) { echo "Active"; }else{ echo "Deactive"; } ?></a></td>
                        <td><a href="events.php?event_id=<?php echo md5($rec['id']); ?>">Edit</a></td>
                    </tr>
                            
                 <?php            
							
						}
                ?>
                
               
                
            
            </table>
            
      </div>
      
      <form name="frmthrought" id="frmthrought" action="adminajax/ajax_events.php" method="post">
      <div class="col-lg-12" style="border:1px solid #ccc; border-radius:5px; margin-top:5px; margin-bottom:5px;">
         
      
      	<table class="tbl_reg">
        	<tr>
            	<td><div id="showError" class="showError"></div> </td>
            </tr>
        	<tr>
           		 <td><h3>Update Addmission &amp; Events</h3></td>
            </tr>
            <tr>
            	<td>
                <div class="form-group">
                	<label for="event_headline">Headline</label>
                	<input type="text" name="event_title" id="event_title" class="form-control" style="background:none; border:1px solid #ccc;"  maxlength="150" value="<?php echo $objCurPage->title; ?>" />
                </div>
                </td>
            </tr>
            <tr>
            	<td>
                <div class="form-group">
                	<label for="events">Description</label>
                	<textarea class="editor" name="events" id="events" rows="10" cols="80" ><?php echo $objCurPage->description; ?></textarea>
                </div>
                </td>
            </tr>
           <tr>
            <td>
            <?php if($objCurPage->count == 1){ ?>
            	<input type="button" value="Update" name="cmdSave" id="cmdSave" />
            <?php } ?>
            <input type="hidden" name="hdn_events" id="hdn_events" />
            <input type="hidden" name="postMe" value="Y" />
            <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />
          
            </td>
            </tr>
        </table>
      </div>
      </form>
      
      
    </div>
     <div class="col-sm-1">&nbsp;</div>
  </div>
</section>
<script type="application/javascript" src="tinymce/js/tinymce/tinymce.min.js"></script>
<script>
	tinymce.init({
    selector: "textarea.editor",
	theme: "modern",
	width:700,
	height:170,
	plugins: ["advlist autolink link image lists charmap print preview hr anchor pagebreak spellchecker",
	"searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking",
	"save table contextmenu directionality emoticons template paste textcolor jbimages"],
    toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link | media | forecolor backcolor emoticons formatselects fontselect fontsizeselect jbimages",
	 style_formats: [
        {title: 'Bold text', inline: 'b'},
        {title: 'Red text', inline: 'span', styles: {color: '#ff0000'}},
        {title: 'Red header', block: 'h1', styles: {color: '#ff0000'}},
        {title: 'Example 1', inline: 'span', classes: 'example1'},
        {title: 'Example 2', inline: 'span', classes: 'example2'},
        {title: 'Table styles'},
        {title: 'Table row 1', selector: 'tr', classes: 'tablerow1'}
    ],
	relative_urls: true	

	
 });

</script>
 <?php include_once('footer.php');?>
</body>
</html>