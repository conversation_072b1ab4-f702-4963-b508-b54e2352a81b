<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	
	public $cat_id; 
	public $cat_name;
	public $cat_photo;
	
	

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		//die("--");
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Photo Gallery View";
		$this->cur_page_url = "ajax_category_image_view.php";
		$this->list_page_url = "ajax_category_image_view.php";

		//--- Check User Access permissions	
		UserAccess::checkUserLogin("", "admin");
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getRecordId();
		
				
	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id); 
	}

//==== Get Current Record Id ====
	public function getRecordId()
	{
		if(isset($_GET["recid"]))
		{
			$this->cat_id = intval(CommonFunctions::replaceChars($_GET["recid"], 0));
		}
		else if(isset($_POST["recid"]))
		{
			$this->cat_id = intval(CommonFunctions::replaceChars($_POST["recid"], 0));
		}
		else
		{
			$this->cat_id = 0;
		}
		
		return 1;
	}	
	
}

$objCurPage = new CurrentPage();

?>

<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
<?php
$tmpSql = "SELECT cat_id,cat_name,cat_photo FROM  tbl_photo_category WHERE cat_id=" . $objCurPage->cat_id ;
		//echo $tmpSql;die();
		$rs = $objCurPage->link_id->query($tmpSql);
		
		if( (!$rs) || (!($rec = $rs->fetch_array())) )
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
/*			   $this->cat_id=$rec["cat_id"];
			   $this->cat_name=$rec["cat_name"];
			   $this->cat_photo=$rec["cat_photo"];
*/			
		

?>
<tr>
<td>
<?php CommonFunctions::displayErrMsg(); ?>
</td>
</tr>
<tr>
<td>

Category Name:&nbsp;<?php echo $rec["cat_name"]; ?>
</td>
</tr>
<tr>
<td>&nbsp;</td>
</tr>
<tr><td>
<img src='<?php echo "../gallery_cat_images/".$rec["cat_photo"]; ?>' style=" height:400px" />
</td></tr>
<?php }?>
</table>



