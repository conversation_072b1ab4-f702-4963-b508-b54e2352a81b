<?php
require_once("../includes/class.CommonFunctions.php");
require_once('../includes/class.phpmailer.php');
require_once("../config.inc.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/interface.AppPage.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
@session_start();

class CurrentPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module name
	
	public $app_config;		// Application Configuration Settings
	
	
	public $no_menu;
	public $name, $address, $email, $phone, $message, $captcha, $captcha_val;
	
	
	//=== Class Constructor ===/
	function __construct()
	{
	   
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::connectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::sqlError());
		}
		
		//--- Set Current Module name ---
		$this->module_name = "Request";
		$this->cur_page_url = "ajax_send_feedback.php";
		$this->list_page_url = "ajax_send_feedback.php";
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		$this->getTaskType();
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}
		
		if(!isset($_POST["postMe"]))
		{
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]))
		{
			
			$this->readFormData();
			
			if($this->validateFormData() == 0)
			{
				
			}
			else
			{	
				//$this->saveData();			
				$this->sendToAdmin();							
			}			
		}	

	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->tender_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->tender_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->tender_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->tender_id = 0;
			}
		}
		
		return 1;
	}
	//==== Create New Record ====
	public function createPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}
	
	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	    
	public function initFormData()
	{
		$this->name = "";
		//$this->address = "";
		$this->email = "";
		$this->phone = "";
		$this->message = "";
		$this->captcha ="";
		$this->captcha_val="";
	    
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{	 
		  
	    $this->name = CommonFunctions::replaceChars($_POST["cname"], 11);	
		//$this->address = CommonFunctions::replaceChars($_POST["address"], 11);			
		$this->email = $_POST["cmail"];
		if(isset($_POST["phone"])){
			$this->phone = 	CommonFunctions::replaceChars($_POST["phone"], 11);
		}
		$this->message = CommonFunctions::replaceChars($_POST["cmessage"], 11);
		$this->captcha_val= CommonFunctions::replaceChars($_POST["code_captcha"],11);
		$this->captcha= $_SESSION['captcha'];
							
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		if($this->captcha_val != $this->captcha){
			$myerr = "Invalid Captcha Code. Please try again";
		}
	    if($myerr!="")
		{			
			echo $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		//--- Begin Transaction
		//mysql_query("BEGIN", $this->link_id);
				
		if($this->task == 1)
		{
			//--- Insert Data			
			$tmpSql = "INSERT INTO tbl_feedback(f_name, feedbck_desc, f_email,f_phone) 
			VALUES('".$this->name.
			"','".$this->message.
			"','".$this->email.
			"','".$this->phone.
			"')";			
			
			//echo $tmpSql; die('aaaa');
			$rs = mysql_query($tmpSql, $this->link_id);
						
			if(mysql_affected_rows($this->link_id) == -1)
			{				
				$myerr = "Data could not be saved";
				//mysql_query("ROLLBACK", $this->link_id);
				echo $myerr;
				exit();
			}			
			else
			{				
				//mysql_query("COMMIT", $this->link_id);				
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	public function sendToAdmin()
	{
	   if($this->name != "")
	    {
			$message = '<table width="50%" border="0" align="center" cellpadding="2" cellspacing="1"  
style="background-color:#DFD7C0;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px">
				  <tr><td>&nbsp;</td></tr>
                  <tr><td align="center"><table width="95%" border="0" align="center" cellpadding="2" cellspacing="1"  
style="background-color:#FFF;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px" >
				  
				   <tr>
				   	<td colspan="4">Please find my contact details bellow:</td>
				   </tr>
				   <tr>
						<td colspan="4">&nbsp;</td>
				  	 </tr>				  
				  <tr>
					<td align="left" valign="top">Name</td>
					<td align="center">:</td>
					<td align="left">'.$this->name.'</td>
				  </tr>';
				 if($this->phone!="")
				 {
				   $message .='<tr>
					<td align="left" valign="top">Phone</td>
					<td align="center">:</td>
					<td align="left">'.$this->phone.'</td>
				    </tr> ';
				 } 
				 $message .='<tr>
					<td align="left" valign="top">E-Mail ID</td>
					<td align="center">:</td>
					<td align="left">'.$this->email.' </td>
				 </tr>';
				 $message .= '<tr>
					<td align="left" valign="top">Message</td>
					<td align="center">:</td>
					<td align="left">'.nl2br($this->message).'</td></tr>
				 </table>
                  </td>
                  </tr>
                   <tr><td>&nbsp;</td></tr>
                  </table>';
				  
				
				//$to = "<EMAIL>";
				$to = "<EMAIL>";
								
				$subject = "Enquiry - " . $this->name;										
				
				$mail = new PHPMailer(true); // the true param means it will throw exceptions on errors, which we need to catch
				
				
				//$mail->IsSMTP(); // telling the class to use SMTP
	
				try {
					$mail->CharSet = 'utf-8';
					$mail->SMTPDebug = false; // Enables SMTP debug information - SHOULD NOT be active on production servers!
																
					$mail->SetFrom("<EMAIL>", $this->name);
					
					$mail->AddReplyTo($this->email, $this->name);
					$mail->AddAddress($to, '');
													
					$mail->Subject = $subject;
					$mail->AltBody = 'To view the message, please use an HTML compatible email viewer!'; // optional - MsgHTML will create an alternate automatically
					$mail->MsgHTML($message);
																	
					$mail->Send();
				}			
				catch (phpmailerException $e)
				{
					echo $e->errorMessage(); //Pretty error messages from PHPMailer
					exit();
					return 0;
				} 
				catch (Exception $e)
				{
					echo $e->getMessage(); //Boring error messages from anything else!
					exit();
					return 0;
				}
				echo "1";
				exit();
	      	}
      	}	  
	}

$objCurPage = new CurrentPage();

?>