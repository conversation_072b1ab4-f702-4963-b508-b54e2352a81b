<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $app_config;
	public $record_id, $branch_id, $branch_name;
	public $news_date,$news_title,$news_details,$upload_file;
	public $file_path, $maxfilesize, $upload_file_edit,$mark_new;
	

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		//--- Set Current Module Name ---
		$this->module_name = "Lesson Plan Details";
		$this->cur_page_url = "lessonplan_details.php";
		$this->list_page_url = "lessonplan.php";
		
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		$this->file_path = "../lessonplan_files/";
						
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
			//echo($this->task); exit;	
		$this->maxfilesize = 4*1024*1024;	
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createDetails();
				break;
			case 2:
				//--- Edit Record
				$this->editDetails();
				break;
			default:
				//echo("i'm 0"); exit;
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				die("_");
		}
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		return 1;
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->record_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->record_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->record_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->record_id = 0;
			}
		}
		
		return 1;
	}


	//==== Create New Record ====
	public function createDetails()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}		
		return 1;
	}
	//==== Edit Existing Record ====
	public function editDetails()
	{
		//--- Get Record Id ---
		$this->getRecordId();
      
		if ($this->record_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			die("_");
		}
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		return 1;
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			$this->record_id = 0;	
			$this->branch_id = "";	
			$this->branch_name = "";	
			$this->news_title_arr = array();	
			$this->news_date = date('m-d-Y');
			$this->news_detail_arr = array();
			$this->upload_file_arr = array();
			$this->mark_new = 0;
					
		}
		else if($this->task == 2) 
		{
			//--- Edit Mode. Initialize fields by getting values from Database
			$tmpSql = "SELECT * "
				. " FROM tbl_lessonplan "
				. " WHERE record_id = " . $this->record_id;
										
							
			$rs = $this->link_id->query($tmpSql);
						
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found";
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
				die("_");
			}
			else
			{
				$this->record_id = $rec["record_id"];
				$this->branch_id = $rec["branch_id"];
				$this->branch_name = $rec["branch_name"];
				$this->lessonplan_title = stripslashes($rec["heading"]);	
                $this->record_date = CommonFunctions::formatMyDateTime($rec["record_date"],"m-d-Y");

				$this->upload_file = $rec["attachment"];
				$this->upload_file_edit = $this->upload_file; 			
			}
				
		}
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{	
		return 1;
	}
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";										
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";					
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>

<link rel="stylesheet" href="css/admin.css" type="text/css" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="//code.jquery.com/jquery-1.12.4.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>

<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script type="text/javascript" src="js/tcal.js"></script>
<link href="css/tcal.css" rel="stylesheet" type="text/css" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->

<script language="javascript" type="text/javascript">
$(document).ready(function(){
	$("#loadingImage").hide();	
	
	<?php if($objCurPage->task==1) {?>
		displayGridItem();
	<?php } ?>
	
	$('#cmdSaveNews').click(function(){	
	    $("#loadingImage").hide();	
		$('#frmLessonplan').submit();
		$("#loaderImg").html('<img src="images/progress-bar.gif" />');
	    $("#loaderImg").show();
	});
	
	return false;
});
</script>

<script language="javascript" type="text/javascript">

function displayGridItem()
{
	//add_item 1
	$('.addVidButton').click(function() {
		
		if($('.vidItem').size() >= 2){
			$(".addVidButton").hide();
		}
		var oval, otxt;
		$('#vidContainer').append('<div class="vidItem" style="margin-top:10px; margin-bottom:10px; padding:10px 0; border-bottom:1px solid #eeeeee;">' + $(".vidItemSample").html() + '</div>');	
			
			//this code is to set focus on first textbox	
			//========================================================	
			var new_div = $('#vidContainer .vidItem').last();
			
			if(new_div.size() > 0){
				new_div.find("input").each(function(n) {
					if(n == 0){
						$(this).focus();
					}
				});
				
				new_div.find("input[type='file']").each(function() {
					$(this).attr('name', 'lessonplan_file_' + $('#vidContainer .vidItem').size() );
					$(this).attr('idx', $('#vidContainer .vidItem').size() );
				});
				
				//this to find all a tag element
				new_div.find("a").each(function() {
					$(this).click(function(){	
						if($('#vidContainer .vidItem').size() > 1){		
							$(".addVidButton").show();
							var objVidItm = $(this);
							var curIdx = $($(this).parent().parent().parent().find("input[type='file']")[0]).attr('idx');
							curIdx = parseInt(curIdx);
							
							$(this).parent().parent().parent().parent().parent().siblings(".vidItem").each(function(){
								cfile = $(this).find("input[type='file']")[0];
								//console.log(cfile);
								anIdx = $(cfile).attr('idx');
								//alert(anIdx);
								anIdx = parseInt(anIdx);
								if(anIdx > curIdx) {
									$(cfile).attr('name', 'lessonplan_file_' + (anIdx-1) );
									$(cfile).attr('idx', (anIdx-1) );
								}
							});
										
							$(objVidItm).parent().parent().parent().parent().parent().remove();
							
						}else{
							$(this).parent().parent().find("input:text").each(function(){
								$(this).val("");
							});
						}
	
					});
				});	
			}
			//call to initialize tcal after clickind add more
			f_tcalInit();	
				
	});
	
	$(".vidItem").find("a").each(function() {

		$(this).click(function(){			
			if($('#vidContainer .vidItem').size() > 1){					
				$(this).parent().parent().parent().parent().parent().remove();
			}else{
				$(this).parent().parent().find("input:text").each(function(){
					$(this).val("");
				});
			}
		});
	});
	
	<?php
	if( $objCurPage->task == 1){
		 if(count($objCurPage->upload_file_arr) == 0 ) { ?>
			$('.addVidButton').trigger("click");  //this event trigger the click event of addVidButton
		                                       // for the first time form is loaded
	<?php }
	} 
	?>
}

function validateSave()
{
	$('#frmLessonplan').submit();
	$("#loaderImg").html('<img src="images/progress-bar.gif" />');
	$("#loaderImg").show();
}

$('#frmLessonplan').ajaxForm({	
	target: '',
	success: function(data){
		if(data){
			$("#loadingImage").hide();				
			if(data == 1)
			{
				$("#loaderImg").empty();
				$("#loaderImg").hide();
				location.href='<?php echo $objCurPage->list_page_url; ?>';
				return false;					
			}
			else
			{
			    $("#loaderImg").empty();
				$("#loaderImg").hide();
				$('#showError').html(data);
				return false;
			}
			
		}
		return false;
	}			
});

</script>

</head>
<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:520px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-9 maincontent" style="border:1px solid #ccc;">
    <!--<p>Date: <input type="text" id="datepicker"></p>-->
 			<div id="show_error"><?php CommonFunctions::displayErrMsg(); ?></div>
            <div id="showError" style="text-align:center; color:#F00;"></div>
 <form name="frmLessonplan" id="frmLessonplan" method="post" action="adminajax/ajax_lessonplan.php" enctype="multipart/form-data">     
            <table width="98%" cellpadding="4" cellspacing="0" border="0" class="dialogBaseShadow">
                <tr>
                    <td>                    
                        <table width="100%" cellpadding="0" cellspacing="0" border="0" class="dialogBase">
                            <tr>
                                <td class="dialogHeader"><h1><?php if($objCurPage->task == 1){ echo("Add New "); }else{ echo("Edit "); } ?>Lesson Plan</h1></td>
                          </tr>
                            <tr>
                                <td>
                                    <table width="100%" cellpadding="5" cellspacing="3" border="0" class="formTextWithBorder">
                                        <tr>
                                            <td align="center" id="ref_link" colspan="2">
                                            	<div class="tableRow">
                                                    <div class="imgLoader" id="loaderImg" style="display:none; margin:0 auto"></div>
                                                </div>
                                            </td>
                                        </tr> 

                                        <tr style=" border-bottom:1px solid #cccccc;">
                                            <td width="30%">Branch</td>
                                            <td width="70%">
                                                <select name="branch" id="branch" class="form-control" onchange="setBranchName(this.options[this.selectedIndex].text);">
                                                    <option value="">Select Branch</option>
                                                    <?php 
													$tsql = "SELECT dept_id, dept_name FROM tbl_department"
                                                    		. " ORDER BY dept_name ASC";
                                                    
                                                    $rs = $objCurPage->link_id->query($tsql);
                                                    
                                                    if( (!$rs) || (!($rec = $rs->fetch_array())) )
                                                    {
                                                    
                                                    }
                                                    else
                                                    {
                                                        do { 
                                                        ?>
                                                        <option value="<?php echo $rec["dept_id"]; ?>" <?php if($objCurPage->branch_id == $rec["dept_id"]) {?>selected<?php } ?>><?php echo $rec["dept_name"]; ?></option>
                                                        <?php 
                                                        }while($rec = $rs->fetch_array());
                                                    }		 
                                                    ?>
                                                </select>
                                                <input name="branchname" id="branchname" type="hidden" value="<?php echo $objCurPage->branch_name; ?>" />
                                            </td>
                                       </tr>
                        <tr>                                 
                        <td align="left" valign="middle" colspan="2">
                          
                            <?php if($objCurPage->task == 1){?>
                            
        <div id="vidContainer">
            <div>
               <div id="qvar_addmore" align="center">
               <a title="Add" class="addVidButton" style="cursor:pointer;float:right; margin-bottom:10px;">
               <img height="16" align="absmiddle" width="16" src="images/b_add.png" border="0" /> Add more
               </a>
               </div>
            </div>
                                
            <div class="vidItemSample" style="display:none; margin-bottom:10px;">
            <table width="100%" border="0" cellpadding="0" cellspacing="1">
              
            <tr valign="top"> 
                <td width="30%">Title</td>
                <td><input type="text" name="lessonplan_title[]" value="" class="form-control" /></td>
            </tr>
            
            <tr valign="top"> 
                 <td width="30%">Upload Document<br />
                <font size="1" color="#666666"> (File Types .pdf,.doc,.docx   Max Size: 4MB)</font></td>
                <td>
                <input type="file" class="form-control" name="lessonplan_file_0" value="" accept=".pdf, .doc, .docx" />
                <input type="hidden" name="lessonplan_file_HX[]" value="1" />
                </td>
            </tr>
            
            <tr valign="top"> 
             <td colspan="2" align="right">
             	<a title="Remove" class="delVidButton"><img height="16" align="absmiddle" width="16" src="images/b_drop.png" border="0" /></a>
             </td>
            </tr>
              
            </table>
            </div>     
	</div>
                            
                             <?php	
                                }
                                
                                else
                                {?>
                                    <div id="edit_lessonplan_div" style="margin-top:10px;">
                                    
                                       <table width="100%" border="0" cellpadding="0" cellspacing="1">
                                          <tr valign="top"> 
                                             <td width="30%">Title</td>
                                              <td>
                                              <input type="text" name="lessonplan_title_edit" value="<?php echo $objCurPage->lessonplan_title; ?>" class="form-control" /></td>
                                          </tr>
                                          

                                          <tr valign="top"> 
                                              <td width="30%">Upload Document<br />
                                              <font size="1" color="#666666"> (File Types .pdf,.doc,.docx   Max Size: 4MB)</font></td>
                                              <td>
                                              <input type="file" class="form-control" name="lessonplan_file" value="" accept=".pdf, .doc, .docx" />
                                              <input type="hidden" name="lessonplan_file_edit" id="lessonplan_file_edit" value="<?php echo $objCurPage->upload_file_edit;?>"  />&nbsp;&nbsp;
            <?php if($objCurPage->upload_file_edit != "") { ?>
             <i><font color="#003399"><small>(Last file: <b><a target="_blank" href="<?php echo $objCurPage->file_path .'lessonplan_branchid-'.$objCurPage->branch_id.'/'.$objCurPage->upload_file_edit ;  ?>" class="generalink">View</a></b>)</small></font></i>
            <?php } ?>
                                              </td>
                                          </tr>  
                                        </table>
                                    
                                    </div>
                                    
                               <?php } ?>
                         </td>
                        </tr>     
                                                           
                        <tr>
                          <td align="left" valign="middle" colspan="2">&nbsp;</td>
                        </tr>
                        <tr>
                          <td align="left" valign="middle" colspan="2">&nbsp;</td>
                        </tr>
                        <tr id="rowExit">
                          <td align="right" valign="middle" colspan="2">
                            <div class="imgLoader" id="loaderImg" style="display:none;"></div>
                            
                            <input type="hidden" name="recid" id="recid" value="<?php echo $objCurPage->record_id; ?>" />
                            <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />                                            
                            <input type="hidden" name="postMe" value="Y" />      
                            <input type="button" name="cmdSaveNews" id="cmdSaveNews" value="<?php if($objCurPage->task==1) {echo("Save");} else {echo("Update");} ?>" class="btn btn-default" />                                                  
                            <!--<input type="button" name="cmdSaveNews" id="cmdSaveNews" class="btn btn-default" value="Save" />-->           
                            <input type="button" name="cmdExitNews" id="cmdExitNews" class="btn btn-default" onClick="window.location.href='lessonplan.php'" value="Exit" />	
                          </td>
                        </tr>                                 
                                    </table>                        
                                </td>
                            </tr>
                        </table>
                   </td>
               </tr>
           </table>           
       </form>

     </div>
  </div>
</section>
<?php include_once('footer.php');?>
<script>
/*$("select#branch").change(function(){	
	//var selectedText = $(this).find("option:selected").text();
	//$("#branchname").val(selectedText);
});*/

function setBranchName(txt){
	$("#branchname").val(txt);
}
</script>
</body>
</html>