<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $reccnt;			//--- Number of Records to be displayed per page
	
	public $app_config;		// Application Configuration Settings
	
	public $id_list;		//--- Selected Id List

	public $video_id; 
	public $search_condition;
	
	public $video_title, $video_link; // used for searching purpose
	public $jsonheadline;
		
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Video Gallery";
		$this->cur_page_url = "video_list.php";
		$this->list_page_url = "video_list.php";
		
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(3);
		$this->sortmode = CommonFunctions::getSortDirection("desc");
		
		//--- Get Record Position (crec) passed to this page ----
		$this->reccnt = 10;
		$this->getRecordPostion();

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();

		//--- Initialize Special Properties ---
		$this->initSpecialProperties();

		//--- Generate Search Condition ---
		$this->generateSearchCondition();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 3:
				//--- Delete Record
				$this->task = 0;
				$this->deleteResults();
				break;
			/*case 4:
				//--- User Status
				$this->announcementStatus();
				break;			*/
			default:
				$this->task = 0;
		}
		
		if(isset($_POST["cmdDelete"]))
		{
			$this->deleteAllResults();
		}
		
	}


	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	//==== Get Record Position (crec) passed to this page ====
	public function getRecordPostion()
	{
		if(isset($_POST["crec"]))
		{
			$this->crec = intval(CommonFunctions::replaceChars($_POST["crec"], 0));
		}
		else if(isset($_GET["crec"]))
		{
			$this->crec = intval(CommonFunctions::replaceChars($_GET["crec"], 0));
		}
		else
		{
			$this->crec=0;
		}
			
		if(($this->crec % $this->reccnt) != 0)
		{
			$this->crec = $this->crec - ($this->crec % $this->reccnt);
		}

		if($this->crec < 0)
		{
			$this->crec=0;
		}
		
		return 1;
	}
	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		
		return 1;
	}
	
	//==== Initialize Special Properties ====
	public function initSpecialProperties()
	{
		return 1;
	}
	public function generateSearchCondition()
	{
		return 1;
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if(isset($_GET["recid"]))
		{
			$this->video_id = intval(CommonFunctions::replaceChars($_GET["recid"], 0));
		}
		else if(isset($_POST["recid"]))
		{
			$this->video_id = intval(CommonFunctions::replaceChars($_POST["recid"], 0));
		}
		else
		{
			$this->video_id = 0;
		}
		
		return 1;
	}
	//==== Edit Existing Record ====
	public function deleteResults()
	{

		//--- Get Record Id ---
		$this->getRecordId();

		if ($this->video_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}

		//---- Check if Form is submitted ----
		//if(!isset($_POST["postMe"]))
		//{
			//--- Form is not posted
			//$this->initFormData();
		//}
		//else if($_POST["postMe"] == "Y")
		//{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->deleteRecords();
			}
		//}

		return 1;
	}
	public function deleteAllResults()
	{
		
		if(isset($_POST["cmdDelete"]))
		{
			$recid="";
			$pieces= array();
			if(isset($_POST["video_ids"]))
			{
				$arr_nums=$_POST["video_ids"]; 
				$i=0;
				
				$recid = implode(",",$arr_nums); 
				$i = count($recid);             
				if($i>0)
				{
					$video_id = explode(",", $recid); 
					for($j=0; $j<count($video_id); $j++)
					{
						$tmpSql2="delete from tbl_video where video_id=".$video_id[$j];
						$rs2 = $this->link_id->query($tmpSql2);
					}
					$myerr="Record deleted successfully";
					$_SESSION["app_message"] = $myerr;					
					header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
					}
			}
			else
			{
				$_SESSION["app_error"] = "No record selected";
			}
		}
	}
	
	
	//=== Initialize Form Data ====
	public function initFormData()
	{
		//--- do nothing
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{
		//--- do nothing
		return 1;
	}
	


	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Delete Selected Records ====
	public function deleteRecords()
	{
		$myerr = "";
		$used_id_list = "";
		$used_name_list = "";
		$tmpmsg = "";
		
		//--- Begin Transaction
		$this->link_id->autocommit(FALSE);
					
		//--- Delete records which are not in use
		$tmpSql = "DELETE FROM tbl_video WHERE video_id =" . $this->video_id . "";
		$rs = $this->link_id->query($tmpSql);
		if($this->link_id->affected_rows != 1)
		{				
			//$myerr = "Sorry ! Status could not be updated ! ";
			$this->link_id->rollback();
			//echo $myerr;
			exit();
		}			
		else
		{
			$this->link_id->commit();
			$_SESSION["app_message"] = "Record deleted successfully.";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}

}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script type="text/javascript">
$(function() {
    $( "#search" ).autocomplete({
        source: 'checkdata.php'
    });
});
</script>
<script language="javascript" type="text/javascript">
function moveRec(frm, crecVal)
{
	frm.crec.value=crecVal;
}
	
function sortPage(sortidx)
{
	var frm, sortmode;
	frm = eval("document.frmEvent");
	//frm.sortby.value == sortidx && 
	if (frm.sortmode.value == "asc")
		{
		sortmode = "desc";
		}
	else
		{
		sortmode = "asc";
		}

	frm.action = "<?php echo $objCurPage->cur_page_url; ?>" + "?sortby=" + sortidx + "&sortmode=" + sortmode;
	frm.submit();
}

</script>
</head>

<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:515px;">
  <div id="row">
    <aside class="col-md-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-8 maincontent">
      <form name="frmvideoList" id="frmvideoList" method="post">
        <table width="100%" cellspacing="2" cellpadding="4">
          <tr>
            <td height="30" align="center" class="errMsgAdmin"><?php CommonFunctions::displayErrMsg(); ?></td>
          </tr>
          <tr>
            <td valign="top" align="center"><table width="100%" cellspacing="0" class="">
                <tr>
                  <td align="center" valign="top"><table width="100%" cellspacing="0" class="dialogBaseShadow">
                      <tr>
                        <td><table width="100%" cellspacing="0" class="dialogBase">
                            <tr>
                              <td class="dialogHeader"><b>Video Gallery</b> List</td>
                            </tr>
                            <tr>
                              <td><table width="100%" cellspacing="1" class="formTextWithBorder">
                                  <tr valign="top">
                                    <td  height="15" align="right"><?php 
                                            $tsql = "SELECT COUNT(*) as totrec FROM tbl_video p WHERE 1 ".$objCurPage->search_condition;
                                            
								  			$rs = $objCurPage->link_id->query(($tsql));                          
                                            if( (!$rs) || (!($rec = $rs->fetch_array())) )
                                                {
                                                
                                                $totrec=0;
                                                }
                                            else
                                                {
                                                if(empty($rec["totrec"]))
                                                    {$totrec=0;}
                                                else
                                                    {$totrec=$rec["totrec"];}
                                                }
                                            
                                            if( $objCurPage->crec >=$totrec)
                                                { 
                                                if($totrec == 0)
                                                    {$objCurPage->crec = 0;}
                                                else if($totrec % $objCurPage->reccnt == 0)
                                                    {$objCurPage->crec = $totrec - $objCurPage->reccnt;}
                                                else
                                                    {$objCurPage->crec = $totrec - ($totrec % $objCurPage->reccnt);}
                                                }
                                            if(($totrec- $objCurPage->crec )>$objCurPage->reccnt)
                                                {$pgrec=$objCurPage->reccnt;}
                                            else
                                                {$pgrec=$totrec- $objCurPage->crec ;}
                                                                            
                                            ?>
                                      <?php if( $objCurPage->crec  >= $objCurPage->reccnt) {?>
                                      <input name="imgleft2" type="image" id="imgleft22" src="images/butt_left2.gif" alt="Show First" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo 0; ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self';" />
                                      <input name="imgleft" type="image" id="imgleft" src="images/butt_left.gif" alt="Show Previous" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo  $objCurPage->crec  - $objCurPage->reccnt; ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self';" />
                                      <?php } else { ?>
                                      <img src="images/butt_left2_dis.gif" width="16" height="16" /> <img src="images/butt_left_dis.gif" width="16" height="16" />
                                      <?php } ?>
                                      &nbsp; <b>
                                      <?php if($totrec > 0)
                                                    {
                                                    if( ( $objCurPage->crec +1) != ( $objCurPage->crec +$pgrec) )
                                                        {echo("<font color='#0033FF'>" . ( $objCurPage->crec +1) . "</font>-<font color='#0033FF'>" . ( $objCurPage->crec +$pgrec) . "</font> of <font color='#CC0000'>" . $totrec) . "</font>";}
                                                    else
                                                        {echo("<font color='#0033FF'>" . ( $objCurPage->crec +1) . "</font> of <font color='#CC0000'>" . $totrec) . "</font>";}
                                                    }
                                                else
                                                    {
														echo("<font color='#990000'>0</font> ");}
                                                ?>
                                      </b>&nbsp;
                                      <?php if($totrec > ( $objCurPage->crec  + $objCurPage->reccnt)) {?>
                                      <input name="imgright" type="image" id="imgright" src="images/butt_right_dis.gif" alt="Show Next" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo  $objCurPage->crec  + $objCurPage->reccnt ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self'" />
                                      <input name="imgright2" type="image" id="imgright2" src="images/butt_right2_dis.gif" alt="Show Last" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php if($totrec % $objCurPage->reccnt != 0) {echo($totrec - ($totrec % $objCurPage->reccnt));} else {echo($totrec - $objCurPage->reccnt);} ?>');  this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self'" />
                                      <?php } else { ?>
                                      <img src="images/butt_right_dis.gif" width="16" height="16" /> <img src="images/butt_right2_dis.gif" width="16" height="16" />
                                      <?php } ?>
                                      <input name="crec" type="hidden" id="crec" value="<?php echo $objCurPage->crec; ?>" /></td>
                                  </tr>
                                  <tr>
                                    <td valign="top"><?php 
                        $color1 = "#E4E4E4";  
						$color2 = "#F1F1F1";
                       
                        $j = 0; 
                        
                        $tmpSql = "SELECT * FROM tbl_video 
								   WHERE 1 ".$objCurPage->search_condition." ORDER BY video_id DESC  
								   LIMIT " .  $objCurPage->crec  . "," . $pgrec;
						    
													                                                
                        $rs = $objCurPage->link_id->query($tmpSql);
                            
                        if( (!$rs) || (!($rec = $rs->fetch_array())) )
                        {
                            echo("<font color='#CC0000'><br>No record found...<br></FONT>");
                        }
                        else
                        {
                    ?>
                                      <table width="100%" border="0" cellpadding="0" cellspacing="1" class="tender_list">
                                        <tr valign="top" class="formHeadingBkg">
                                          <td style="width:2%"></td>
                                          <td style="text-align:left">Title</td>
                                          <td style="text-align:left">Youtube Link</td>
                                          <td >Task</td>
                                        </tr>
                                        <?php do { 
                                $bgcolorx = ($j % 2) ? $color1 : $color2;
                    ?>
                                        <tr bgcolor="<?php echo $bgcolorx; ?>">
                                          <td  valign="top" align="center"><div class="squaredThree">
                                              <input type="checkbox" value="<?php echo $rec["video_id"]; ?>" id="squaredThree<?php echo $rec["video_id"]; ?>" name="video_ids[]" />
                                              <label for="squaredThree<?php echo $rec["video_id"]; ?>"></label>
                                            </div></td>
                                          <td width="26%" valign="top" style="font-size:14px;">
										        <?php echo $rec["video_title"]; ?>
                                           </td>
                                           
                                           <td width="26%" valign="top" style="font-size:14px;">
										        <?php echo $rec["video_link"]; ?>
                                           </td>
                                          
                                          <td width="17%" align="center" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0">
                                              <tr>
                                                <td width="37%" align="center" valign="top"><a href="javascript:window.location.href='<?php echo ("video_detail.php?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["video_id"] . "&task=2"); ?>'" title="Edit Record"><img src="images/b_edit5.png" width="16" height="16" border="0" /></a></td>
                                                <td width="27%" align="center"><a href="javascript:if(window.confirm('Are You Sure?')){window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["video_id"] . "&task=3"); ?>'}" title="Delete Record"><img src="images/b_drop.png" width="16" height="16" border="0" /></a></td>
                                              </tr>
                                            </table></td>
                                        </tr>
                                        <?php 
                                        $j++; }while($rec = $rs->fetch_array());?>
                                      </table>
                                      <?php }?></td>
                                  </tr>
                                  <tr>
                                    <td valign="top" align="right"></td>
                                  </tr>
                                </table></td>
                            </tr>
                            <tr>
                              <td height="20" align="right">&nbsp;</td>
                            </tr>
                            <tr>
                              <td align="right"><input name="sortby" type="hidden" id="sortby" value="<?php echo $objCurPage->sortby; ?>" />
                                <input name="sortmode" type="hidden" id="sortmode" value="<?php echo $objCurPage->sortmode; ?>" />
                                <input name="postMe" type="hidden" id="postMe" value="Y" />
                                <input name="cmdAdd" type="button" class="btn btn-default" id="cmdAdd" value="Add" onClick="window.location.href='<?php echo ("video_detail.php?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&task=1"); ?>'" />
                                <input name="cmdDelete" type="submit" class="btn btn-default" id="cmdDelete" onClick="javascript:if(window.confirm('Are You Sure ?')){window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&task=5"); ?>'}" value="Delete">
                                <input name="cmdExit" type="button" class="btn btn-default" id="cmdExit" onClick="window.location.href='video_list.php'" value="Exit" /></td>
                            </tr>
                            <tr>
                              <td height="32" align="right">&nbsp;</td>
                            </tr>
                          </table></td>
                      </tr>
                    </table></td>
                </tr>
              </table></td>
          </tr>
        </table>
      </form>
    </div>
    <div class="col-sm-1">&nbsp;</div>
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>