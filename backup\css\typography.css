@import url(http://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300italic,400italic,500,500italic,700,700italic,900,900italic&amp;subset=latin,cyrillic-ext,latin-ext,cyrillic,greek,vietnamese,greek-ext);

@import url(http://fonts.googleapis.com/css?family=Roboto+Condensed:400,300,300italic,400italic,700,700italic&amp;subset=latin,greek,latin-ext,cyrillic,greek-ext,vietnamese,cyrillic-ext);

@import url(https://fonts.googleapis.com/css?family=Open+Sans:400,300,300italic,400italic,600,600italic,700,700italic,800,800italic&amp;subset=latin,greek,latin-ext,cyrillic,greek-ext,vietnamese,cyrillic-ext);

@import url(https://fonts.googleapis.com/css?family=Montserrat:400,700);

@import url(https://fonts.googleapis.com/css?family=PT+Serif:400,400italic,700,700italic&amp;subset=latin,cyrillic-ext,latin-ext,cyrillic);

/*
	font-family: 'Open Sans', sans-serif;
	font-family: 'Open Sans', sans-serif;
	font-family: 'PT Serif', serif;
*/

*{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	-webkit-box-sizing:border-box;
	-o-box-sizing:border-box;
	-ms-box-sizing:border-box;	
}
body {
    color: #666666;
    font-family: 'Open Sans', sans-serif;
    font-size: 13px;
    font-weight: normal;
    background-color: #ffffff;
}

/* Heading Typo */
body h1, body h2, body h4, body h5, body h6 {
    color: #042b87;
    font-family: 'Open Sans', sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    margin: 0px 0px 0px 0px;
}
 body h3{
    color: #95103e;
    font-family: 'Open Sans', sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: 1.3;
    margin: 0px 0px 15px 0px;
	/*border-bottom: 1px solid #ddd ;*/
}

h1 {
    font-size: 62px;
}

h2 {
    font-size: 55px;
}

h3 {
    font-size: 28px;
}

h4 {
    font-size: 22px;
}

h5 {
    font-size: 18px;
}

h6 {
    font-size: 14px;
}

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
    color: #3d3d3d;
}

p a {
    color: #292929;
}
/* Peragraph Typo */
p {
    /*letter-spacing: 0.2px;*/
	font-family: 'Open Sans', sans-serif;
    line-height: 24px;
	color:#0e0e0e;
	font-size:14px;
}

a {
    color: #777777;
    text-decoration: none;
	font-family: 'Open Sans', sans-serif;
}

a:hover, a:focus, a:active {
    outline: none;
    text-decoration: none;
}

ul {
   margin:0px;
   padding:0px;
}
li {
    font-family: 'Open Sans', sans-serif;
    list-style: none;
}
ul ul {
    margin:0px;
	padding:0px;
}
ol {
    float: none;
    list-style: decimal;
    padding-left: 15px;
}
ol ol {
    margin-left: 20px;
}
ol li {
	list-style: decimal;
	width: 100%;
}
figure {
	float:left;
	width:100%;
    position: relative;
	overflow:hidden;
}

.clear {
    clear: both;
    font-size: 0;
    line-height: 0;
}

img {
    max-width: 100%;
}

strong {
    color: #3d3d3d;
}

iframe {
    border: none;
    float: left;
    width: 100%;
}
/* Form Input Typo */
select {
    border: 1px solid #d2d2d2;
    color: #3d3d3d;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: 300;
    height: 40px;
    padding: 8px 12px;
    width: 100%;
}

label {
    color: #3d3d3d;
    display: block;
    font-weight: 400;
    margin-bottom: 10px;
}

button {
    border: none;
}

textarea, input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"], .uneditable-input {
    -moz-box-sizing: border-box;
  	-webkit-box-sizing: border-box;
	box-sizing: border-box;
    border: 1px solid #fff;
    display: inline-block;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    outline: none;
    vertical-align: middle;
}

form p {
    float: left;
    position: relative;
    width: 100%;
}

form p span i {
    color: #474747;
    left: 16px;
    position: absolute;
    top: 13px;
}
table {
    background-color: transparent;
    max-width: 100%;
    width: 100%;
}
th {
    text-align: left;
}
table > thead > tr > th, table > tbody > tr > th, table > tfoot > tr > th, table > thead > tr > td, table > tbody > tr > td, table > tfoot > tr > td {
    border-top: 1px solid #d2d2d2;
    border: 1px solid #d2d2d2;
    line-height: 2;
    padding-left: 7px;
    vertical-align: top;
}
table thead tr th {
    border-top: 1px solid #d2d2d2;
}
.table > caption + thead > tr:first-child > th, .table > colgroup + thead > tr:first-child > th, .table > thead:first-child > tr:first-child > th, .table > caption + thead > tr:first-child > td, .table > colgroup + thead > tr:first-child > td, .table > thead:first-child > tr:first-child > td {
	border-top: 1px solid #d2d2d2;
}
table > thead > tr > th {
    border-bottom: 2px solid #d2d2d2;
    vertical-align: bottom;
}
table > caption + thead > tr:first-child > th, table > colgroup + thead > tr:first-child > th, table > thead:first-child > tr:first-child > th, table > caption + thead > tr:first-child > td, table > colgroup + thead > tr:first-child > td, table > thead:first-child > tr:first-child > td 
{
	border-bottom: 0;
}
table > tbody + tbody {
    border-top: 2px solid #d2d2d2;
}
.table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
    padding: 12px 16px;
}
p ins {
    color: #999;
}
dl dd {
    margin-left: 20px;
}
address {
    font-style: italic;
}
::-webkit-input-placeholder {color: #a0a0a0; opacity: 1;}
:-moz-placeholder { color: #a0a0a0; opacity: 1; }
::-moz-placeholder { color: #a0a0a0; opacity: 1; }
:-ms-input-placeholder {color: #a0a0a0; opacity: 1;}

.conatct_plchldr ::-webkit-input-placeholder {color: #a0a0a0; opacity: 1;}
.conatct_plchldr :-moz-placeholder { color: #a0a0a0; opacity: 1; }
.conatct_plchldr ::-moz-placeholder { color: #a0a0a0; opacity: 1; }
.conatct_plchldr :-ms-input-placeholder {color: #a0a0a0; opacity: 1;}

.plc_hldr_404 ::-webkit-input-placeholder {color: #fff; opacity: 1;}
.plc_hldr_404 :-moz-placeholder { color: #fff; opacity: 1; }
.plc_hldr_404 ::-moz-placeholder { color: #fff; opacity: 1; }
.plc_hldr_404 :-ms-input-placeholder {color: #fff; opacity: 1;}