<?php
class UserAccess
{
	//----------------------------------------------------------------

	//=== Check User Login and User Access ===
	//--- reqd_access: required access ("01"-"99" or Blank); (Returns: 1 / 0) ---
	//--- Returns 1: success; 0: user_home; -1: user_expire ---
	static function checkUserLogin($reqd_access, $allowed_user_types = "", $will_redirect = 1)
	{
		//die('AAA');
		$chklog = 0;
		$reqd_access = strtolower($reqd_access);
		$allowed_user_types = strtolower($allowed_user_types);
	    //print_r($_SESSION);die();
		if(!isset($_SESSION["currentLogonUser"]))
		{
			/*if($allowed_user_types == "member" && isset($_COOKIE['user']) && isset($_COOKIE['pass']))
			//if($allowed_user_types == "member")
			{
				$result_cookie = UserAccess::doLoginFromCookie($_COOKIE['user'], $_COOKIE['pass']);
				//$result_cookie = UserAccess::doLoginFromCookie(md5("<EMAIL>"), md5("password"));
				if($result_cookie == 0)
				{
					$chklog = -1;	
				}
			}
			else
			{
				$chklog = -1;
			}
			*/
			
		    $chklog = -1;
		}	
		
		if($chklog != -1)
		{
			$curLogonUser = $_SESSION["currentLogonUser"];
			//print_r($curLogonUser);die('fff');
			
			if($curLogonUser->cur_user_group_type == "" || $curLogonUser->cur_user_login_name == "")
			{
				$chklog = -1;
			}
			else if($curLogonUser->cur_user_special_login_for != "" )
			{				
				$chklog = -1;
			}
			
			else 
			{							
				$chklog = UserAccess::canUserAccess($reqd_access, $allowed_user_types);
			}
			//echo $chklog ."++++++";
		}

		if($chklog == -1)
		{
			//$curLogonUser = $_SESSION["currentLogonUser"];
			//print_r($curLogonUser->cur_user_full_name);die('No');
			
			if($allowed_user_types == "admin")
			{
				if($will_redirect == 1)
				{
					header("Location: index.php");
					exit();
				}
			}
			else
			{
				if($will_redirect == 1)
				{
					header("Location: ./");
					exit();
				}
			}
		}
		else if($chklog == 0)
		{
			if($allowed_user_types == "admin")
			{
				if($will_redirect == 1)
				{
					header("Location: home.php");
					exit();
				}
			}
			else
			{
				if($will_redirect == 1)
				{
					header("Location: ./");
					exit();
				}
			}
		}
		
		return $chklog;
	}


	//=== Check User Access Permission for a feature ===
	//--- reqd_access: required access ("01"-"99" or Blank); (Returns: 1 / 0) ---
	//--- Returns 1: success; 0: access denied ---
	static function canUserAccess($reqd_access, $allowed_user_types = "")
	{
		global $config;
	
		$chklog = 0;
		$reqd_access = strtolower($reqd_access);
		$allowed_user_types = strtolower($allowed_user_types);
		
		$curLogonUser = $_SESSION["currentLogonUser"];

		if($allowed_user_types != "")
		{
			if(strpos($allowed_user_types, $curLogonUser->cur_user_group_type) === false)
			{
				$chklog = 0;
			}
			else if( (!(strpos($allowed_user_types, "admin") === false)) && $curLogonUser->cur_user_login_name == $config["SUPER_ADMIN"])
			{
				$chklog = 1;
			}
			else if($reqd_access == "")
			{
				$chklog = 1;
			}
			else if(!(strpos("," . $curLogonUser->cur_user_privileges . ",", "," . $reqd_access . ",") === false))
			{
				$chklog = 1;
			}
		}
		else
		{
			if($reqd_access == "")
			{
				$chklog = 1;
			}
			else if(!(strpos("," . $curLogonUser->cur_user_privileges . ",", "," . $reqd_access . ",") === false))
			{
				$chklog = 1;
			}
		}
		
		return $chklog;
	}

	static function doLoginFromCookie($userid, $usrpwd)
	{
		//--- Connect TO Database ---
		$tmp_link_id = DatabaseManager::iconnectDB();
		if(!$tmp_link_id)
		{
			die(DatabaseManager::isqlError());
		}
		/*$sql="Select mem_id, mem_email, mem_first_name, mem_last_name, mem_photo, mem_online_status, mem_univ_other, (select univ_name from tbl_university where  univ_id = tm.univ_id) as univ_name from tbl_member tm where md5(mem_email)='".addslashes($userid)."' and mem_password='".addslashes($usrpwd)."' and mem_status = 1";


		$rec=mysql_query($sql, $tmp_link_id);
		if(!($rec)||(!($rs=mysql_fetch_array($rec))))
		{
			return 0;
		}
		else
		{
			session_regenerate_id(true); 
			$curLogonUser = new LogonUser();
			$curLogonUser->cur_user_num_id = $rs["mem_id"];
			$curLogonUser->cur_user_login_name = $rs["mem_email"];
			$curLogonUser->cur_user_full_name = $rs["mem_first_name"] . " " . $rs["mem_last_name"];
			$curLogonUser->cur_user_photo = $rs["mem_photo"];
			$curLogonUser->cur_user_univ_other = $rs["mem_univ_other"];
			$curLogonUser->cur_user_univ_name = $rs["univ_name"];
			$curLogonUser->cur_user_group_type = "member";
			$curLogonUser->cur_user_group_name = "member";
			$curLogonUser->cur_user_online_status = $rs["mem_online_status"];
			$_SESSION["currentLogonUser"] = $curLogonUser;
			return 1;
		}		*/
	}


	//----------------------------------------------------------------
}
?>