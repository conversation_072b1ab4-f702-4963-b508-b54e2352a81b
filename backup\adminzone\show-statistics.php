<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
require_once("../php_mailer/class.phpmailer.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $app_config;		// Application Configuration Settings
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	
	public $search_condition;
	public $email, $rid;
	public $idz;
	public $mail_subject;
	public $mail_body;
	public $maxfilesize;
	public $alumni_email;
	public $alumni_id;
	public $mail_attach;
	public $mail_attach_new;
	public $saved;
	public $status;
	public $admin_mail;
	public $uploaded_document;
	public $uploaded_doc_edit;

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Alumni Mail";
		$this->cur_page_url = "alumni_mail.php";
		$this->list_page_url = "alumni.php";
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		UserAccess::checkUserLogin('', "admin");
		
		$this->maxfilesize = 1*1024*1024;
		//--- Get Record Position (crec) passed to this page ----
		$this->getRecordPostion();


		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->showStatistics();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
		}	
	}


	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	
		//==== Get Record Position (crec) passed to this page ====
	public function getRecordPostion()
	{
		if(isset($_POST["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_POST["crec"], 0));}
		else if(isset($_GET["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_GET["crec"], 0));}
		else
			{$this->crec=0;}
		
		return 1;
	}


	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		
		if(isset($_GET["rid"]))
		{
			$this->idz= $_GET["rid"];
		}
		else if(isset($_POST["idz"]))
		{
			$this->idz = $_POST["idz"];
		}
		if(isset($_GET["search_condition"]))
		{
			$this->search_condition = $_GET["search_condition"];
		}
		else if(isset($_POST["search_condition"]))
		{
			$this->search_condition= $_POST["search_condition"];
		}
		if(isset($_GET["mailall"]))
		{
			$this->mail_all = $_GET["mailall"];
		}
		
		
		$tmpSql = "SELECT * FROM tbl_alumni WHERE alumni_id in (" . $this->idz  . ")";	
		$rs = $this->link_id->query($tmpSql);
					
		if((!$rs) || (!($rec = $rs->fetch_array())))
		{
			
		}
		else
		{
			do
			{
				$this->alumni_id = $rec["alumni_id"];
				//$this->status = $rec["status"];
				//echo $this->status;die();
				if($rec["email"] != "")
				{
					if($this->alumni_email != "")
					{
						$this->alumni_email = $this->alumni_email . ",";
					}
						
						$this->alumni_email = $this->alumni_email . $rec["email"];
				}
				
			}
			while($rec = $rs->fetch_array());
		
			
		}
		
				
		return 1;
	}

	

	//==== Create New Record ====
	public function showStatistics()
	{
			
	}


	
	//=== Initialize Form Data ====
	public function initFormData()
	{
		
			//--- Add Mode. Initialize field values to blank or zero or null
			if($this->search_condition!="")
			{
				if(DatabaseManager::iexistsInTable($this->link_id, "tbl_alumni", "alumni_id != 0 ". $this->search_condition." AND email != ''")==0)
				{
					die("No Email address found.");
		
				}
			}
			else if($this->idz !="")
			{
				if(DatabaseManager::iexistsInTable($this->link_id, "tbl_alumni", "alumni_id IN (".$this->idz.") AND email != ''")==0)
				{
					die("No Email address found.");
				}
			}
			
			$this->saved = false;
			
			
			
				
		
		return 1;
	}



	//=== Read Form Data ====
	public function readFormData()
	{}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{}
	
	


		
	public function sendMail()
	{}
		//end of class
	
}

$objCurPage = new CurrentPage();

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script type="text/javascript" src="js/jquery-1.10.2.js"></script>
<script type="text/javascript" src="js/jquery-ui.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->
<link href="css/bootstrap.min.css" rel="stylesheet" />

<link href="css/colorbox.css" rel="stylesheet" />
<link href="css/popbox.css" rel="stylesheet" type="text/css" />
<link href="css/accrodiantree.css" rel="stylesheet" />
<!--<script type="text/javascript" src="../js/jquery-1.10.1.min.js"></script>-->
<script type="text/javascript" src="js/jquery-ui-1.10.2.custom.min.js"></script>
<script type="text/javascript" src="js/webwidget_vertical_menu.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<script type="text/javascript" src="js/jquery.colorbox.js"></script>

<script language="JavaScript">
<?php if($objCurPage->saved == true)
{
 ?>
 
if(window.opener)
	{
	window.opener.location.reload();
	window.close();
	}
<? } ?>

</script>
<style>
.emailBox{
	border: 1px solid #666666;
    color: #333333;
    font-size: 16px;
    height: 20px;
    outline: medium none;
    padding: 2px;
    width: 220px;
}
.msgBox{
	border: 1px solid #666666;
    color: #333333;
    font-size: 16px;
    height: 300px;
    outline: medium none;
    padding: 2px;
    width: 500px;
}
</style>
</head>

<body>
<form action="" method="post" enctype="multipart/form-data" name="form1" id="form1" onsubmit="return validateSendMail();">
  <table width="600" border="0" align="left" cellpadding="2" cellspacing="0" class="formTextWithBorder">
    <tr>
      <td><table width="100%" align="center" cellspacing="0" class="formTextWithBorder">
        <tr>
          <td><table width="100%" cellspacing="0" class="dialogBase">
            <tr>
              <td class="dialogHeader"><strong>Statistics</strong></td>
            </tr>
            <tr>
              <td align="right" valign="top" ></td>
            </tr>
            <tr>
              <td align="right"></td>
            </tr>
            <tr>
              <td valign="top">
              
              <table align="center" width="100%" cellspacing="1" class="formTextWithBorder">
              	<tr valign="top" class="formHeadingBkg">
                  <td width="7%" height="15" valign="top">Year of Passing</td>
                  <td width="12%" valign="top">Registered Members</td></tr>
                <?php 
					$tmpSql_statistics = "SELECT year_of_passing, count(alumni_id) as nosOfId FROM tbl_alumni GROUP BY year_of_passing";
					
					$rs_statistics = $objCurPage->link_id->query($tmpSql_statistics);
					if((!$rs_statistics) || (!($rec_statistics = $rs_statistics->fetch_array())))
					{}
					else
					{
						do
						{
						?>
                        <tr>
                           <td style="text-align:center"><a href="alumni.php?yop=<?php echo $rec_statistics["year_of_passing"]; ?>" ><?php echo $rec_statistics["year_of_passing"]; ?></a></td>
                           <td style="text-align:center"><?php echo $rec_statistics["nosOfId"]; ?></td>
                        </tr>										
				  <?php }
						while($rec_statistics = $rs_statistics->fetch_array());
					}
				?>
              </table>
              
              </td>
            </tr>
            
          </table></td>
        </tr>
      </table></td>
    </tr>
  </table>
</form>
</body>
</html>
