/*
	Stylesheet for Tigra Calendar v5.0
	Product is Public Domain (Free for any kind of applicaiton, customization and derivative works are allowed) 
	URL: http://www.softcomplex.com/products/tigra_calendar/

	- all image paths are relative to path of stylesheet
	- the styles below can be moved into the document or in existing stylesheet

*/

/* input box in default state */ 
.tcalInput {
	background: url('../images/cal2.gif') 100% 90% no-repeat;
	padding-right: 18px;
	cursor: pointer;
	font-family: verdana, arial, helvetica, sans-serif;
	/*font-size: 11px;*/
	color: #5c5c5c;
	/*border: 1px solid #666666;*/
	height: 40px;
	width:204px;
	z-index:999999;
}

.tcalInputServiceList {
	background: url('../images/cal2.gif') 100% 90% no-repeat;
	padding-right: 20px;
	cursor: pointer;
	font-family: verdana, arial, helvetica, sans-serif;
	font-size: 11px;
	color: #5c5c5c;
	border: 1px solid #666666;
	height: 22px;
	width:130px;
}
/* additional properties for input boxe in activated state, above still applies unless in conflict */
.tcalActive {
	/*background-image: url('../images/no_cal.gif');*/
}
/* container of calendar's pop-up */
#tcal {
	position: absolute;
	visibility: hidden;
	z-index: 99999;
	width: 222px;
	background-color: white;
	margin-top: 2px;
	padding: 0 2px 2px 2px;
	border: 1px solid silver;
	
	-moz-box-shadow: 3px 3px 4px silver;
	-webkit-box-shadow: 3px 3px 4px silver;
	box-shadow: 3px 3px 4px silver;
	-ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='silver')";
	filter: progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='silver');
}

/* table containing navigation and current month */
#tcalControls {
	border-collapse: collapse;
	border: 0;
	width: 100%;
}
#tcalControls td {
	border-collapse: collapse;
	border: 0;
	padding: 0;
	width: 16px;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	cursor: pointer;
}
#tcalControls th {
	border-collapse: collapse;
	border: 0;
	padding: 0;
	line-height: 45px;
	font-size: 14px;
	text-align: center;
	font-family: Tahoma, Geneva, sans-serif;
	font-weight: bold;
	white-space: nowrap;
}
#tcalPrevYear { background-image: url('../images/prev_year.gif'); }
#tcalPrevMonth { background-image: url('../images/prev_mon.gif'); }
#tcalNextMonth { background-image: url('../images/next_mon.gif'); }
#tcalNextYear { background-image: url('../images/next_year.gif'); }

/* table containing week days header and calendar grid */
#tcalGrid {
	border-collapse: collapse;
	border: 1px solid silver;
	width: 100%;
}
#tcalGrid th {
	border: 1px solid silver;
	border-collapse: collapse;
	padding: 3px 0;
	text-align: center;
	font-family: Tahoma, Geneva, sans-serif;
	font-size: 10px;
	background-color: gray;
	color: white;
}
#tcalGrid td {
	border: 0;
	border-collapse: collapse;
	padding: 2px 0;
	text-align: center;
	font-family: Tahoma, Geneva, sans-serif;
	width: 14%;
	font-size: 11px;
	cursor: pointer;
}		
#tcalGrid td.tcalOtherMonth { color: silver; }
/*#tcalGrid td.tcalWeekend { background-color: #ACD6F5; }*/
#tcalGrid td.tcalWeekend { background-color: #C69451; }
#tcalGrid td.tcalToday { border: 1px solid red; }
#tcalGrid td.tcalSelected { background-color: #FFB3BE; }