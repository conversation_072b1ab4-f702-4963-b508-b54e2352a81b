<?php
//print_r($_POST); die();
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
     public $link_id;          // Database Link

     public $module_name;     // Current Module Name
     public $task;               // Type of Task (1: Add, 2: Edit, 0: Nothing....)
     public $cur_page_url;     // Current Page URL
     public $list_page_url;     // List Page URL
     public $sortby;               //--- sortby: 1/2/3/4...    
     public $sortmode;          //--- sortmode: asc/desc
     public $crec;               //--- current record set position
     public $app_config;          // Application Configuration Settings

     public $placement_notice_id;
     public $upload_file, $placement_notice_file_update;
     public $path_upload_file;
     public $maxfilesize;
     public $title, $weblink, $details, $placement_notice_date;

     //=== Class Constructor ===/
     function __construct()
     {
          global $config;
          //--- Connect TO Database ---
          $this->link_id = DatabaseManager::iconnectDB();

          if (!$this->link_id) {
               die(DatabaseManager::isqlError());
          }
          $this->app_config = $config;


          //--- Set Current Module Name ---
          $this->module_name = "Ajax Placement Notice";
          $this->cur_page_url = "ajax_placement_notice_details.php";
          $this->list_page_url = "ajax_placement_notice_details.php";

          //--- Check User Access permissions
          UserAccess::checkUserLogin("", "admin");

          //--- Get Sort Index and Sort Order
          $this->sortby = CommonFunctions::getSortIndex(1);
          $this->sortmode = CommonFunctions::getSortDirection("asc");

          $this->path_upload_file = "../../placement_files/placement_notice/";

          //--- Get Task Type (Add/Edit) and Current Record Id ---
          $this->getTaskType();

          $this->maxfilesize = 4 * 1024 * 1024;

          //--- Execute a Task ---
          switch ($this->task) {
               case 1:
                    //--- Add Record
                    $this->createDeptNewsletter();
                    break;
               case 2:
                    //--- Edit Record
                    $this->editDeptNewsletter();
                    break;
               default:
                    $this->task = 0;
                    exit();
          }
     }

     //=== Class Destructor ===/
     function __destruct()
     {
          //--- Disconnect from Database ---
          //DatabaseManager::disconnectDB($this->link_id);
     }

     //==== Get Task Type (1:Add, 2:Edit) ====
     public function getTaskType()
     {

          if (isset($_POST["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
          } else if (isset($_GET["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
          } else {
               $this->task = 0;
          }
          if ($this->task == 0) {
               $this->task = 1;
          }

          return 1;
     }


     //==== Get Current Record Id ====
     public function getRecordId()
     {
          if ($this->task == 1) {
               $this->placement_notice_id = 0;
          } else if ($this->task == 2) {
               if (isset($_GET["recid"])) {
                    $this->placement_notice_id = CommonFunctions::replaceChars($_GET["recid"], 0);
               } else if (isset($_POST["recid"])) {
                    $this->placement_notice_id = CommonFunctions::replaceChars($_POST["recid"], 0);
               } else {
                    $this->placement_notice_id = 0;
               }
          }

          //echo $this->placement_notice_id;die("tendetid");

          return 1;
     }


     //==== Create New Record ====
     public function createDeptNewsletter()
     {

          //--- Get Record Id ---
          $this->getRecordId();

          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          } else if (isset($_POST["postMe"]) && $_POST["postMe"] == "Y") {
               //--- Form is posted
               $this->readFormData();
               if ($this->validateFormData() == 0) {
                    //--- do nothing
               } else {
                    $this->saveData();
               }
          }

          return 1;
     }


     //==== Edit Existing Record ====
     public function editDeptNewsletter()
     {
          //--- Get Record Id ---
          $this->getRecordId();

          if ($this->placement_notice_id == 0) {
               $_SESSION['app_error'] = "Record not found...";
               exit();
          }

          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          } else if (isset($_POST["postMe"]) && $_POST["postMe"] == "Y") {
               //--- Form is posted
               $this->readFormData();
               if ($this->validateFormData() == 0) {
                    //--- do nothing
               } else {
                    $this->saveData();
               }
          }

          return 1;
     }

     //=== Initialize Form Data ====
     public function initFormData()
     {
          $this->title = "";
		  $this->details = "";
		  $this->weblink = "";
          $this->placement_notice_date = "";
          $this->placement_notice_id = "";
          return 1;
     }

     //=== Read Form Data ====
     public function readFormData()
     {
		  if (isset($_POST["placement_notice_date"])) {
			   $mdate = explode("-",$_POST["placement_notice_date"]);
			   $this->placement_notice_date = $mdate[2]."-".$mdate[0]."-".$mdate[1];
          }
          if (isset($_POST["title"])) {
               $this->title = CommonFunctions::replaceChars($_POST["title"], 0);
          }
		  if (isset($_POST["details"])) {
               $this->details = CommonFunctions::replaceChars($_POST["details"], 0);
          }
		  if (isset($_POST["weblink"])) {
               $this->weblink = CommonFunctions::replaceChars($_POST["weblink"], 0);
          }
          if ($this->task == 2) {
			   //$this->title_edit = CommonFunctions::replaceChars($_POST["title"], 0);
			   //$this->details_edit = CommonFunctions::replaceChars($_POST["details"], 0);
               if (isset($_POST["placement_notice_file_edit"])) {
                    $this->placement_notice_file_edit = $_POST["placement_notice_file_edit"];
               }
          }

          return 1;
     }

     //==== Validate Form Data (Returns: 1 / 0) ====
     public function validateFormData()
     {
          $myerr = "";
          $file_ext = "pdf,doc,docx,xls,xlsx";

          if ($this->task == 1) {
               
			   if ($myerr == "") {
                    if ($this->placement_notice_date == "") {
                         $myerr = "Please specify date";
                    }
               }
               if ($myerr == "") {
                    if ($this->title == "") {
                         $myerr = "Please specify title";
                    }
               }
               if ($myerr == "") {
                    /*if (isset($_FILES['placement_notice_file_0']['name']) == FALSE) {
                         $myerr = "Please upload file";
                    }*/
                    if (!empty($_FILES['placement_notice_file_0']['name'])) {
                         if (CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['placement_notice_file_0']['name']), $file_ext) == 0) {
                              $myerr = "Un-supported file format , please upload files type - " . $file_ext;
                         } else if (($_FILES['placement_notice_file_0']['size'] > $this->maxfilesize)) {
                              $myerr = "File size should not be more than 4MB";
                         }
                    }
               }
          }

          if ($this->task == 2) {
               
			   if ($myerr == "") {
                    if ($this->placement_notice_date == "") {
                         $myerr = "Please specify date";
                    }
               }
			   if ($myerr == "") {
                    if ($this->title == "") {
                         $myerr = "Please specify title";
                    }
               }
               if ($myerr == "") {
                    if (!empty($_FILES['placement_notice_file']['name'])) {
                         if (CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['placement_notice_file']['name']), $file_ext) == 0) {
                              $myerr = "Un-supported file format, please upload files type - " . $file_ext;
                         } else if (($_FILES['placement_notice_file']['size'] > $this->maxfilesize)) {
                              $myerr = "File size should not be more than 4MB";
                         }
                    }
               }
          }

          if ($myerr != "") {
               echo $myerr;
               exit();
               return 0;
          } else {
               return 1;
          }
     }

     //==== Save Data ====
     public function saveData()
     {
          $myerr = "";

          //--- Begin Transaction 
          $this->link_id->autocommit(FALSE);

          if ($this->task == 1) {
               //print_r($this->arr_news_title); die('ee');

               if(isset($_FILES["placement_notice_file_0"]["name"]) && $_FILES["placement_notice_file_0"]["name"] != "") {
                    $file_name = "";
                    $ext = strtolower(substr(strrchr($_FILES["placement_notice_file_0"]["name"], "."), 1));
                    $name = strtolower(substr($_FILES["placement_notice_file_0"]["name"], 0, strpos($_FILES["placement_notice_file_0"]["name"], ".")));

                    $path = $this->path_upload_file . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
                    $file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
                    move_uploaded_file($_FILES["placement_notice_file_0"]["tmp_name"], $path);
               }else{
				    $file_name = "";
			   }
               
			   //--- Insert Data
				$tmpSql = "INSERT INTO tbl_placement_notice(placement_notice_id, title, details, weblink, placement_notice_date, attachmemt)"
					 . " VALUES(null, '" . addslashes($this->title) . "'"
					 . ", '" . addslashes($this->details) . "'"
					 . ", '" . addslashes($this->weblink) . "'"
					 . ", '" . $this->placement_notice_date . "'"
					 . ", '" . $file_name . "')";

				//echo $tmpSql;die('sss');					
				$rs = $this->link_id->query($tmpSql);

				if ($this->link_id->affected_rows == -1) {
					 $myerr = "Data could not be saved";
                     $this->link_id->rollback();
				}else {
                    $this->link_id->commit();
                    $_SESSION["app_message"] = "Data saved successfully";
                    echo '1';
                    exit();
               }
          } else if ($this->task == 2) {
               $upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "tbl_placement_notice", "attachmemt", "", "placement_notice_id=" . $this->placement_notice_id);
               //echo $this->path_upload_file.$upload_old_file;die();

               if (isset($_FILES['placement_notice_file']['name'])) {
                    if (file_exists($this->path_upload_file . $upload_old_file)) {
                         @unlink($this->path_upload_file . $upload_old_file);
                    }

                    //$this->file_upload();
				   $file_name = "";
				   $ext = strtolower(substr(strrchr($_FILES["placement_notice_file"]["name"], "."), 1));
				   $name = strtolower(substr($_FILES["placement_notice_file"]["name"], 0, strpos($_FILES["placement_notice_file"]["name"], ".")));
				   $path = $this->path_upload_file . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
	
				   $file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
	
				   if (move_uploaded_file($_FILES['placement_notice_file']['tmp_name'], $path)) {
						$this->placement_notice_file_update = $file_name;
				   } else {
						$myerr = "File couldn't be uploaded";
				   }
				  //echo $this->placement_notice_file_edit;die('hi');
               }

               $tmpSql = "UPDATE tbl_placement_notice SET "
                    . "  title = '" . addslashes($this->title) . "'"
					. ", details = '" . addslashes($this->details) . "'"
					. ", weblink = '" . addslashes($this->weblink) . "'"
                    . ", placement_notice_date = '" . $this->placement_notice_date . "'"
                    //. ", attachmemt = '" . $this->placement_notice_file_edit . "'"
					. ", attachmemt = '" . $this->placement_notice_file_update . "'"
                    . "  WHERE placement_notice_id = " . $this->placement_notice_id;

                //echo $tmpSql;die("ddd");
               $rs = $this->link_id->query($tmpSql);

               if ($this->link_id->affected_rows == -1) {
                    $myerr = "Data could not be updated";
                    $this->link_id->rollback();
                    echo $myerr;
                    exit();
               } else {
                    $this->link_id->commit();
                    $_SESSION["app_message"] = "Data successfully updated";
                    echo '1';
                    exit();
               }
          }

          //--- In case of any error set the Session variable ---
          if ($myerr != "") {
               $_SESSION['app_error'] = $myerr;
               echo '2';
               exit();
               return 0;
          } else {
               return 1;
          }
     }

     //Upload File
     public function file_upload()
     {
          if (isset($_FILES['placement_notice_file']['name'])) {
               //echo"aaa";die();
               $file_name = "";
               $ext = strtolower(substr(strrchr($_FILES["placement_notice_file"]["name"], "."), 1));
               $name = strtolower(substr($_FILES["placement_notice_file"]["name"], 0, strpos($_FILES["placement_notice_file"]["name"], ".")));
               $path = $this->path_upload_file . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;

               $file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;

               if (move_uploaded_file($_FILES['placement_notice_file']['tmp_name'], $path)) {
                    $this->placement_notice_file_edit = $file_name;
               } else {
                    $myerr = "File couldn't be uploaded";
               }
          }
     }
}

$objCurPage = new CurrentPage();
