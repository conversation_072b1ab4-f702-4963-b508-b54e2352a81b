<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */

class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $hob_list;
		
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "News";
		$this->cur_page_url = "news.php";

		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");		
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
				//--- Get Task Type (Add/Edit) and Current Record Id ---
	}

}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Welcome To SVM Admin Zone</title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script type="text/javascript">
$(function() {
    $( "#search" ).autocomplete({
        source: 'checkdata.php'
    });
});
</script>
<script language="javascript">

</script>
</head>

<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
</div>
</div>
<section class="container">
  <div id="row">
    <aside id="left_menu" class="col-md-4 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    
    <div id="right_menu" class="col-md-8 maincontent">
      <form method="post" name="frmList" id="frmList" action="adminajax/news.php">
        <table width="100%" cellspacing="0" class="dialogBase tbl_reg ">
          <?php if(!empty($_SESSION["app_message"]) || !empty($_SESSION["app_error"])){ ?>
          <tr>
            <td height="30" align="center" colspan="2"><?php CommonFunctions::displayErrMsg(); ?></td>
          </tr>
          <?php } ?>
          <tr>
          <tr>
            <td class="dialogHeader" style="text-align:center" colspan="5"><h3>Manage Gallery</h3></td>
          </tr>
          <tr>
            <th>Heading</th><th>Details</th><th>Gallery Date</th><th>Catefiry</th><th>Status</th><th>Option</th>
          </tr>
          <tr>
            <td>Heading 1</td>
            <td>Demo content</td>
            <td>12-03-2016</td>
            <td>Category</td>
            <td>Active</td>
            <td><a href="#">Add</a> <a href="#">Edit</a> <a href="#">Delete</a></td>
          </tr>
          <tr>
            <td>Heading 1</td>
            <td>Demo content</td>
            <td>12-03-2016</td>
            <td>Category</td>
            <td>Active</td>
            <td><a href="#">Add</a> <a href="#">Edit</a> <a href="#">Delete</a></td>
          </tr>
          <tr>
            <td>Heading 1</td>
            <td>Demo content</td>
            <td>12-03-2016</td>
            <td>Category</td>
            <td>Active</td>
            <td><a href="#">Add</a> <a href="#">Edit</a> <a href="#">Delete</a></td>
          </tr>
          <tr>
            <td>Heading 1</td>
            <td>Demo content</td>
            <td>12-03-2016</td>
            <td>Category</td>
            <td>Active</td>
            <td><a href="#">Add</a> <a href="#">Edit</a> <a href="#">Delete</a></td>
          </tr>
          <tr>
            <td>Heading 1</td>
            <td>Demo content</td>
            <td>12-03-2016</td>
            <td>Category</td>
            <td>Active</td>
            <td><a href="#">Add</a> <a href="#">Edit</a> <a href="#">Delete</a></td>
          </tr>
          <tr>
            <td>Heading 1</td>
            <td>Demo content</td>
            <td>12-03-2016</td>
            <td>Category</td>
            <td>Active</td>
            <td><a href="#">Add</a> <a href="#">Edit</a> <a href="#">Delete</a></td>
          </tr>
          <tr>
            <td>Heading 1</td>
            <td>Demo content</td>
            <td>12-03-2016</td>
            <td>Category</td>
            <td>Active</td>
            <td><a href="#">Add</a> <a href="#">Edit</a> <a href="#">Delete</a></td>
          </tr>
        </table>
      </form>
    </div>
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>