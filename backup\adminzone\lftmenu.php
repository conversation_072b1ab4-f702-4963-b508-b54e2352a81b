<style>
    .active {
        background: #01176a;
    }

    .active a {
        color: #fff
    }

    .admin_leftpanel li:before {
        /*content:"\00bb";*/
        padding-right: 2px
    }
	
	.isDisabled {
	  cursor: not-allowed;
	  opacity: 0.5;
	  pointer-events: none;
	}
</style>
<?php if (!isset($objCurPage->no_menu) || $objCurPage->no_menu != "Y") {
    $pgurl = $_SERVER['REQUEST_URI'];
    //echo $pgurl;die("aaa");
	$anchor_disable = '';
	$tmpSql = "SELECT user_id, user_name, user_email, user_gender, user_password, user_date from admin_details WHERE user_name = '" . $_SESSION["currentLogonUser"]->cur_user_login_name  . "'";
	$rs = $objCurPage->link_id->query($tmpSql);
	if( (!$rs) || (!($rec = $rs->fetch_array())) )
	{}
	else
	{
		$objCurPage->user_date = $rec["user_date"];
		if(!$objCurPage->user_date="")
		{
			$tmpsql_exp="SELECT * from admin_details WHERE user_date <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
			//echo $tmpsql_exp; die("AAAAAAAAAAAAAAAAAA");
			$rs_exp = $objCurPage->link_id->query($tmpsql_exp);
			if( (!$rs_exp) || (!($rec_exp = $rs_exp->fetch_array())) )
			{}
			else
			{
				//$_SESSION['app_error'] = "Your Password has not been changed since last 1 month. <br/>Kindly change your password.";
				//echo $_SESSION['app_error']; die("_");
				$anchor_disable = "isDisabled";
			}
		}
							
	}
?>
    <div id="webwidget_vertical_menu" class="webwidget_vertical_menu">
        <ul class="admin_leftpanel">
            <li <?php if (strstr($pgurl, "news.php") == "news.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="news.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-newspaper-o" aria-hidden="true"></i> Manage General Announcement</a></li>

            <li <?php if (strstr($pgurl, "stud_announcement.php") == "stud_announcement.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="stud_announcement.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-newspaper-o" aria-hidden="true"></i>
                    Manage Student Announcement</a></li>

            <li <?php if (strstr($pgurl, "tender.php") == "tender.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="tender.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-newspaper-o" aria-hidden="true"></i> Manage Tenders</a></li>

            <li <?php if (strstr($pgurl, "managephotocategory_list.php") == "managephotocategory_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="managephotocategory_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-camera-retro" aria-hidden="true"></i> Manage Photo Category</a></li>

            <li <?php if (strstr($pgurl, "managephotogallery_list.php") == "managephotogallery_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="managephotogallery_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-camera" aria-hidden="true"></i> Manage Photo Gallery</a> </li>

            <li <?php if (strstr($pgurl, "video_list.php") == "video_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="video_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-camera" aria-hidden="true"></i> Manage Video Gallery</a> </li>

<li <?php if (strstr($pgurl, "lessonplan.php") == "lessonplan.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="lessonplan.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-file-pdf-o" aria-hidden="true"></i> Manage Lesson Plan</a>
</li>


            <li <?php if (strstr($pgurl, "pdflecture_list.php") == "pdflecture_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="pdflecture_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-file-pdf-o" aria-hidden="true"></i> Manage Lecture Notes</a> </li>

            <li <?php if (strstr($pgurl, "videolecture_list.php") == "videolecture_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="videolecture_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-video-camera" aria-hidden="true"></i> Manage Video Lecture</a> </li>

            <li <?php if (strstr($pgurl, "timetable_list.php") == "timetable_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="timetable_list.php" class="<?php echo $anchor_disable;?>"><i class="fa  fa-file-pdf-o" aria-hidden="true"></i> Manage Time Table</a> </li><i class="fa-solid fa-newspaper"></i>


 <li <?php if (strstr($pgurl, "newsletter_list.php") == "newsletter_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="newsletter_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-newspaper-o" aria-hidden="true"></i> Manage Newsletter</a> </li>

            <li <?php if (strstr($pgurl, "mentorship_list.php") == "mentorship_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="mentorship_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-file-pdf-o" aria-hidden="true"></i> Manage Mentorship</a> </li>


           <?php /*?> <li <?php if(strstr($pgurl,"department_list.php") == "department_list.php") { echo 'class="active"' ;} else { echo 'class=""';} ?> class="active"><a href="department_list.php" ><i class="fa fa-building" aria-hidden="true"></i> Manage Department</a></li>
          <li <?php if(strstr($pgurl,"faculty_list.php") == "faculty_list.php") { echo 'class="active"' ;} else { echo 'class=""';} ?> class="active"><a href="faculty_list.php" ><i class="fa fa-user" aria-hidden="true"></i> Manage Faculty</a></li><?php */?>
          
            <li <?php if (strstr($pgurl, "alumni.php") == "alumni.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="alumni.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-users" aria-hidden="true"></i> Manage Alumni</a></li>
                
            <li <?php if (strstr($pgurl, "placement_notice_list.php") == "placement_notice_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="placement_notice_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-institution" aria-hidden="true"></i> Manage Placement Notice</a></li>   
                
            <li <?php if (strstr($pgurl, "placement_details_list.php") == "placement_details_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="placement_details_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-institution" aria-hidden="true"></i> Manage Placement Details</a></li>
           <li <?php if (strstr($pgurl, "mandatory_disclosure_list.php") == "mandatory_disclosure_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="mandatory_disclosure_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-institution" aria-hidden="true"></i> Manage Mandatory Disclosure</a></li>       
                
            <li <?php if (strstr($pgurl, "branches_ques_syllabus_list.php") == "branches_ques_syllabus_list.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="branches_ques_syllabus_list.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-institution" aria-hidden="true"></i> Manage Academics Branches Question Bank And Syllabus</a></li>    
                
            <li <?php if (strstr($pgurl, "manage-account.php") == "manage-account.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="manage-account.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-user" aria-hidden="true"></i> Manage Account</a></li>

            <li <?php if (strstr($pgurl, "change-password.php") == "change-password.php") {
                    echo 'class="active"';
                } else {
                    echo 'class=""';
                } ?> class="active"><a href="change-password.php"><i class="fa fa-unlock-alt" aria-hidden="true"></i> Change Password</a></li>

            <li><a href="logout.php" class="<?php echo $anchor_disable;?>"><i class="fa fa-sign-out" aria-hidden="true"></i> Logout</a></li>
        </ul>
        <div style="clear: both"></div>
    </div>
<?php } else { ?>
<?php } ?>