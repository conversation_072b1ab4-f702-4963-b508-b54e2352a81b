
@import url("//fonts.googleapis.com/css?family=Open+Sans");
@font-face {
  font-family: 'good times';
  src: url('../fonts/good times rg.ttf');
  src: url('../fonts/good times rg.ttf'), 
  font-weight: normal;
  font-style: normal;
}
body,div, ul, h1, h2, h3, h4, h5, li, p, img, form, input, textarea {
    margin: 0;
    padding: 0;
	/*font-family:"Open Sans";*/
	font-family:Georgia, "Times New Roman", Times, serif !important;
}
#header {border:1px solid #ccc; text-align:center; background:#01176a; padding:5px 0}
body{
	background:#f9f9f9;
	font-family:Verdana, Geneva, sans-serif;
	color:#868585;
}
h1,h2,h3,h4{color:#333;}
#mid_container{margin: 3% auto;width: 90%;min-height: 62vh;}
td{vertical-align:middle}
.admin_leftpanel li{list-style:none; border:1px solid #ccc; padding:3% 2%; margin:1% 2%	}
.admin_leftpanel li ul li{padding-left:4%; margin-left:10%}
.admin_leftpanel a{text-decoration:none;color:#7c7c7c}
.admin_leftpanel a:hover{text-decoration:underline}
#right_menu{
	border:1px solid #ccc; 
	box-shadow: 3px 3px 5px #777;
	float:right;
	margin-bottom:1%;
	min-height: 560px;}
#left_menu{box-shadow: 3px 3px 5px #777;}
.tbl_reg{width:100%; padding:5px; overflow:hidden}

.tbl_reg input[type="text"],.tbl_reg input[type="password"],.tbl_reg input[type="date"],
.tbl_reg input[type="datetime"],.tbl_reg input[type="email"],.tbl_reg input[type="number"],
.tbl_reg input[type="search"],.tbl_reg input[type="tel"],.tbl_reg input[type="time"], 
.tbl_reg input[type="url"], .tbl_reg textarea, .tbl_reg select {
  background: rgba(255,255,255,0.1);
  border: none;
  font-size: 16px;
  height: auto;
  outline: 0;
  padding: 15px;
  width: 100%;
  background-color: #e8eeef;
  color: #a3a1a1;
  box-shadow: 0 1px 0 rgba(0,0,0,0.03) inset;
  margin-bottom: 10px;
}


input[type="radio"],
input[type="checkbox"] {
  margin: 0 4px 8px 0;
}
.tbl_reg input[type="button"],
button {
  padding: 10px 25px;
  color: #FFF;
  background-color: #626262;
  font-size: 18px;
  text-align: center;
  font-style: normal;
  border-radius: 5px;
  border: 1px solid #64e3e8;
  border-width: 1px 1px 3px;
  box-shadow: 0 -1px 0 rgba(255,255,255,0.1) inset;
  margin-bottom: 10px;
  cursor:pointer;
}
.tbl_reg .search{
	padding: 10px 6px !important;
}

.hobbies 
{	width:50%; 
	float:left;
}
input[type=checkbox] {
	visibility: hidden;
}

/* SQUARED THREE */
.squaredThree {	
	margin: 20px auto;
	position: relative;
}

.squaredThree label {
	cursor: pointer;
	position: absolute;
	width: 20px;
	height: 20px;
	top: 0;
	border-radius: 4px;

	-webkit-box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,.4);
	-moz-box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,.4);
	box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,.4);

	background: -webkit-linear-gradient(top, #222 0%, #45484d 100%);
	background: -moz-linear-gradient(top, #222 0%, #45484d 100%);
	background: -o-linear-gradient(top, #222 0%, #45484d 100%);
	background: -ms-linear-gradient(top, #222 0%, #45484d 100%);
	background: linear-gradient(top, #222 0%, #45484d 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#222', endColorstr='#45484d',GradientType=0 );
}

.squaredThree label:after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	opacity: 0;
	content: '';
	position: absolute;
	width: 9px;
	height: 5px;
	background: transparent;
	top: 6px;
	left: 6px;
	border: 3px solid #64e3e8;
	border-top: none;
	border-right: none;
	-webkit-transform: rotate(-45deg);
	-moz-transform: rotate(-45deg);
	-o-transform: rotate(-45deg);
	-ms-transform: rotate(-45deg);
	transform: rotate(-45deg);
}

.squaredThree label:hover::after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
	filter: alpha(opacity=30);
	opacity: 0.3;
}

.squaredThree input[type=checkbox]:checked + label:after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: alpha(opacity=100);
	opacity: 1;
}
.squaredThree span{padding-left:25px; color:#a3a1a1}
	.showError{
font-family:Arial, Helvetica, sans-serif;	
font-size : 14px;
font-weight:bold;
color:#FF3300;
text-align:center;
height:20px;
}
/*==================================*/
.searchBtn {
	float: left;
	cursor: pointer;
	width: 44px;
	height: 38px;
	line-height: 15px;
	background-image: url(../images/search_ico.PNG);
	background-position: left 0;
	background-repeat: no-repeat;
}
.searchProgress {
	background-image: url(4.gif);
	background-position: left 0;
	float: left;
	cursor: pointer;
	width: 44px;
	height: 38px;
	line-height: 15px;
	background-repeat: no-repeat;
}
.textBox input {
	color: #999999;
	font: bold 14px arial;
	float: left;
	height: auto;
	padding: 9px;
	vertical-align: middle;
	width: 432px;
}
#content {
	height: auto;
	float:left;
	/*width: 490px;*/
}
#content #sub_cont {
	width: 490px;
	display: none;
	height: 400px;
}
#content .no-rec {
	color: #000;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 14px;
	padding: 5px;
	border-bottom: solid #669900;
	text-align: center;
	background: #CCCC66;
}
#content .each_rec {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 14px;
	padding: 5px;
	border-bottom: solid #669900;
	text-align: justify;
	background: #CCCC66;
	margin: 5px 0;
}
#content .each_rec:hover {
	background: #FFFFFF;
}
#content .each_rec a {
	color: #000;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 14px;
	text-decoration: none
}
.search-background {
	display: none;
	font-size: 13px;
	font-weight: bold;
	height: 160px;
	position: absolute;
	padding-top: 100px;
	text-align: center;
	text-decoration: none;
	width: 470px;
}
#heading {
	/*font-family: Georgia, "Times New Roman", Times, serif;
	font-size: 56px;
	color: #CC0000;*/
}
body {
	font-family: Arial, Sans-Serif;
}
search-background label {
	border: solid #66FF00 1px;
}
#paging_button ul {
	width: 600px;
	padding: 0px;
	margin: 8px;
}
#paging_button ul li {
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	-moz-box-shadow: 0 1px 3px rgba(0,0,0,0.6);
	-webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.6);
	float: left;
	height: 20px;
	width: 20px;
	list-style-image: none;
	list-style-type: none;
	font-weight: bold;
	border: solid #CCCCCC 1px;
	margin: 3px;
	cursor: pointer;
}
li:hover {
	color: #CC0000;
	cursor: pointer;
}
.footer-text{
	background: #01176a none repeat scroll 0 0;
    clear: both;
    padding: 1%;
    text-align: center;
	font-size: 13px;
	color:#fff;
	}

	
.news_list .squaredThree{
	margin: 5px 28px 10px 0 !important;
	
	}
.formHeadingBkg td{
	background: #01176a;
    color: #fff;
    font-size: 16px;
    padding: 1% 0;
    text-align: center;
    text-shadow: 1px 1px 1px #333;
	 }
	 
.cButton{
	/* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#323231+0,5b5b5a+100 */
background: rgb(50,50,49); /* Old browsers */
background: -moz-linear-gradient(top,  rgba(50,50,49,1) 0%, rgba(91,91,90,1) 100%); /* FF3.6-15 */
background: -webkit-linear-gradient(top,  rgba(50,50,49,1) 0%,rgba(91,91,90,1) 100%); /* Chrome10-25,Safari5.1-6 */
background: linear-gradient(to bottom,  rgba(50,50,49,1) 0%,rgba(91,91,90,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#323231', endColorstr='#5b5b5a',GradientType=0 ); /* IE6-9 */
border-radius: 10px;
    color: #ccc;
    padding: 3px 9px;
	}
.formTextWithBorder .squaredThree{
	 margin: 8px 25px 10px 0;
	}
/**
  MEDIA QUERIES
*/	
@media (min-width:768px) and (max-width:991px) {
	#right_menu{
		 float:none;
	 }
}


 @media (max-width: 767px) {
	 #right_menu{
		 float:none;
	 }
 }
 
 
/*-----------------------------
	START COLORBOX STYLES
-------------------------------*/
/*
    ColorBox Core Style:
    The following CSS is consistent between example themes and should not be altered.
*/
#colorbox, #cboxOverlay, #cboxWrapper{position:absolute; top:0; left:0; z-index:9999; overflow:hidden;}
#cboxOverlay{position:fixed; width:100%; height:100%;}
#cboxMiddleLeft, #cboxBottomLeft{clear:left;}
#cboxContent{position:relative;}
#cboxLoadedContent{overflow:auto;}
#cboxTitle{margin:0;}
#cboxLoadingOverlay, #cboxLoadingGraphic{position:absolute; top:0; left:0; width:100%; height:100%;}
#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow{cursor:pointer;}
.cboxPhoto{float:left; margin:auto; border:0; display:block;}
.cboxIframe{width:100%; height:100%; display:block; border:0;}

/* 
    User Style:
    Change the following styles to modify the appearance of ColorBox.  They are
    ordered & tabbed in a way that represents the nesting of the generated HTML.
*/
#cboxOverlay{background:url(../images/overlay.png) repeat 0 0;}
#colorbox{}
    #cboxTopLeft{width:21px; height:21px; background:url(../images/controls.png) no-repeat -101px 0;}
    #cboxTopRight{width:21px; height:21px; background:url(../images/controls.png) no-repeat -130px 0;}
    #cboxBottomLeft{width:21px; height:21px; background:url(../images/controls.png) no-repeat -101px -29px;}
    #cboxBottomRight{width:21px; height:21px; background:url(../images/controls.png) no-repeat -130px -29px;}
    #cboxMiddleLeft{width:21px; background:url(../images/controls.png) left top repeat-y;}
    #cboxMiddleRight{width:21px; background:url(../images/controls.png) right top repeat-y;}
    #cboxTopCenter{height:21px; background:url(../images/border.png) 0 0 repeat-x;}
    #cboxBottomCenter{height:21px; background:url(../images/border.png) 0 -29px repeat-x;}
    #cboxContent{background:#fff; overflow:hidden;}
        .cboxIframe{background:#fff;}
        #cboxError{padding:50px; border:1px solid #ccc;}
        #cboxLoadedContent{margin-bottom:28px;}
        #cboxTitle{position:absolute; bottom:4px; left:0; text-align:center; width:100%; color:#949494;}
        #cboxCurrent{position:absolute; bottom:4px; left:58px; color:#949494;}
        #cboxSlideshow{position:absolute; bottom:4px; right:30px; color:#0092ef;}
        #cboxPrevious{position:absolute; bottom:0; left:0; background:url(../images/controls.png) no-repeat -75px 0; width:25px; height:25px; text-indent:-9999px;}
        #cboxPrevious:hover{background-position:-75px -25px;}
        #cboxNext{position:absolute; bottom:0; left:27px; background:url(../images/controls.png) no-repeat -50px 0; width:25px; height:25px; text-indent:-9999px;}
        #cboxNext:hover{background-position:-50px -25px;}
        #cboxLoadingOverlay{background:url(../images/loading_background.png) no-repeat center center;}
        #cboxLoadingGraphic{background:url(../images/loading.gif) no-repeat center center;}
        #cboxClose{position:absolute; bottom:0; right:0; background:url(../images/controls.png) no-repeat -25px 0; width:25px; height:25px; text-indent:-9999px;}
        #cboxClose:hover{background-position:-25px -25px;}

/*
  The following fixes a problem where IE7 and IE8 replace a PNG's alpha transparency with a black fill
  when an alpha filter (opacity change) is set on the element or ancestor element.  This style is not applied to or needed in IE9.
  See: http://jacklmoore.com/notes/ie-transparency-problems/
*/
.cboxIE #cboxTopLeft,
.cboxIE #cboxTopCenter,
.cboxIE #cboxTopRight,
.cboxIE #cboxBottomLeft,
.cboxIE #cboxBottomCenter,
.cboxIE #cboxBottomRight,
.cboxIE #cboxMiddleLeft,
.cboxIE #cboxMiddleRight {
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF,endColorstr=#00FFFFFF);
}

/*
  The following provides PNG transparency support for IE6
  Feel free to remove this and the /ie6/ directory if you have dropped IE6 support.
*/
.cboxIE6 #cboxTopLeft{background:url(../images/ie6/borderTopLeft.png);}
.cboxIE6 #cboxTopCenter{background:url(../images/ie6/borderTopCenter.png);}
.cboxIE6 #cboxTopRight{background:url(../images/ie6/borderTopRight.png);}
.cboxIE6 #cboxBottomLeft{background:url(../images/ie6/borderBottomLeft.png);}
.cboxIE6 #cboxBottomCenter{background:url(../images/ie6/borderBottomCenter.png);}
.cboxIE6 #cboxBottomRight{background:url(../images/ie6/borderBottomRight.png);}
.cboxIE6 #cboxMiddleLeft{background:url(../images/ie6/borderMiddleLeft.png);}
.cboxIE6 #cboxMiddleRight{background:url(../images/ie6/borderMiddleRight.png);}

.cboxIE6 #cboxTopLeft,
.cboxIE6 #cboxTopCenter,
.cboxIE6 #cboxTopRight,
.cboxIE6 #cboxBottomLeft,
.cboxIE6 #cboxBottomCenter,
.cboxIE6 #cboxBottomRight,
.cboxIE6 #cboxMiddleLeft,
.cboxIE6 #cboxMiddleRight {
    _behavior: expression(this.src = this.src ? this.src : this.currentStyle.backgroundImage.split('"')[1], this.style.background = "none", this.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src=" + this.src + ", sizingMethod='scale')");
}
/*-----------------------------
	END COLORBOX STYLES
-------------------------------*/
.maincontent{min-height:80vh !important}
.main-header h1{
	text-shadow: 1px 2px 3px #333; 
	color:#fff; 
	float:left; 
	margin-top:10px;
	font-family:"good times";
}
.main-header .site-logo{
	float:left; 
	padding-right:10px; 
	width:10%;
}
.header-right .welcome-block{
	float:right; 
	margin-top:20px;
	text-transform: capitalize;
	color:#fff;
}
.form-control{
	height:39px !important;
	margin-bottom:20px;
}
.btn-default {
    border: 2px solid #01176a !important;
}
input[type="file"] {
	border: 1px solid #ccc;
   /* height: 40px;*/
}
#frmNewsDetail .vidItem td, #frmNewsDetail #edit_news_div td, #frmFacultyDetail .vidItem td, #frmFacultyDetail #edit_news_div td, #frmtenderDetail .vidItem td, #frmtenderDetail #edit_news_div td{
	height:50px;
}
#frmNewsDetail #edit_news_div .tcal.form-control.tcalInput , #frmFacultyDetail #edit_news_div .tcal.form-control.tcalInput, #frmtenderDetail #edit_news_div .tcal.form-control.tcalInput{
    margin-bottom: 5px;
}
/*#frmFacultyDetail input[type="file"] {
    width: 100% !important;
}*/
#frmDepartment textarea.form-control {
    height: 80px !important;
}
#frmNewsDetail textarea.form-control, #frmFacultyDetail textarea.form-control, #frmtenderDetail textarea.form-control, #frmFacultyDetail textarea.form-control{
    height: 150px !important;
}
#frmTenderDetail .tcalInput{
	width:100% !important;
}
input[type="file"] {
    width: 80% !important;
}
.errMsgAdmin span.msgText{
   color: #008000;
}