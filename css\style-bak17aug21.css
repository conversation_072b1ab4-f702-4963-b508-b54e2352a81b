body{
	overflow-x: hidden;
	font-family: 'Open Sans', sans-serif;
	font-size: 14px;
}
.ct_wrapper,
header,
.ct_content_wrap,
section,
footer{
	float:left;
	width:100%;
	position:relative;
}
.ct_wrapper{
	overflow: visible;	
}
section{
	padding:15px 0px;	
}
hr{
	margin:5px !important
}
a{
	color: #333;
}
a:hover{
	color: #ccc;
	}
/*tab-css start*/
.iq_tab_menu{
	float:left;
	width:100%;
	position:relative;
	text-align:center;
	margin:0px 0px 10px;	
}
.iq_tab_menu ul{
	float:left;
	width:100%;	
	margin: 0 0 30px;
	text-align: center;
}
.iq_tab_menu ul li{
	display: inline-block;
	position:relative;
	width:147px;
	margin:0px;	
}
.iq_tab_menu ul li:first-child{
	margin-left:0px;
	margin-bottom: 5px;	
}
.iq_tab_menu ul li a{
	display:block;
	font-size:15px;
	font-weight:normal;
	padding:12px 0px;
	line-height:normal;
	border:1px solid #c4c4c4;
	text-transform:uppercase;
	line-height: 18px;
}
.iq_tab_menu ul li:hover a,
.iq_tab_menu ul li.active a{
	color: #fff;
	background: #95103e;
}
/*tab-css end*/

/*Input field Css Start*/
.innerList li{
    /*position: relative;*/
    display: block!important;
	font-weight: normal!important;
	padding-left: 5px !important;
	color: #0c0c0c !important;
	line-height: 1.7;
	margin: 5px 0;
	font-size: 14px !important;
	
}
.innerList li:before{
    font-family: 'FontAwesome';
    content: "\f00c";
   /* position: absolute;*/
    left: 0;
	padding-right:5px;
}
.innerList li strong{
    font-weight: bold;
	padding-left: 5px;

}
ol li{
	color: #0c0c0c;
	font-size: 14px;
	line-height: 2;
	list-style:disc !important;
}
.iq-input{
    float: left;
    width: 100%;
    margin: 0 0 30px;
    position: relative;
}
.iq-input:last-child{
	margin: 0;
}
.iq-input textarea,
.iq-selectric,
.iq-input select,
.iq-input input[type="email"],
.iq-input input[type="text"]{
    float: left;
    width: 100%;
    font-weight: normal;
    color: #7d8386;
    font-size: 14px;
    text-align: left;
    min-height: 46px;
    line-height: 43px;
    padding: 0 12px 1px;
    -moz-appearance:none;
    -webkit-appearance:none;
    border: 1px solid #434141;
    text-transform: capitalize;
    background-color: transparent;
	margin-bottom:10px;
}
.iq-input select{
    outline: none;
}
.iq-input input[type="text"]::-moz-placeholder{color: #7d8386}
.iq-input input[type="email"]::-webkit-input-placeholder{color: #7d8386}
.iq-input input[type="text"]::-moz-placeholder{color: #7d8386}
.iq-input input[type="email"]::-webkit-input-placeholder{color: #7d8386}
/*Selectric Start*/
.iq-input select,
.iq-selectric{
    background-size: 10px !important;
    background: transparent url("../images/arrow-down.png") no-repeat right 15px center;
}
.select ul.options{
	display: none;
}
.select ul.options{
	left: 0;
	right: 0;
	top: 100%;
	z-index: 999999999;
	position: absolute;
	background-color: #fff;
	border:1px solid #dbdbdb;
    text-transform: capitalize;
    overflow-y: scroll;
    overflow-x: hidden;
    max-height: 122px;
}
.select ul.options li{
	float: left;
	width: 100%;
	cursor: pointer;
	text-align: left;
	padding: 10px 15px;
    font-weight: normal;
}
.select ul.options li:hover{
    color: #fff;
}
/*Input Icon Button Start*/
.iq-input .iq-input-icon{
    top: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    z-index: 1;
    width: 45px;
    height: 100%;
    color: #777777;
    cursor: pointer;
    text-align: center;
    position: absolute;
}
.iq-input .iq-input-icon:before{
    left: 0;
    top: 50%;
    right: 0;
    z-index: -1;
    content: "\f002";
    position: absolute;
    font-family: 'FontAwesome';
    transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
}
.iq-input .iq-input-icon input{
    height: auto;
    font-size: 0;
    border:none !important;
    background-color: transparent !important;
}
.bnr{
	padding: 0;
}
.no_padding{
	padding:0px;	
}
.no_margin{
	margin:0px;	
}
.ct_bg_1{
	background-color:#d33ba0;	
}
.ct_bg_2{
	background-color:#1dc454;	
}
.ct_bg_3{
	background-color:#2c6fd1;	
}
.ct_bg_4{
	background-color:#e7b52a;	
}
.ct_bg_5{
	background-color:#44a0d0;	
}
.ct_bg_6{
	background-color:#199c9a;	
}
/*
	  ==============================================================
		   Top Bar Wrap Style
	  ==============================================================
*/
.msn h3{
	font-size: 24px;
	border-bottom: 1px solid #ccc;
	padding: 10px 0;
}
.msn h3 span{
	border-bottom:4px solid #95103e ;
	padding: 7px 0;
}
.msn p{
	padding:5px;
}
.gt_aside_category gt_detail_hdg aside_margin_bottom h5{
	border-bottom: 3px solid #d35400 !important;
}
.gt_aside_post_des2 h6 a{
	color: #333 !important;
}
.gt_aside_post_des2 h6 a:hover{
	color: #f00 !important;
}
.btnn {
	background: #95103e!important;
	padding:5px 8px;
	color: #fff;
	border-radius:0 !important;
	margin-top:5px;
}
.btnn:hover {
	background: #b41049 !important;
	color: #ccc;
}
.btnn-smt {
	background: none!important;
	padding:5px 8px;
	color: #fff;
	border-radius:0 !important;
	margin-top:5px;
	border:1px solid #FFF !important;
}
.btnn-smt:hover {
	color: #fff !important;
	border: 1px solid #999 !important;
}
.btnn-rst {
	background: #900!important;
	padding:5px 8px;
	color: #fff;
	border-radius:0 !important;
	margin-top:5px;
}
.btnn-rst:hover {
	background: #933 !important;
	color: #fff;
}
.top_strip{
	float:left;
	width:100%;
	position:relative;
	background-color:#222222;
	padding:2px 0px;	
}
.top_location_wrap{
	float:left;
	width:auto;
	position:relative;	
}
.top_location_wrap p{
	color:#fff;
	margin:0px;
	font-family: 'Open Sans', sans-serif;
	font-size:12px;
	font-weight:300;	
}
.top_location_wrap p i{
	margin:0px 8px 0px 0px;	
}
.top_location_wrap p a{
	padding: 0 5px;
}
.top_ui_element{
	float:right;
	width:auto;
	position:relative;	
}
.top_ui_element ul{
	float:left;
	width:100%;	
}
.top_ui_element ul li{
	position:relative;
	color:#fff;
	display:inline-block;
	font-family: 'Open Sans', sans-serif;
	padding:0px 5px;
	font-size:11px;
	font-weight:300;
	letter-spacing:1.4px;
}
.top_ui_element ul li:last-child{
	padding-right:0px;	
}
.top_ui_element ul li i{
	margin:0px 5px 0px 0px;	
}
.top_ui_element ul li a{
	color:#fff;	
}
.top_ui_element ul li span{
	background: #fff;
	color: #fff !important;
	padding: 5px;
}
.topheader, .theme1{
	padding: 3px 0;
}
.topheader, .theme2{
	padding:5px;
}
.topheader, .topheader a{
	color: #d2d2d2;
	font-size: 12px;
	font-weight: normal;
}
.topheader a:hover{
	text-decoration: none;
	color: #fff;
}
.topheader a.fntInc{
	display: inline-block;
	/*background: #fff;*/
	color: #d2d2d2;
	padding: 1px 0;
	width: 25px;
	text-align: center;
}
.topheader a.whlTe, .topheader a.blTh, .topheader a.bluTh{
	border: solid 1px #666;
	padding: 0;
	background: #fff;
	color: #333;
}
.topheader a.black{
	background: #000;
	color:#FFF;
	border: 1px solid #666;
}
/*
	  ==============================================================
		   Logo Wrap Style
	  ==============================================================
*/
.logo_wrap{
	float:left;
	width:auto;	
	padding:10px 0px;
}
.logo_wrap a{
	display:block;	
}
/*
	  ==============================================================
		   Navigation Wrap Style
	  ==============================================================
*/
.menu_bg{
	background: #039 !important;
}
.logo_outer_wrap{
	float:left;
	width:100%;
	position:relative;	
}
.nav_outer_wrap{
	float:left;
	width:100%;
	position:relative;
	background: #95103e;
	border-bottom:1px solid #ddd;
	border-top: 1px solid #ddd;
}
.back_to_top{
	background: #95103e !important;
}
.back_to_top a{
	color: #fff;
}
.back_to_top a:hover{
	color: #fff;
}

.main_navigation{
	float:left;
	width:auto;	
}
.main_navigation ul{
	float:left;
	width:100%;	
}
.main_navigation ul li{
	position:relative;
	display:inline-block;
	font-family: 'Open Sans', sans-serif;
}
.main_navigation ul li a{
	display:block;
	text-transform: none;
	padding:10px 14px;
	/*font-weight:700;*/
	font-size:14px;
	color: #fff;
	font-weight: bold;
}
.main_navigation ul li a:hover{
	color: #fff !important;
	background: #ae154a;
}
.main_navigation ul li a span{
	display:block;
	font-size:10px;
	line-height:normal;	
	font-weight:300;
}
/*2nd ul*/
.main_navigation ul ul{
	position:absolute;
	top:100%;
	left:0px;
	width:200px;
	z-index:1000;
	background-color:#95103e;
	opacity:0;
	visibility:hidden;	
}
.main_navigation ul ul li{
	width:100%;
}
.main_navigation ul ul li a{
	font-weight:300;
	font-size:12px;
	padding:10px 15px;
	border-bottom:1px solid #ddd;
	color: #fff;
}
.main_navigation ul ul li a:hover{
	color: #ccc !important;
}
.main_navigation ul ul ul{
	position:absolute;
	top:0px;
	left:100%;	
}
.main_navigation ul > li:hover > ul{
	opacity:1;
	visibility:visible;	
}
/*
	  ==============================================================
		   Search Wrap Style
	  ==============================================================
*/
.top_search_wrap{
	float:right;
	width:auto;
	padding:22px 0;
	cursor:pointer;
	position:relative;	
	color: #fff;
}
.top_search_wrap i{
	font-size:16px;	
}
.search-wrapper-area{
	position:absolute;
	right:0px;
	top:64px;
	background:#fff;
	padding:10px 20px;
	display:none;
	z-index:20;
}
.search-area{
	position:relative;
}
.search-area input[type="text"]{
	float:left;
	border:1px solid #ccc;
	height:40px;
	padding:5px 15px;
}
.search-area input[type="submit"]{
	float:left;
	position:absolute;
	right:0px;
	padding:10px; 
	border:none;
	background:#95103e !important;
	color:#fff;
}
/*
	  ==============================================================
		   Banner Wrap Style
	  ==============================================================
*/

/*
	  ==============================================================
		   Get Started Wrap Style
	  ==============================================================
*/
.get_started_outer_wrap{
	float:left;
	width:100%;
	position:relative;
	border-bottom:1px solid #f2f2f2;
	/*padding:0px 0px 10px;*/
	margin:0px 0px 25px;	
}
.get_started_outer_wrap:before{
	content:"";
	position:absolute;
	bottom:4px;
	left:0px;
	right:0px;
	border-bottom:1px solid #f2f2f2;
}
.get_started_content_wrap{
	float:left;
	width:100%;
	position:relative;	
}
.get_started_content_wrap h3{
	margin:0px 0px 15px;
	text-transform:uppercase;	
}
.get_started_content_wrap p{
		
}
/*
	  ==============================================================
		   Get Started Services Wrap Style
	  ==============================================================
*/
.carousel-control{
	width:5%;
}
.get_started_services1{
	float:left;
	width:100%;
	position:relative;
	background: #d35400;
	margin: 0;
	padding: 25px;
}
.get_started_services2{
	float:left;
	width:100%;
	position:relative;
	background: #3498db;
	margin: 0;
	padding: 25px;
}
.get_started_services3{
	float:left;
	width:100%;
	position:relative;
	background: #8e44ad;
	margin: 0;
	padding: 25px;
}
.get_started_icon{
	float:left;
	width:50px;
	position:relative;	
}
.get_started_icon i{
	font-size:40px;
	color:#fff;	
}
.get_icon_des{
	float:none;
	padding:0px 0px 0px 70px;
	width:auto;	
}
.get_icon_des p{
	color: #fff;
}
.get_icon_des h5{
	text-transform: uppercase;
	margin: 0px 0px 8px;
	color: #fff !important;
	font-weight: bold;	
}
.get_icon_des a{
	text-transform:capitalize;
	color: #333;
	border:1px solid #8b8888;
	padding: 6px 10px;
	background: #fff;
	float: right;
}
.get_icon_des a:hover{
	color:#333;	
}
.get_icon_des a i{
	margin:0px 0px 0px 8px;	
}
/*
	  ==============================================================
		   Heading Wrap Style
	  ==============================================================
*/
.ct_heading_1_wrap{
	float:left;
	width:100%;
	position:relative;
	text-align:center;
	margin:0px 0px 50px;	
}
.ct_heading_1_wrap h3{
	text-transform:uppercase;
	font-weight:600;
	margin:0px 0px 10px;
	color: #95103e !important;
}
.ct_heading_1_wrap p{
	margin:0px 0px 15px;	
}
.ct_heading_1_wrap span{
	display:inline-block;
	position:relative;	
}
.ct_heading_1_wrap span img{
	display:inline-block;
	width:auto;
	height:auto;
	margin-top:-5px;	
}
.ct_heading_1_wrap span:before,
.ct_heading_1_wrap span:after{
	content:"";
	position:absolute;
	width:80px;
	height:1px;
	background-color:#777777;
	top:10px;	
}
.ct_heading_1_wrap span:before{
	left:-100px;	
}
.ct_heading_1_wrap span:after{
	right:-100px;	
}
/*White Heading*/
.ct_heading_1_wrap.ct_white_hdg h3{
	color:#7c7c7c;	
}
.ct_heading_1_wrap.ct_white_hdg p{
	color:#fff;	
}
.ct_heading_1_wrap.ct_white_hdg span:before,
.ct_heading_1_wrap.ct_white_hdg span:after{
	background-color:#212121;	
}
/*
	  ==============================================================
		   Courses Subject Wrap Style
	  ==============================================================
*/
.ct_courses_subject_bg{
	background-image:url(../extra-images/banner-02.jpg);
	background-size:cover;
	background-position:center;
	background-repeat:no-repeat;
	padding: 40px 0 40px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	overflow: hidden;
}
.ct_courses_subject_bg:before{
	content: "";
	position: absolute;
	top: 0px;
	left: 0px;
	bottom: 0px;
	width: 170px;
	height: auto;
	background-image:url(images/cources-left-icons.png);
	background-position:center;
	background-repeat:no-repeat;
	background-size: contain;
}
.ct_courses_subject_bg:after{
	content: "";
	position: absolute;
	top: -20px;
	right: -15px;
	bottom: 0px;
	width: 170px;
	height: auto;
	background-image:url(images/cources-right-icons.png);
	background-position:center;
	background-repeat:no-repeat;
	background-size: contain;
}
.courses_subject_carousel{
	float:left;
	width:100%;
	position:relative;	
}
.courses_subject_carousel .item{
	margin:0px 10px;
	float:left;
	width: 90%;
}
.course_subject_wrap{
	float:left;
	width:100%;
	position:relative;
	text-align:center;
	/*padding:20px 20px;*/
	padding:10px 10px;	
}
.course_subject_wrap p{font-size:10px;}
.course_subject_wrap i{
	display:block;
	font-size:30px;	
	margin:0px 0px 10px;
	color:#fff;
}
.course_subject_wrap p{
	color:#fff;
	text-transform:capitalize;
	margin:0px;
	font-weight:600;	
}
.course_subject_wrap p span{
	display:block;
	font-weight:bold;
}
.course_subject_wrap:hover{
	background-color:#fff;
}
.course_subject_wrap:hover i,
.course_subject_wrap:hover p{
	color:#333;	
}
/*Owl Carousel*/
.courses_subject_carousel .owl-nav{
	position:absolute;
	top:50%;
	width:100%;	
}
.courses_subject_carousel .owl-nav div{
	font-size:0px;
	position:absolute;	
}
.courses_subject_carousel .owl-nav .owl-prev{
	left:-20px;	
}
.courses_subject_carousel .owl-nav .owl-next{
	right:0px;	
}
.courses_subject_carousel .owl-nav .owl-prev:before,
.courses_subject_carousel .owl-nav .owl-next:before{
	content:"\f104";
	font-family:fontawesome;
	position:absolute;
	top:-40px;
	left:0px;
	width:auto;
	height:auto;
	color:#787879;
	font-size:55px;	
}
.courses_subject_carousel .owl-nav .owl-next:before{
	content:"\f105";
}
/*
	  ==============================================================
		   Most Popular Courses Wrap Style
	  ==============================================================
*/
.ct_course_list_wrap{
	margin:0px 0px 30px;	
}
.ct_course_list_wrap,
.most_popular_courses{
	float:left;
	width:100%;
	position:relative;	
}
.most_popular_courses .item{
	margin:0px 10px 0px;	
}
.ct_course_list_wrap figure img{
	width:100%;
	height:auto	
}
.course_list_img_des{
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;	
}
.ct_course_review{
	position:absolute;
	bottom:0px;
	left:0px;
	width:100%;
	padding:8px 0px;
	text-align:center;
	background-color:rgba(34,34,34,0.7);
	z-index:10;
}
.ct_course_review span{
	display:inline-block;
	color:#fff;
	text-transform:capitalize;
	margin:0px 15px 0px 0px;	
}
.ct_course_review ul{
	width:auto;
	display:inline-block;	
}
.ct_course_review ul li{
	display:inline-block;
	position:relative;	
}
.ct_course_review ul li a{
	display:inline-block;
	color:#efb467;	
}
.ct_course_list_wrap:hover .course_list_img_des .ct_zoom_effect{
	transform:scale(1);
	-webkit-transform:scale(1);
	-moz-transform:scale(1);
	opacity:0.8;	
}
.ct_course_link{
	position:absolute;
	top:50%;
	width:100%;
	text-align:center;
	margin:-25px 0px 0px;
	transform:scale(0);
	-webkit-transform:scale(0);
	-moz-transform:scale(0);	
}
.ct_course_link > a{
	display:inline-block;
	border:1px solid #fff;
	color:#fff;
	font-weight:600;
	padding:10px 20px;	
}
.ct_course_list_wrap:hover .ct_course_link{
	transform:scale(1);
	-webkit-transform:scale(1);
	-moz-transform:scale(1);	
}
.popular_course_des{
	float:left;
	width:100%;
	position:relative;
	padding:10px;
	background-color:#f8f9f8;
	border: 1px solid #ddd;
	height: 165px;
}
.popular_course_des p{
	font-size: 12px;
}
.popular_course_des h5 > a{
	text-transform:uppercase;
	margin:0px 0px 15px;
	display:block;	
}
.popular_course_des h5 > a:hover{
	color: #000 !important;
}
.ct_course_meta{
	float:left;
	width:100%;
	border-top:1px solid #cecece;
	padding:10px 0px;	
}
.course_author{
	float:left;
	width:auto;	
}
.course_author i{
	margin:0px 8px 0px 0px;
	color:#777777;	
}
.course_author a{
	display:inline-block;
	color:#777777;
	text-transform:capitalize;
	font-weight:300;
	font-size:12px;
}
.ct_course_meta ul{
	width:auto;
	float:right;	
}
.ct_course_meta ul li{
	display:inline-block;
	position:relative;
	color:#777777;
	padding:0px 0px 0px 10px;
}
.ct_course_meta ul li a{
	display:inline-block;
	color:#777777;	
	font-weight:300;
}
.ct_course_meta ul li i{
	margin:0px 8px 0px 0px;	
}
/*Owl Carousel*/
.most_popular_courses .owl-nav{
	position:absolute;
	top:50%;
	width:100%;	
}
.most_popular_courses .owl-nav div{
	font-size:0px;
	position:absolute;	
}
.most_popular_courses .owl-nav .owl-prev{
	left:-30px;	
}
.most_popular_courses .owl-nav .owl-next{
	right:-10px;	
}
.most_popular_courses .owl-nav .owl-prev:before,
.most_popular_courses .owl-nav .owl-next:before{
	content:"\f104";
	font-family:fontawesome;
	position:absolute;
	top:-40px;
	left:0px;
	width:auto;
	height:auto;
	color:#787879;
	font-size:55px;	
}
.most_popular_courses .owl-nav .owl-next:before{
	content:"\f105";
}

/*
	  ==============================================================
		   Our Team Wrap Style
	  ==============================================================
*/
.teacher_bg{
	padding-bottom:50px;	
}
.ct_teacher_outer_wrap{
	float:left;
	width:100%;
	position:relative;
	background-color:#fff;
	margin:0px 0px 30px;	
}
.ct_teacher_outer_wrap figure img{
	width:100%;
	height:auto;	
}
.ct_teacher_wrap{
	float:left;
	width:100%;
	text-align:center;
	position:relative;
	padding:20px 15px;
	background-color: #f8f9f8;
}
.ct_teacher_wrap h5 > a{
	display:block;
	margin:0px 0px 10px;
	text-transform:capitalize;	
}
.ct_teacher_wrap span{
	color:#242424;
	text-transform:uppercase;
	margin:0px 0px 15px;
	display:block;	
}
.ct_teacher_wrap ul{
	border-top:1px solid #cecece;
	padding:10px 0px 0px;
	float:left;
	width:100%;	
}
.ct_teacher_wrap ul li{
	position:relative;
	display:inline-block;
	width:23%;	
}
.ct_teacher_wrap ul li a{
	display:block;
	color:#cecece;
	font-size:18px;	
}
.ct_teacher_wrap h5 > a:hover{
	color:#222;
}
.ct_teacher_outer_wrap:hover figure img{
	transform:scale(1.2) rotate(-6deg);
	-webkit-transform:scale(1.2) rotate(-6deg);
	-moz-transform:scale(1.2) rotate(-6deg);	
}
/*
	  ==============================================================
		   Facts Wrap Style
	  ==============================================================
*/
.ct_facts_bg{
	background-size:cover;
	background-repeat:no-repeat;	
}
/*
	  ==============================================================
		   Testimonial Wrap Style
	  ==============================================================
*/
.ct_testimonial_bg{
	background-image:url(images/testimonial-bg.jpg);
	background-repeat:no-repeat;
	background-size:cover;	
}
.ct_testimonial_bg:before{
	content:"";
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	background-color:#222;	
}
.testimonial_carousel{
	float:left;
	width:100%;
	position:relative;	
}
.testimonial_carousel .item{
	float:left;
	margin:0px 10px;	
}
.testimonial_wrap{
	float:left;
	width:100%;
	position:relative;
	background-color:#fff;
	text-align:center;
	padding:10px;
	border: 1px solid #ddd;
}
gt_aside_post_des span{
	padding-bottom:15px;
}
.testimonial_wrap figure{
	/*border-radius:100%;*/
	display:inline-block;
	margin:0px 0px 10px;
	width:auto;
	float:none;	
}
.testimonial_wrap figure img{
	width:auto !important;
	height:auto;	
}
.testimonial_wrap p{
	color:#666666;
	font-style:italic;	
}
.testimonial_wrap span{
	display:block;
	color:#333333;	
}
/*Owl Carousel*/
.testimonial_carousel .owl-nav{
	position:absolute;
	top:50%;
	width:100%;	
}
.testimonial_carousel .owl-nav div{
	font-size:0px;
	position:absolute;	
}
.testimonial_carousel .owl-nav .owl-prev{
	left:-30px;	
}
.testimonial_carousel .owl-nav .owl-next{
	right:-10px;	
}
.testimonial_carousel .owl-nav .owl-prev:before,
.testimonial_carousel .owl-nav .owl-next:before{
	content:"\f104";
	font-family:fontawesome;
	position:absolute;
	top:-40px;
	left:0px;
	width:auto;
	height:auto;
	color:#787879;
	font-size:55px;	
}
.testimonial_carousel .owl-nav .owl-next:before{
	content:"\f105";
}
/*
	  ==============================================================
		   Learn More Wrap Style
	  ==============================================================
*/
.ct_learn_more_bg{
	background-color:#222222;
	padding:30px 0px;
	float:left;
	width:100%;
	position:relative;	
}
.ct_learn_more{
	float:left;
	width:100%;
	position:relative;	
}
.ct_learn_more h4{
	display:inline-block;
	color:#fff;
	font-weight:bold;
	text-transform:uppercase;
	padding-top:8px;	
}
.ct_learn_more h4 span{
	display:inline-block;	
}
.ct_learn_more a{
	float:right;
	display:inline-block;
	border:1px solid #fff;
	text-transform:uppercase;
	color:#fff;
	font-size:14px;
	padding:8px 20px;
	font-weight:600;	
}
/*
	  ==============================================================
		   Latest News Wrap Style
	  ==============================================================
*/
.ct_blog_simple_bg{
	padding-bottom:50px;	
}
.ct_news_wrap{
	float:left;
	width:100%;
	position:relative;
	margin:0px 0px 30px;
}
.ct_news_wrap span{
	display:block;
	color:#777777;
	font-weight:600;
	margin:0px 0px 10px;	
}
.ct_news_wrap h5 > a{
	display:block;
	color:#333333;
	font-weight:300;
	margin:0px 0px 10px;	
}
.ct_news_wrap p{
	margin:0px 0px 15px;	
}
.news_img{
	float:left;
	width:100%;
	position:relative;	
}
.news_img img{
	width:30px;
	height:30px;
	float:left;
	margin:0px 20px 0px 0px;	
}
.news_img label{
	float:left;
	display:block;
	color:#777777;
	font-weight:600;
	padding:2px 0px 0px;	
}
/*
	  ==============================================================
		   Latest Event Wrap Style
	  ==============================================================
*/
.event_bg{
	padding-bottom:50px;	
}
.ct_main_event_wrap{
	float:left;
	width:100%;
	position:relative;
	background-image:url(extra-images/event-01.jpg);
	background-repeat:no-repeat;
	background-size:cover;
	background-position:center;	
	text-align:center;
	z-index:10;
	padding:48px 10px;
}
.ct_main_event_wrap:before{
	content:"";
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	background-color:rgba(0,0,0,0.5);
	z-index:-1;	
}
.ct_main_event_wrap h4{
	color:#FFF;
	margin-bottom:20px;
	text-transform:uppercase;
}
.ct_main_event_wrap h5{
	color:#FFF;
	margin-bottom:50px;
	font-weight:600;
}
.ct_main_event_wrap h5 span{
	display:inline-block;
}
.ct_main_event_wrap ul.countdown{
	float:left;
	width:100%;	
}
.ct_main_event_wrap ul.countdown li{
	position:relative;
	display:inline-block;
	color:#fff;
	margin:0px 0px 0px 50px;
}
.ct_main_event_wrap ul.countdown li:first-child{
	margin-left:0px;	
}
.ct_main_event_wrap ul.countdown li span{
	font-size:24px;
	position:relative;	
	width:75px;
	height:75px;
	display:inline-block;
	line-height:80px;
}
.ct_main_event_wrap ul.countdown li span:before{
	content:"";
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	border:1px solid #fff;
	transform:rotate(45deg);
	-webkit-transform:rotate(45deg);
	-moz-transform:rotate(45deg);	
}
.ct_main_event_wrap ul.countdown li span:after{
	content:"";
	position:absolute;
	top:0px;
	left:7px;
	width:100%;
	height:100%;
	border:1px solid #fff;
	transform:rotate(45deg);
	-webkit-transform:rotate(45deg);
	-moz-transform:rotate(45deg);
}
.ct_main_event_wrap ul.countdown li p{
	text-transform:capitalize;
	margin:0px;
	font-weight:bold;
	line-height:80px;
	color:#fff;	
}
.ct_main_event_wrap ul.event_location_list{
	float:left;
	width:100%;		
}
.ct_main_event_wrap ul.event_location_list li{
	position:relative;
	width:100%;
	display:inline-block;
	color:#fff;
	margin:5px 0px;
	font-weight:600;
	font-size:14px;	
}
.ct_main_event_wrap ul.event_location_list li i{
	margin-right:10px;
	color:#fff;
	font-size:18px;	
}
.sub_event_wrap{
	float:left;
	width:100%;
	position:relative;
	background-repeat:no-repeat;
	background-size:cover;
	background-position:center;
	z-index:10;
	padding:30px 10px 30px;
	margin:0px 0px 30px;
}
.sub_event_wrap:before{
	content:"";
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	background-color:rgba(0,0,0,0.8);
	z-index:-1;	
}
.sub_event_wrap h6 > a{
	display:block;
	color:#fff;
	margin:0px 0px 17px;
	padding:0px 0px 20px;
	position:relative;
	text-transform:uppercase;
	font-weight:600;
	line-height:26px;
	font-size:20px;	
}
.sub_event_wrap h6 > a:before{
	content:"";
	position:absolute;
	bottom:0px;
	left:0px;
	width:50px;
	height:2px;
	background-color:#fff;	
}
.sub_event_wrap span{
	display:block;
	font-size:11px;
	color:#fff;
	font-weight:600;	
}
.sub_event_wrap span i{
	color:#ffffff;
	margin-right:10px;	
}
/*
	  ==============================================================
		   Sub Banner Wrap Style
	  ==============================================================
*/
.sub_banner_wrap{
	background-image:url(images/sub-banner-bg.jpg);
	background-repeat:no-repeat;
	background-size:cover;
	background-position:center;
	z-index:10;
}
.sub_banner_wrap:before{
	content:"";
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	background-color:rgba(0,0,0,0.4);
	z-index:-1;	
}
.sub_banner_hdg,
.ct_breadcrumb{
	float:left;
	width:100%;	
}
.sub_banner_hdg h3{
	color:#fff;
	text-transform:uppercase;	
}
.ct_breadcrumb ul{
	float:left;
	width:100%;
	text-align:right;	
}
.ct_breadcrumb ul li{
	display:inline-block;
	position:relative;	
}
.ct_breadcrumb ul li:before{
	content:"/";
	color:#fff;
	position:absolute;
	top:0px;
	left:-4px;
	width:auto;
	height:auto;	
}
.ct_breadcrumb ul li:first-child:before{
	display:none;	
}
.ct_breadcrumb ul li:last-child{
	padding-right:0px;	
}
.ct_breadcrumb ul li a{
	display:inline-block;
	color:#fff;
	padding:0px 10px;	
}
/*
	  ==============================================================
		   Faqs and Terms Wrap Style
	  ==============================================================
*/
.accor_outer_wrap,
.ct_accord_list{
	float:left;
	width:100%;
	position:relative;	
}
.ct_accord_list{
	margin:0px 0px 20px;	
}
.accord_hdg,
.accord_list_1{
	float:left;
	width:100%;
	position:relative;
	border:1px solid #eeeeee;
	padding:18px 60px 18px 30px;	
}
.accord_hdg span,
.accord_list_1 span{
	position:absolute;
	top:20px;
	right:20px;
	color:#e8e6e6;
	display:inline-block;	
}
.accord_hdg span:after,
.accord_list_1 span:after{
	content:"";
	position:absolute;
	top:-10px;
	left:-15px;
	bottom:-10px;
	width:1px;
	background-color:#eeeeee;	
}
.accor_outer_wrap .accord_hdg h6,
.accord_list_1 h6{
	color:#666666;
	text-transform:capitalize;
	font-size:16px;	
}
.accord_des{
	float:left;
	width:100%;
	position:relative;	
}
.accord_des p{
	padding:18px 20px;	
}
.accor_outer_wrap .ct_accord_list:last-child{
	margin-bottom:0px;	
}
/*
	  ==============================================================
		   Contact us Wrap Style
	  ==============================================================
*/
.gt_contact_us_map.map-canvas{
	float:left;
	width:100%;
	height:400px;
	position:relative;	
}
.get_touch_wrap{
	float:left;
	width:100%;
	position:relative;
	margin:0px 0px 15px;	
}
.get_touch_wrap h4{
	text-transform:uppercase;
	margin:0px 0px 15px;
	font-weight:600;	
}
.ct_contact_form,
.ct_contact_form form,
.form_field{
	float:left;
	width:100%;
	position:relative;	
}
.form_field label{
	position:absolute;
	top:26px;
	left:30px;
	color:#d5d5d5;
	font-size:15px;
	margin:0px;	
}
.form_field input[type="text"],
.form_field textarea{
	width:100%;
	height:65px;
	border:1px solid #f2f2f2;
	padding:14px 20px 12px 60px;
	display:inline-block;
	margin:0px 0px 15px;
}
.form_field textarea{
	height:120px;
	padding:20px 20px 12px 60px;	
}
.form_field button{
	color:#fff;
	font-weight:bold;
	text-transform:uppercase;
	padding:20px 30px;
	font-size:16px;
	display:inline-block;	
}
.form_field button i{
	color:#fff;	
}
.form_field button:hover{
	background-color:#000;	
}
.ct_contact_address{
	float:left;
	width:100%;
	position:relative;
	margin:0px 0px 20px;	
}
.ct_contact_address h5{
	text-transform:capitalize;
	margin:0px 0px 15px;	
}
.ct_contact_address h5 i{
	margin-right:20px;	
}
.newletter_des.contact_us_newsltr{
	width:100%;	
}
.newletter_des.contact_us_newsltr:before{
	display:none;	
}
.newletter_des.contact_us_newsltr form{
	margin:0px 0px 20px;	
}
.contact_us_newsltr p{
	color:#777777;
	text-align:left;	
}
.bottom_border{
	float:left;
	width:100%;
	position:relative;
	border-bottom:1px solid #f2f2f2;
	margin-bottom:30px;
	z-index:99;
}
.fax_info{
	float:left;
	width:100%;
}
ul.fax_info li{
	display:inline-block;
	color:#777777;
	font-size:12px;
	padding:0px 8px;
	border-left:1px solid #777777;
	line-height:10px;
	font-family: 'Open Sans', sans-serif;
}
ul.fax_info li:first-child{padding-left:0px;border-left:0px;}
/*
	  ==============================================================
		   404 Page Wrap Style
	  ==============================================================
*/
.ct_404_page_bg_right{
	width:55%;
	float:left;	
	position:relative;
	min-height:100%;
}
.ct_404_page_bg:before{
	content:"";
	position:absolute;
	top:0px;
	right:0px;
	bottom:0px;
	background-image:url(images/404-bg.jpg);
	background-repeat:no-repeat;
	background-position:right;
	background-size:cover;
	width:55%;	
}
.ct_404_des_wrap{
	width:45%;
	float:left;
	padding:140px 60px;
	background-color:#1c1c1c;	
}
.ct_404_logo{
	float:left;
	width:100%;
	position:relative;
	text-align:center;
	margin:0px 0px 35px;
}
.ct_404_logo a{
	display:inline-block;	
}
.ct_404_detail_wrap{
	float:left;
	width:100%;
	position:relative;
	text-align:center;
	margin:0 0 40px;	
}
.ct_404_detail_wrap h1{
	color:#fff;
	font-weight:bold;
	font-size:200px;
	line-height:normal;
	font-family: 'Open Sans', sans-serif;
	line-height:170px;
	margin:0px 0px 10px;
}
.ct_404_detail_wrap p{
	color:#fff;
	text-transform:uppercase;
	font-weight:600;
	font-size:16px;
	margin:0px 0px 20px;	
}
.ct_404_detail_wrap form{
	float:left;
	width:100%;
	position:relative;
	margin:0px 0px 30px;	
}
.ct_404_detail_wrap form input[type="search"]{
	width:100%;
	height:38px;
	border:1px solid;
	background-color:transparent;
	padding:8px 60px 8px 15px;	
}
.ct_404_detail_wrap form button{
	color:#fff;	
	color:#fff;
	height:38px;
	position:absolute;
	top:0px;
	right:0px;
	padding:0px 25px;
}
.ct_404_detail_wrap form input[type="search"]:focus + button{
	background-color:#fff;
	color:#1c1c1c;	
}
.ct_404_detail_wrap > a{
	display:inline-block;
	color:#fff;
	text-transform:uppercase;
	padding:12px 30px;
	font-weight:600;	
}
.ct_404_detail_wrap > a:hover{
	background-color:#fff;
	color:#1c1c1c;	
}
.ct_404_socail_media{
	float:left;
	width:100%;
	position:relative;
	text-align:center;	
}
.ct_404_socail_media h6{
	color:#fff;
	text-transform:uppercase;
	margin:0px 0px 20px;	
}
.ct_404_socail_media ul{
	float:left;
	width:100%;
	margin:0px 0px 25px;	
}
.ct_404_socail_media ul li{
	display:inline-block;
	position:relative;	
}
.ct_404_socail_media ul li a{
	display:block;
	color:#afafaf;
	margin-right:6px;
	background-color:#282828;
	width:40px;
	height:40px;
	border-radius:100%;
	line-height:40px;
}
.ct_404_socail_media ul li:hover a{
	color:#fff;	
}
/*
	  ==============================================================
		   Blog Detail Wrap Style
	  ==============================================================
*/
.ct_blog_outer_wrap{
	border-bottom:1px solid #f4f4f4;
	padding-bottom:50px;	
}
.ct_blog_detail_outer_wrap{
	float:left;
	width:100%;
	position:relative;	
}
.ct_blog_detail_top{
	float:left;
	width:100%;
	position:relative;
	margin:0px 0px 30px;	
}
.ct_blog_detail_top h4{
	margin:0px 0px 20px;
	color:#222222;
	text-transform:uppercase;
}
.ct_blog_detail_top ul{
	float:left;
	width:100%;
	padding:10px;
	background-color:#f4f4f4;
}
.ct_blog_detail_top ul li{
	display:inline-block;
	position:relative;
	border-right:1px solid #d5d5d5;
	padding-right:20px;
	margin-right:20px;
}
.ct_blog_detail_top ul li img{
	display:inline-block;
	vertical-align:top;
	margin-right:15px;
	padding-top:3px;	
}
.ct_blog_detail_top ul li p{
	display:inline-block;
	margin:0px;	
}
.ct_blog_detail_top ul li p span{
	color:#666666;
	display:block;
	line-height:normal;
	font-weight:600;
	font-size:12px;
}
.ct_blog_detail_top ul li p span:nth-child(1){
	font-size:11px;
	margin:0px 0px 3px;
}
.ct_blog_detail_top ul li p span:nth-child(2){
	color:#666666;
	text-transform:uppercase;	
}
.ct_blog_detail_top > a{
	display:inline-block;
	color:#fff;
	text-transform:uppercase;
	position:absolute;
	right:0px;
	bottom:0px;
	height:57px;
	padding:0px 25px;
	line-height:56px;		
}
.ct_blog_detail_top > a:hover{
	background-color:#222222;	
}
.ct_blog_detail_des,
.ct_blog_detail_des_list,
.ct_blog_detail_tag{
	float:left;
	width:100%;
	position:relative;	
}
.ct_blog_detail_des figure{
	margin:0px 0px 15px;	
}
.ct_blog_detail_des figure img{
	width:100%;
	height:auto;	
}
.ct_blog_detail_des p{
	margin:0px 0px 15px;	
}
.ct_blog_detail_des_list{
	margin:0px 0px 25px;	
}
.ct_blog_detail_des_list h5{
	text-transform:uppercase;
	font-weight:normal;
	margin:0px 0px 15px;	
}
.ct_blog_detail_des_list p{
	margin:0px 0px 20px;	
}
.ct_blog_detail_des_list ul{
	float:left;
	width:100%;
}
.ct_blog_detail_des_list ul li{
	width:100%;
	position:relative;
	padding:6px 0 6px 30px;
	color:#777777;
	font-weight:500;
	font-family: 'Open Sans', sans-serif;
}
.ct_blog_detail_des_list ul li:before{
	content:"\f0a9";
	position:absolute;
	font-family:fontawesome;
	top:6px;
	left:0px;
	width:auto;
	height:auto;	
}
.ct_blog_detail_tag{
	padding:25px 0px;
	border-bottom:1px solid #f4f4f4;
	border-top:1px solid #f4f4f4;	
}
.ct_blog_detail_tag h5{
	text-transform:uppercase;
	font-weight:normal;
	display:inline-block;
	float:left;
	padding:3px 10px 0px 0px;	
}
.ct_blog_detail_tag ul{
	float:left;
	width:auto;	
}
.ct_blog_detail_tag ul li{
	display:inline-block;
	position:relative;
}
.ct_blog_detail_tag ul li a{
	display:block;
	text-transform:capitalize;
	font-size:12px;
	border:1px solid #e8e8e8;
	padding:6px 15px;
	color:#777;
}
.ct_blog_detail_tag ul li:hover a{
	color:#fff;	
}
.ct_blog_author{
	padding:25px 0px;
	border-bottom:1px solid #f4f4f4;	
}
.ct_blog_author,
.ct_author_des{
	float:left;
	width:100%;	
}
.ct_blog_author h5{
	text-transform:uppercase;
	font-weight:normal;
	margin:0px 0px 15px;	
}
.ct_author_des{
	background-color:#f4f4f4;
	padding:25px 20px;	
}
.ct_author_des figure{
	width:145px;	
}
.ct_author_des figure img{
	width:100%;
	height:auto;	
}
.ct_author_detail{
	float:none;
	padding:0px 0px 0px 160px;
	width:auto;	
}
.ct_author_detail > h6{
	text-transform:uppercase;
	display:inline-block;
	font-weight:600;
	margin-right:10px;
	margin-bottom:10px;
}
.ct_author_detail span{
	display:inline-block;	
}
.ct_author_detail p{
	line-height:26px;	
}
.ct_share_link{
	float:left;
	width:100%;	
}
.ct_share_link h6{
	color:#777777;
	margin-right:15px;
	display:inline-block;	
}
.ct_share_link ul{
	display:inline-block;
	position:relative;
	width:auto;	
}
.ct_share_link ul li{
	display:inline-block;
	position:relative;	
}
.ct_share_link ul li a{
	display:block;
	color:#777777;
	margin:0px 8px;	
}
/*Comment CSS*/
.gt_comment_list_wrap{
	float:left;
	width:100%;
	position:relative;
}
.gt_comment_list_wrap ul{
	float:left;
	width:100%;	
}
.gt_comment_list_wrap ul li{
	float:left;
	width:100%;
	position:relative;
	padding:25px 0 13px;
	border-bottom:1px solid #f8f8f8; 	
}
.gt_comment_list_wrap ul li:last-child{
	border-bottom:0px;
	padding-bottom:0px;	
}
.gt_comment_list_wrap > ul > li:first-child{
	padding-top:0px;	
}
.gt_comment_wrap{
	float:left;
	width:100%;
	position:relative;	
}
.gt_comment_wrap figure{
	width:90px;
	float:left;
	position:relative;	
}
.gt_comment_wrap figure img{
	width:100%;
	height:auto;	
}
.gt_comment_des{
	float:none;
	width:auto;
	padding:0px 0px 0px 110px;	
}
.gt_comment_des h6{
	display:inline-block;
	margin:0px 0px 10px;	
}
.gt_comment_des h6 > a{
	display:inline-block;
	font-size:15px;
	color:#222222;
	text-transform:uppercase;
}
.gt_comment_des > a{
	display:inline-block;
	float:right;
	font-size:14px;
	font-weight:600;	
}
.gt_comment_des > a i{
	margin-right:10px;	
}
.gt_comment_date{
	float: right;
    position: relative;	
}
.gt_comment_date span,
.gt_comment_date a{
	color:#aaa9a9;
	font-size:14px;
	margin-left:5px;
	display:inline-block;	
}
.gt_comment_date span:first-of-type{
	margin-left:0px;	
}
/*Second Ul*/
.gt_comment_list_wrap ul ul{
	padding-left:140px;
	margin:0px;
	border-top:1px solid #f8f8f8;
	margin-top:10px; 	
}
.gt_comment_list_wrap ul ul li{
	border:0px;
	padding-bottom:0px;	
}
/*Post Comment Wrap CSS*/
.gt_post_comment_wrap.ct_blog_author{
	padding-bottom:0px;	
}
.gt_post_comment_wrap{
	float:left;
	width:100%;
	position:relative;
	padding:25px 0px 60px;
	border-bottom:0px;	
}
.gt_commet_field{
	float:left;
	width:100%;
	position:relative;	
}
.gt_commet_field input[type="text"],
.gt_commet_field input[type="email"]{
	width:100%;
	height:45px;
	border:1px solid #d3d3d3;
	margin:0px 0px 25px;
	padding:8px 30px 8px 10px;
	background-color:#f7f8f7;	
}
.gt_commet_field textarea{
	width:100%;
	min-height:210px;
	border:1px solid #d3d3d3;
	padding:8px 30px 8px 10px;
	margin:0px 0px 25px;
	background-color:#f7f8f7;	
}
.gt_commet_field input[type="submit"],
.gt_commet_field button{
	color:#fff;
	font-size:16px;
	text-transform:uppercase;
	border:0px;
	padding:9px 40px 9px;
	font-weight:bold;	
}
/*Focus CSS*/
.gt_commet_field input[type="text"]:focus,
.gt_commet_field input[type="email"]:focus,
.gt_commet_field textarea:focus{
	box-shadow:0 0px 10px 1px rgba(0, 0, 0, 0.15);	
}
.gt_commet_field input[type="submit"]:hover,
.gt_commet_field button:hover{
	color:#fff;	
	background-color:#222;
}
/*Related Blog Css*/
.ct_related_blog{
	float:left;
	width:100%;
	position:relative;
	padding:50px 0px 30px;
}
.ct_related_blog h5{
	text-transform:uppercase;
	margin:0px 0px 20px;	
}
/*
	  ==============================================================
		   Pagination Wrap Style
	  ==============================================================
*/
.ct_pagination{
	float:left;
	width:100%;
	position:relative;	
}
.ct_pagination ul{
	float:left;
	width:100%;
	text-align:center;	
}
.ct_pagination ul li{
	position:relative;
	display:inline-block;	
}
.ct_pagination ul li a{
	display:inline-block;
	width:38px;
	height:38px;
	border:1px solid #d4d4d4;
	color:#777777;
	line-height:38px;	
}
.ct_pagination ul li a.next{
	color:#777777;
	width:70px;	
}
.ct_pagination ul li a:hover{
	color:#fff;	
}
/*
	  ==============================================================
		   Course Detail Wrap Style
	  ==============================================================
*/
.ct_course_detail_wrap{
	float:left;
	width:100%;
	position:relative;
	margin:0px 0px 25px;	
}
.ct_course_detail_wrap .ct_blog_detail_des_list{
	margin:0px;	
}
.ct_course_detail_wrap h5{
	text-transform:uppercase;
	margin:0px 0px 25px;	
}
ul.ct_course_list{
	background-color:#f4f4f4;
	width:100%;
	position:relative;
	float:left;	
}
ul.ct_course_list li{
	width:100%;
	position:relative;
	float:left;
	border-bottom:1px solid #e8e8e8;
	padding:20px 10px 16px 30px;
	line-height:normal;	
}
ul.ct_course_list li:last-child{
	border-bottom:0px;	
}
ul.ct_course_list li h6{
	color:#222;
	font-size:12px;	
	width:50%;
	float:left;
	line-height:normal;
	text-align:left;
	font-family: 'Open Sans', sans-serif;
	font-weight:600;
}
ul.ct_course_list li span{
	color:#777777;
	float:left;
	font-size:12px;
	width:50%;
	text-align:center;
	line-height:normal;	
}
/*
	  ==============================================================
		   Comming Soon Wrap Style
	  ==============================================================
*/
.comming_soon_counter{
	float:left;
	width:100%;
	position:relative;
	padding:40px 0px;
	text-align:center;
	border:2px solid #fff;
	margin:0px 0px 30px;	
}
.comming_soon_counter h4{
	color:#fff;
	text-transform:uppercase;
	margin:0px 0px 10px;
}
.comming_soon_counter h3{
	color:#fff;
	text-transform:uppercase;
	font-weight:600;
	margin:0px 0px 20px;	
}
.comming_soon_counter ul.countdown li{
	width:23%;
	display:inline-block;	
}
.comming_soon_counter ul.countdown li span{
	background-color:#fff;
	color:#1c1c1c;
	display:inline-block;
	width:70px;
	height:70px;
	border-radius:100%;
	line-height:70px;
	font-size:30px;	
	margin:0px 0px 25px;
}
.comming_soon_counter ul.countdown li p{
	color:#fff;
	font-size:20px;
	text-transform:capitalize;	
}
.search_filter{
	float: left;
	width: 100%;
	position: relative;
	margin: 0px 0px 30px;
}
.search_filter h4{
	font-size: 24px;
	text-transform: uppercase;
	display: inline-block;
	color: #222222;
}
.filter_list{
	float: right;
	width: auto;
	position: relative;
}
.filter_list .chosen-container{
	width:200px !important;
	margin-right: 10px;	
}
.filter_list .chosen-container-single .chosen-single div b::before{
	top:6px;	
}
.filter_list .chosen-container .chosen-results li{
	font-size:14px;
}
.filter_list .chosen-container .chosen-results li.higlighted,
.filter_list .chosen-container .chosen-results li:hover{
	color:#fff;	
}
.filter_list .chosen-container-single .chosen-single{
	background:none;
	border: 1px solid #d4d4d4;
	border-radius:0px;
	box-shadow:none;
	height:37px;
	padding:5px 15px;	
}
.filter_list .chosen-container .chosen-drop{
	background:#fff;
	border:1px solid #e4e4e4;
	box-shadow:none;	
}
.chosen-container-single.chosen-with-drop .chosen-single div{
	background-color:#eaeaea;	
}
.chosen-container-single.chosen-with-drop .chosen-single div b:before{
	content:"\f106";	
}
.filter_list .chosen-container-single .chosen-single span{
	color:#777;	
}
.filter_search{
	float: right;
	width: 250px;
}
.filter_search input[type="text"]{
	border: 1px solid #d4d4d4;
	height: 37px;
	width: 100%;
	padding: 8px 15px;
}
.filter_search a{
	position: absolute;top: 12px;right: 15px;
	color: #777;
	display: inline-block;
}
.ct_course_review span.new_author{
	float: left;
	color: #fff;
	font-size: 12px;
	padding-left: 30px;
	margin: 0px;
}
.ct_course_review span.new_author i{
	margin-right: 5px;
}
.ct_course_review ul.new-section{
	float: right;
	width: auto;
}
.ct_course_review ul li{
	display: inline-block;
	padding-right: 20px;
	position: relative;
}
.ct_course_review ul li span.new_author{
	padding-left: 0px;
}
.ct_course_meta > a{
	display: inline-block;
}
.ct_course_meta a i{
	margin-right: 5px;
	text-transform: capitalize;
	font-size: 15px;
}
.ct_course_meta.border{
	border:0px;
}
.table-style th:nth-of-type(1), td:nth-of-type(1){
	color: #333 !important;
}
.side-bar{margin-top:15px;}
.side-bar ul li{list-style-type:none; display:block; background:#284160; border-bottom:1px solid #395475;}
.side-bar ul li:hover{background:#345379; }
.side-bar ul li a{color:#fff; font-size:14px;  padding:10px 20px; width: 100%; display:block;}
.top-15{margin-top:15px;}
.bottom-15{ margin-bottom:15px; min-height: 298px;}
.implnk h3{font-size:15px; font-weight:bold; color:#333333;}
.carousel-inner{
	overflow: visible !important;
}

/*--------------------------------------------*/

.text-right{
	text-align:right;
	/*margin-top: 15px;*/
}
.news-li{
    line-height: 30px;
	margin-top:15px;
	border-bottom: 1px dotted #ccc;
}
.news-li .heading{
	color: #95103e;
    font-weight: bold;
	text-transform:uppercase;
}
.news-li .document-link a{
	color: #0020d2;
}
.bullet-ul ul li::before {
    content: "\f0da";
    /*position: absolute;*/
    font-family: fontawesome;
    left: 0px;
    top: 1px;
    font-size: 14px;
    color: #95103e;
    width: auto;
    height: auto;
	padding-right: 10px;
}
.bullet-ul ul li{
	padding-bottom: 15px;
}
/*.bullet-ul ul li{
    list-style: decimal;
    padding-left: 10px;
}*/
.innerList li:before{
	padding-right: 5px;
}
.innerList li {
    display: block !important;
    font-weight: normal !important;
    padding-left: 5px !important;
    color: #0c0c0c !important;
    line-height: 1.7;
    margin: 5px 0;
    font-size: 14px !important;
}
.pagination_link {
    float: right;
}
#page_links {
    background-color: #f4c62b;
    border: 1px solid #000000;
    font-size: 18px;
    padding: 0 10px;
}
#page_a_link {
    font-family: "Open Sans";
    font-size: 12px;
    border: 1px #000000 solid;
    color: #fff;
    background-color: #2954a2;
    padding: 2px 8px;
    margin: 3px;
    text-decoration: none;
}
table.no-brdr > tbody > tr > td{
    border: none;
	padding-bottom: 10px;
	padding-top: 10px;
}
table.no-brdr > tbody > tr{
    border-bottom:1px solid #ccc;
}
table.no-brdr .attachment a {
    color: #0020d2;
}
.gallery-items .gallery_cat_img {
    background-position: center center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    max-width: 100%;
    min-height: 300px;
}
.sec-top {
    margin-top: 15px;
}
.gallery-item {
    border: 1px solid #ddd;
    margin-bottom: 20px;
    padding: 0 0 0px;
}
.folio-info-gallery {
    height: 100%;
    margin-top: 20px;
    min-height: 50px;
	text-align: center;
}

.b-wrapper {
    background: rgba(0, 0, 0, 0.7);
    position: absolute;
    width: 100%;
    height: 100%;
    bottom: 0%;
    left: 0;
	-webkit-transition: .5s all;
	-moz-transition: .5s all;
    transition: .5s all;
	-webkit-transform: scale(0);
	-moz-transform: scale(0);
    transform: scale(0);
	-o-transform: scale(0);
	-ms-transform: scale(0);
	padding:0 15px;
}
.gallery-items a:hover .b-wrapper{
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
    transform: scale(1);
	-o-transform: scale(1);
	-ms-transform: scale(1);
}
.b-wrapper h5 {
    /*font-size: 2.5em;*/
    color: #fff;
    text-align: center;
    padding: 30% 0;
    font-weight: 200;
}
img.zoom-img{
	-webkit-transform: scale(1, 1);
    transform: scale(1, 1);
	-moz-transform: scale(1, 1);
	-ms-transform: scale(1, 1);
	-o-transform: scale(1, 1);
	transition-timing-function: ease-out;
	-webkit-transition-timing-function: ease-out;
	-moz-transition-timing-function: ease-out;
	-ms-transition-timing-function: ease-out;
	-o-transition-timing-function: ease-out;
	-webkit-transition-duration: 2s !important;
	-moz-transition-duration: 2s !important;
	transition-duration: 2s !important;
}
img.zoom-img:hover,.portfolio-grids:hover img.zoom-img{
	-webkit-transform: scale(1.2);
	transform: scale(1.2);
	-moz-transform: scale(1.2);
	-ms-transform: scale(1.2);
	-o-transform: scale(1.2);
	overflow: hidden;
}
.resp-tabs-list {
    list-style: none;
    padding: 0 0 3em;
    margin: 0 auto;
    text-align: center;
    display: none;
}
h2.resp-accordion {
    cursor: pointer;
    padding: 5px;
    display: none;
}
.resp-tab-active {
    text-shadow: none;
    color: #f4792c;
    border-bottom-color: #f4792c;
}
.portfolio-grids a {
    display: block;
    overflow: hidden;
    position: relative;
}
.portfolio-grids {
    padding: 0;
}
.portfolio-grids img.img-responsive {
    width: 100%;
}
.gt_aside_category ul{
	margin-bottom: 30px;
}
.extra-tbl table tr td{
	border:none;
	background-color: #fff;
	color: #0c0c0c !important;
}
ol.pdisol li {
    list-style: decimal !important;
	padding-left: 10px;
}

.custom-modal .modal-header{
    border: none;
    box-shadow: none;
    text-align: left;
    color:#fff;
    padding: 10px;
}
.custom-modal .modal-header h4.modal-title{
    font-size: 20px;
}
.custom-modal .modal-content{
    width: 100%;
	max-width: 800px;
	margin: auto;
	padding: 0;
}
.custom-modal .modal-body{
    display: inline-block;
    position: relative;
    padding-bottom: 321px;
    width: 100%;
}
.custom-modal iframe{
    width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}
.youtubevideo img.embedurl{
	max-width: 100%;
}
.youtubevideo{
    position: relative;
}
.youtubevideo::before, .youtubelistvideo::before{
    content: "";
    position: absolute;
    background:#fff;
    left: 50%;
    top: 50%;
    height: 30px;
    width: 30px;
    margin-left: -12px;
    margin-top: -30px;
}
.youtubevideo::after, .youtubelistvideo::after{
    content: "\f16a";
    font-family: "FontAwesome";
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 50px;
    line-height: 60px;
    margin-top: -45px;
    margin-left: -30px;
    cursor: pointer;
    color:#ff0303;
}
.gallery-grid .grid-blk {
    margin-top: 20px;
    position: relative;
}

#pagination-result {
    float: left;
    width: 100%;
    position: relative;
    margin-bottom: 20px;
}
#pagination {
    float: right;
    top: -35px;
}
.first {
    border-left: 
    #bccfd8 1px solid;
}
.link {
    padding: 7px 14px;
    background: 0 0;
    border-left: 0;
    cursor: pointer;
	display: inline-block;
}
.link, .pagination-setting {
    border: 1px solid #bccfd8;
    color:#607d8b;
}
.current {
    background: #95103e;
    color:#fff;
}
.gallery-grid, .page-blk {
    float: left;
    width: 100%;
}
.main-result{
	clear: both;
}
ol.academic-list li {
    list-style: decimal !important;
}
.main_navigation ul ul li a:hover, .main_navigation ul ul ul li a:hover {
    background: #2f2b2b;
}