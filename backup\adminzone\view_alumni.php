<?php 
/* ================= MAIN Incusions =================*/
//OM
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
require_once("../php_mailer/class.phpmailer.php");
@session_start();

class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $app_config;		// Application Configuration Settings
	public $module_name;	// Current Module Name
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		//global Config Settings
		$this->thisPage = CommonFunctions::curPageName();
		//--- Connect To Database --- 
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->module_name = "View Alumni Details";
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
	}
	
	function __destruct()
	{
		
	}	
 }
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script type="text/javascript" src="js/jquery-1.10.2.js"></script>
<script type="text/javascript" src="js/jquery-ui.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />

<link href="css/colorbox.css" rel="stylesheet" />
<link href="css/popbox.css" rel="stylesheet" type="text/css" />
<link href="css/accrodiantree.css" rel="stylesheet" />
<!--<script type="text/javascript" src="../js/jquery-1.10.1.min.js"></script>-->
<script type="text/javascript" src="js/jquery-ui-1.10.2.custom.min.js"></script>
<script type="text/javascript" src="js/webwidget_vertical_menu.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<script type="text/javascript" src="js/jquery.colorbox.js"></script>

<style type="text/css">
body
{
	background-color:#F9F9F9;
}
.inner_content_area p{
	font-size:14px;
	line-height:22px;
	padding:10px 0;
}
	
.tbl_cont{
	font-size:14px;
	line-height:20px;
}
	
a.generalink:link
{
	color:#990000;
	font-size:14px;
	
}
a.generalink:active
{
	color:#990000;
	font-size:14px;
}
a.generalink:visited
{
	color:#990000;
	font-size:14px;
}
a.generalink:hover
{
	color:#0033CC;
	font-size:14px;
	text-decoration:none;
}


#box2{
	float: left;
	width: 100%;
}

#box2_top_black{background:url("../images/box2_top_bg_b.jpg") repeat-x top left; height:27px;}

#box2 .top_right_black {
    background: url("../images/box2_top_r_b.jpg") no-repeat scroll right top transparent;
    color: #FFA823;
    display: block;
    font-weight: bold;
    height: 27px;
    line-height: 27px;
    text-indent: 40px;
}

#box2 .top_left_black{
	background:url("../images/box2_top_l_b.jpg") no-repeat top left;
	height:27px;
	display: block;}

</style>
</head>

<body>
<?php  
	if(isset($_GET['alumni_id']))
	 {
		$sql="SELECT * FROM tbl_alumni WHERE alumni_id=". $_GET['alumni_id'];
		 $rs = $objCurPage->link_id->query($sql);
		if((!$rs) || (!($rec = $rs->fetch_array())))
		{
				
		}
		else{
      ?>			
											
    <table width="700" align="center" cellpadding="3" cellspacing="1" class="tbl_cont formTextWithBorder" bgcolor="CCCCCC">																				
       <tr>
           <td colspan="6" class="pageHeader" style="text-align:left;">
             <tr class="inner formHeadingBkg" valign="top">
                <td colspan="6" align="left"><strong>Alumni Details</strong></td>
             </tr>										
           </td>
        </tr>	
        <tr>&nbsp;</tr>	
        <?php if($rec["alumni_photo"] !="") { ?>
        <tr><td colspan="6" align="left"><img src="<?php echo '../alumni_photo/'.$rec["alumni_photo"] ; ?>" width="100" height="100" /> </td>
      </tr>
      <?php }?>
        
        <?php if($rec["email"]!="" || $rec["reg_no"]!="") 
        {?>
        <tr class="inner">
            <td width="29%" class="label" align="left">Email</td>
            <td width="2%" align="left">:</td>
            <td width="22%" align="left"><?php echo $rec["email"];?></td>
            <?php if($rec["reg_no"]!=""){?>
            <td width="29%" class="label" align="left">Registrtion No.</td>
            <td width="2%" align="left">:</td>
            <td width="16%" align="left"><?php echo $rec["reg_no"]; ?></td>
            <?php } ?>
        </tr>
        <?php }  if($rec["first_name"]!="") 
        {?>
        <tr class="inner">
            <td class="label" align="left">Full Name</td>
            <td align="left">:</td>
            <td align="left"><?php if($rec["first_name"] !="")
                {$fullname=$rec["first_name"];}
                if($rec["middle_name"] !="")
                {$fullname.=" ".$rec["middle_name"];}
                if($rec["last_name"] !="")
                {$fullname.=" ".$rec["last_name"];} 
                echo $fullname; ?></td>
            <td align="left" class="label">Gender</td>
            <td align="left">:</td>
            <td align="left"><?php if($rec["gender"]=='M')
                  echo "Male";
                  else
                  echo "Female"; ?></td>
        </tr>
        <?php }  if($rec["date_of_birth"]!="") 
        {?>
        <tr  class="inner">
            <td class="label" align="left">Date of Birth</td>
            <td align="left">:</td>
            <td align="left"><?php echo CommonFunctions::formatMyDateTime($rec["date_of_birth"],"d-m-Y"); ?></td>
            <td align="left">&nbsp;</td>
            <td align="left">&nbsp;</td>
            <td align="left">&nbsp;</td>
        </tr>
        <?php } if($rec["family_info"]!="")
        {?>
        <tr class="inner">
            <td class="label" align="left">Family Details</td>
            <td align="left">:</td>
            <td align="left"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["family_info"], 1)) ; ?></td>
            <td align="left">&nbsp;</td>
            <td align="left">&nbsp;</td>
            <td align="left">&nbsp;</td>
        </tr>
        <?php } if($rec["stream"]!="")
        {?>
        <tr class="inner">
            <td class="label" align="left">Stream</td>
            <td align="left">:</td>
            <td align="left"><?php echo $rec["dept"]; ?></td>
            <td align="left" class="label">Year of Passing</td>
            <td align="left">:</td>
            <td align="left"><?php echo $rec["year_of_passing"]; ?></td>
        </tr>
        <?php } if($rec["qualification"]!="" || $rec["membership"]!="")
        {?>
        <tr class="inner">
            <td class="label" align="left">Additional Qualification</td>
            <td align="left">:</td>
            <td align="left"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["qualification"], 1)) ; ?></td>
            <td align="left" class="label">Professional Membership</td>
            <td align="left">:</td>
            <td align="left"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["membership"], 1)) ;?></td>
        </tr>
        <?php } if($rec["expertise"]!="")
        {?>
        <tr class="inner">
            <td class="label" align="left">Hobby</td>
           <td align="left">:</td>
             <td align="left"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["expertise"], 1)) ; ?></td>
             <td align="left">&nbsp;</td>
             <td align="left">&nbsp;</td>
             <td align="left">&nbsp;</td>
        </tr>
        <?php } if($rec["street"]!="" && $rec["city"]!="")
        {?>
        <tr class="inner">
            <td height="22" align="left" class="label">Street</td>
           <td align="left">:</td>
             <td align="left"><?php  echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["street"], 1)) ;?></td>
             <td align="left" class="label">City</td>
             <td align="left">:</td>
             <td align="left"><?php  echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["city"], 1)) ; ?></td>
        </tr>
        <?php } if($rec["state"]!="" && $rec["country"]!="")
        {?>
        <tr class="inner">
             <td class="label" align="left">State</td>
             <td align="left">:</td>
             <td align="justify"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["state"], 1)) ;?></td>
             <td align="justify" class="label">Country</td>
             <td align="justify">:</td>
             <td align="justify"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["country"], 1)) ;  ?></td>
        </tr>
        <?php } if($rec["per_address"]!="" || $rec["office_phone"]!="")
        {?>
        <tr class="inner">
             <?php if($rec["per_address"]!=""){?>
             <td class="label" align="left">Permanent Address</td>
             <td align="left">:</td>
             <td align="justify"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["per_address"], 1)) ;  ?></td>
             <?php } ?>
             <?php if($rec["office_phone"]!=""){?>
             <td align="justify" class="label">Phone</td>
             <td align="justify">:</td>
             <td align="justify"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["office_phone"], 1)) ; ?></td>
             <?php } ?>
        </tr>
        <?php } if($rec["mobile_phone"]!="")
        {?>
          <tr class="inner">
            <td class="label" align="left">Mobile</td>
           <td align="left">:</td>
             <td align="left"><?php 
                   echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["mobile_phone"], 1)) ; 		
            ?></td>
             <td align="left">&nbsp;</td>
             <td align="left">&nbsp;</td>
             <td align="left">&nbsp;</td>
        </tr>
        <?php }  if($rec["institute_alumni"]!="")
        {?>
          <tr class="inner">
            <td class="label" align="left">Institute/Alumni Affairs</td>
           <td align="left">:</td>
             <td align="justify"><?php echo html_entity_decode(CommonFunctions::replaceCharsRev($rec["institute_alumni"], 1)) ; ?></td>
             <td align="justify">&nbsp;</td>
             <td align="justify">&nbsp;</td>
             <td align="justify">&nbsp;</td>
        </tr>
         <?php }  if($rec["alumni_photo"]!="")
        {?>
        <?php } ?>
 </table>	
							  
		<?php	 
		}
	 }	
	 ?>

</body>
</html>
