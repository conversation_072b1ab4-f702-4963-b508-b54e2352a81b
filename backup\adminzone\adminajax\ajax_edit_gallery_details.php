<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	
	public $app_config;
	
	public $arr_image_text;
	public $arr_img_id,$arr_img_upload;
	
	public $cat_id;
	public $image_id;
	public $image_title;
	public $upload_image;
	public $path_gallery_photo;
	public $upload_image_arr;
	public $arr_image_title;

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
			
		//--- Set Current Module Name ---
		$this->module_name = "Manage Gallery Details";
		$this->cur_page_url = "ajax_edit_gallery_details.php";
		$this->list_page_url = "ajax_edit_gallery_details.php";
						
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
		
		$this->path_gallery_photo = "../../gallery_images/";
					
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		$this->maxfilesize = 2*1024*1024;
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
			    $this->createGallery();
				break;
			case 2:
				//--- Edit Record
				$this->editGallery();
				break;
			case 3:
				//--- Edit Record
				$this->deleteGallery();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			if(isset($_GET["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->cat_id = 0;
			}
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->image_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->image_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->image_id = 0;
			}
			
		}
		
		return 1;
	}


	//==== Create New Record ====
	public function createGallery()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editGallery()
	{
		
		//--- Get Record Id ---
		$this->getRecordId();
         	
		if ($this->image_id == 0)
		{
			$_SESSION['app_error'] = "Record not found";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{	
		$this->arr_image_file = array();
		$this->arr_image_title = array();
		$this->image_id = "";
					
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{						
		if($this->task == 2)
		{
			$this->cat_id = $_POST["cat_id"];
			
			$tSql = "SELECT image_id, cat_id, image_title, upload_image FROM tbl_photo_gallery WHERE cat_id = ".$this->cat_id." ORDER BY image_id DESC";
						
			$rs = $this->link_id->query($tSql);
			
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				
			}
			else
			{
				$i = 1;
				do{
					 $this->arr_img_id[$i] = $rec["image_id"];
					 $this->arr_img_upload[$i] = $rec["upload_image"];
					 
				 	$i++;
				  }while($rec = $rs->fetch_array());
			}
			
			for($x=1;$x<=count($this->arr_img_id);$x++)
			{
				$this->arr_image_title[$x] = $_POST["image_title_".$this->arr_img_id[$x]];
			}
			
		}
		
		if($this->task == 1)
		{
			$i = 0;
			foreach($_POST["gallery_photo_HX"] as $val)
			{
				if(!empty($val))
				{
					if($i > 0)
					{
						$this->arr_image_file[$i] = $val;
					}
				}
				$i++;
			}		
		}
				
		if(isset($_POST["image_title"]))
		{
			$i = 0;
			foreach($_POST["image_title"] as $val)
			{				
				$this->arr_image_title[$i] = $val;				
				$i++;
				
			}
		}	
								
		return 1;
	}
	

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";
		$file_ext = "jpg,gif,jpeg,png";
		
		if($myerr == "" && $this->task == 2)	
		{								
			for($i=1; $i<=count($this->arr_img_id); $i++)
			{			  
			   if(!empty($_FILES['gallery_photo_' . $i]['name']))
				{
					if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['gallery_photo_' . $i]['name']), $file_ext) != 1)
					{
						$myerr = "Un-supported file format of photo, please upload files type - " . $file_ext;
						break;
					}
					else if(($_FILES['gallery_photo_' . $i]['size'] > $this->maxfilesize))
					{
						$myerr = "Photo file size should not be more than 2MB";
						break;
					}
				}				
			}
		}	
		
		if($myerr == "" && $this->task == 1)	
		{								
			for($i=1; $i<=count($this->arr_image_file); $i++)
			{	
			   if($this->arr_image_title[$i] == "")
			   {
			   		$myerr = "Please specify photo title".$i;
					break;
			   }
			   if(empty($_FILES['gallery_photo_' . $i]['name']) && $this->task == 1)
				{
					$myerr = "Please upload photo";
					break;
				}
				 if(!empty($_FILES['gallery_photo_' . $i]['name']))
				{
					if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['gallery_photo_' . $i]['name']), $file_ext) != 1)
					{
						$myerr = "Un-supported file format of photo, please upload files type - " . $file_ext;
						break;
					}
					else if(($_FILES['gallery_photo_' . $i]['size'] > $this->maxfilesize))
					{
						$myerr = "Photo file size should not be more than 2MB";
						break;
					}
				}				
			}
		}	
			
			
		if($myerr != "")
		{
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
		
	public function uploadImage()
	{	
	   
		for($i=1; $i<=count($this->arr_image_file); $i++)
		{
		    if(isset($_FILES["gallery_photo_" . $i]["name"]) && $_FILES["gallery_photo_" . $i]["name"] !=""){ 
			$photo="";
								
			$ext = strtolower(substr(strrchr($_FILES["gallery_photo_".$i]["name"], "."), 1));
			$name = strtolower(substr($_FILES["gallery_photo_".$i]["name"], 0, strpos($_FILES["gallery_photo_".$i]["name"], ".")));
			$path = $this->path_gallery_photo . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
			
			$medium_path = $this->path_gallery_photo ."medium_". str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
							
			$small_path = $this->path_gallery_photo ."thumb_". str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
			$photo = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
								
			move_uploaded_file($_FILES["gallery_photo_".$i]["tmp_name"], $path);
			$resizeObj = new resize($path);		
				
			$resizeObj -> resizeImage(600, 600 ,'auto');
			$resizeObj -> saveImage($path, 100);
			
			$resizeObj -> resizeImage(125, 125 ,'crop');
			$resizeObj -> saveImage($medium_path, 100);
			
			$resizeObj -> resizeImage(100, 100 ,'crop');
			$resizeObj -> saveImage($small_path, 100);
			
			//--- Insert Data
			$tmpSql = "INSERT INTO tbl_photo_gallery(image_id, cat_id, image_title, upload_image)" 
					. " VALUES(null, " . $this->cat_id
					. ", '".$this->arr_image_title[$i]."','" . $photo . "'"
					. ")";
							
		    $rs = $this->link_id->query($tmpSql);			
			
			if($this->link_id->affected_rows == -1)
			{								
				break;
			}			
		  }
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction		
		$this->link_id->autocommit(FALSE);
		
		if($this->task == 1)
		{		
			$this->uploadImage();
							
			$this->link_id->commit();					
			$_SESSION["app_message"] = "Data successfully saved";
			
			echo '1';
				
			
		}
		else if($this->task == 2)
		{	
				
				for($x=1;$x<=count($this->arr_img_id);$x++)
				{										
					if( isset($_FILES["upload_img_".$this->arr_img_id[$x]]) )
					{
						
						if($this->arr_img_upload[$x] != "")
						{
							if(file_exists($this->path_gallery_photo.$this->arr_img_upload[$x]))
							{
								@unlink($this->path_gallery_photo.$this->arr_img_upload[$x]);
								@unlink($this->path_gallery_photo."medium_".$this->arr_img_upload[$x]);
								@unlink($this->path_gallery_photo."thumb_".$this->arr_img_upload[$x]);
							}
						}
												
							if(isset($_FILES["upload_img_" . $this->arr_img_id[$x]]["name"]) && $_FILES["upload_img_" . $this->arr_img_id[$x]]["name"] !="")
							{ 
								$photo="";
													
								$ext = strtolower(substr(strrchr($_FILES["upload_img_".$this->arr_img_id[$x]]["name"], "."), 1));
								$name = strtolower(substr($_FILES["upload_img_".$this->arr_img_id[$x]]["name"], 0, strpos($_FILES["upload_img_".$this->arr_img_id[$x]]["name"], ".")));
								$path = $this->path_gallery_photo . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
								
								$medium_path = $this->path_gallery_photo ."medium_". str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
								
								$small_path = $this->path_gallery_photo ."thumb_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
												
								$photo = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
													
								move_uploaded_file($_FILES["upload_img_".$this->arr_img_id[$x]]["tmp_name"], $path);
								$resizeObj = new resize($path);		
									
								$resizeObj -> resizeImage(600, 450 ,'auto');
								$resizeObj -> saveImage($path, 100);
								
								$resizeObj -> resizeImage(125, 125 ,'crop');
								$resizeObj -> saveImage($medium_path, 100);
								
								$resizeObj -> resizeImage(120, 90 ,'crop');
								$resizeObj -> saveImage($small_path, 100);
																						
						  }
												  
						  $sqlUpdt = "UPDATE tbl_photo_gallery SET image_title = '".$this->arr_image_title[$x]
						  ."', upload_image = '".$photo."' WHERE cat_id = ".$this->cat_id." AND image_id = ".$this->arr_img_id[$x];
						 						  
						  $rs = $this->link_id->query($sqlUpdt);
						   
					}
					else
					{
						
						$sqlUpdt = "UPDATE tbl_photo_gallery SET image_title = '".$this->arr_image_title[$x]."' WHERE image_id = ".$this->arr_img_id[$x];
						
						$rs2 = $this->link_id->query($sqlUpdt);
					}
				}
				
				$this->link_id->commit();		
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();				
			
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
	
	//Delete Case Image
	public function deleteGallery()
	{
		
	   if($this->task == 3)
		{
			if(isset($_GET["cat_id"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_GET["cat_id"], 0);
			}
			else if(isset($_POST["cat_id"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_POST["cat_id"], 0);			
			}
			else
			{
				$this->cat_id = 0;
			}
			
			if(isset($_GET["image_id"]))
			{
				$this->image_id = CommonFunctions::replaceChars($_GET["image_id"], 0);
			}
			else if(isset($_POST["image_id"]))
			{
				$this->image_id = CommonFunctions::replaceChars($_POST["image_id"], 0);			
			}
			else
			{
				$this->image_id = 0;
			}
			
		}
		if($this->cat_id!= 0 && $this->image_id != 0)
		{	
			$this->deleteRecord();
		}
	}
	
	public function deleteRecord()
	{
		$image_name = DatabaseManager::igetDataFromTable($this->link_id, "tbl_photo_gallery", "upload_image", "", "cat_id = ".$this->cat_id." AND image_id=".$this->image_id);
		if($image_name != "")
		{
			if(file_exists($this->path_gallery_photo.$image_name))
			{
				@unlink($this->path_gallery_photo.$image_name);
				@unlink($this->path_gallery_photo."medium_".$image_name);
				@unlink($this->path_gallery_photo."thumb_".$image_name);
			}
		}
				
		$this->link_id->autocommit(FALSE);
		
		
		$tmpSql = "DELETE FROM tbl_photo_gallery WHERE cat_id = ".$this->cat_id." AND image_id = ".$this->image_id;
		$rs = $this->link_id->query($tmpSql);
		
		if($this->link_id->affected_rows == -1)
		{
			$this->link_id->rollback();
			echo "0";			
			exit;			
		}	
		else
		{
			
			//--- Commit Transaction
			$this->link_id->commit();
			echo "1";			
			exit;
		}	
		
	}
	
}

$objCurPage = new CurrentPage();

?>