<?php
//print_r($_POST); die();
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $faculty_id;
	public $dept_id;
	public $faculty_name;
	public $faculty_qual;
	public $faculty_email;
	public $upload_file;
	public $path_upload_file;
	public $arr_faculty_file;
	public $arr_faculty_name, $arr_faculty_qual,$arr_faculty_email, $faculty_name_edit, $faculty_qual_edit, $faculty_email_edit;
	
			
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		
		//--- Set Current Module Name ---
		$this->module_name = "Ajax Faculty Details";
		$this->cur_page_url = "ajax_faculty_details.php";
		$this->list_page_url = "ajax_faculty_details.php";
				
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
			
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		$this->path_upload_file = "../../faculty_images/";
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		$this->maxfilesize = 2*1024*1024;
								
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createNews();
				break;
			case 2:
				//--- Edit Record
				$this->editNews();
				break;
			default:
				$this->task = 0;
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
			
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->faculty_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->faculty_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->faculty_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->faculty_id = 0;
			}
		}
		
		//echo $this->faculty_id;die("tendetid");
		
		return 1;
	}


	//==== Create New Record ====
	public function createNews()
	{
	
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editNews()
	{
		//--- Get Record Id ---
		$this->getRecordId();
         	
		if ($this->faculty_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		$this->arr_faculty_name = array();
		$this->arr_faculty_qual = array();
		$this->arr_faculty_email = array();
		$this->arr_faculty_file = array();
		$this->faculty_id = "";	
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{ 	
	   if(isset($_POST["faculty_name"]))
		{
			$i = 0;
			foreach($_POST["faculty_name"] as $val)
			{	
				$this->arr_faculty_name[$i] = CommonFunctions::replaceChars($val, 0);		
				$i++;
			}
		}
		
		if(isset($_POST["faculty_qual"]))
		{
			$i = 0;
			foreach($_POST["faculty_qual"] as $val)
			{	
				$this->arr_faculty_qual[$i] = CommonFunctions::replaceChars($val, 0);		
				$i++;
			}
		}
		
		if(isset($_POST["faculty_email"]))
		{
			$i = 0;
			foreach($_POST["faculty_email"] as $val)
			{	
				$this->arr_faculty_email[$i] = CommonFunctions::replaceChars($val, 0);		
				$i++;
			}
		}
		
		//print_r($this->arr_news_date);exit();
		
		
		if($this->task == 1)
		{
			$i = 0;
			foreach($_POST["faculty_img_HX"] as $val)
			{
				if(!empty($val))
				{
					if($i > 0)
					{
						$this->arr_faculty_file[$i] = $val;
					}
				}
				$i++;
			}	
				
		}
		
		if($this->task == 2){
			
			$this->faculty_name_edit = CommonFunctions::replaceChars($_POST["faculty_name_edit"], 0);
			$this->faculty_qual_edit = CommonFunctions::replaceChars($_POST["faculty_qual_edit"], 0);
			$this->faculty_email_edit = CommonFunctions::replaceChars($_POST["faculty_email_edit"], 0);
			
			if(isset($_POST["faculty_img_edit"]))
			{
				$this->faculty_img_edit = $_POST["faculty_img_edit"];			
			}	
			
		}
		
		$this->dept_id = $_POST["dept_id"];					
		return 1;
	}	

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";
		$file_ext = "jpg,jpeg,png,gif";
		
		if($this->dept_id == "")
		{
			$myerr = "Please Select Department Name";
		}
		
		if($this->task == 1){
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->arr_faculty_name); $x++)
				{
					if($this->arr_faculty_name[$x] == "")
					{
						$myerr = "Please specify name -".$x;
						break;
					}
				}
				
			}
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->arr_faculty_qual); $x++)
				{
					if($this->arr_faculty_qual[$x] == "")
					{
						$myerr = "Please specify qualification -".$x;
						break;
					}
				}
				
			}
					
			if($myerr == "")	
			{								
				for($i=1; $i<=count($this->arr_faculty_file); $i++)
				{			  
				   /*if(empty($_FILES['faculty_img_' . $i]['name']) && $this->task == 1)
					{
						$myerr = "Please upload photo -".$i;
					}*/			   
					 if(!empty($_FILES['faculty_img_' . $i]['name']))
					 {
						if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['faculty_img_' . $i]['name']), $file_ext) == 0)
						{
							$myerr = "Un-supported file format , please upload files type - " . $file_ext;
						}
						else if(($_FILES['faculty_img_' . $i]['size'] > $this->maxfilesize))
						{
							$myerr = "File size should not be more than 2MB";
							break;
						}
					}	
					
				}
			}
			
	    }
		
		if($this->task == 2){
			
			if($myerr == "")	
			{
					if($this->faculty_name_edit == "")
					{
						$myerr = "Please specify name";
					}
			}
			
			if($myerr == "")	
			{
					if($this->faculty_qual_edit == "")
					{
						$myerr = "Please specify qualification";
					}
				
			}
	    }

		if($myerr != "")
		{
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction 
		$this->link_id->autocommit(FALSE);
			
		if($this->task == 1)
		{
			//print_r($this->arr_faculty_name); die('ee');
			for($i=1; $i<count($this->arr_faculty_name); $i++)
			{
				if(isset($_FILES["faculty_img_" . $i]["name"]) && $_FILES["faculty_img_" . $i]["name"] !="")
				{   
					$file_name="";
					$ext = strtolower(substr(strrchr($_FILES["faculty_img_" . $i]["name"], "."), 1));
					$name = strtolower(substr($_FILES["faculty_img_" . $i]["name"], 0, strpos($_FILES["faculty_img_" . $i]["name"], ".")));
					
					$path = $this->path_upload_file . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
					//$medium_path = $this->path_upload_file ."medium_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
					//$small_path = $this->path_upload_file ."thumb_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
										
					$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
										
					move_uploaded_file($_FILES["faculty_img_" . $i]["tmp_name"], $path);
					
					//--- Insert Data
					$tmpSql = "INSERT INTO tbl_faculty(faculty_id, dept_id, faculty_name, faculty_qual, faculty_email, attachmemt)" 
							. " VALUES(null, ".$this->dept_id." , '" . addslashes($this->arr_faculty_name[$i]) . "'" 
							. ", '" . addslashes($this->arr_faculty_qual[$i]) . "'"
							. ", '" . $this->arr_faculty_email[$i]. "'"
							. ", '" . $file_name . "')";
							
					//echo $tmpSql;die();					
									
					$rs = $this->link_id->query($tmpSql);
					
					if($this->link_id->affected_rows == -1)
					{								
						break;
					}			
				}
				else
				{
					
					/*print_r($this->arr_faculty_name) . "<br/>";
					print_r($this->arr_mark_new);
					die('ee');*/
				   $tmpSql = "INSERT INTO tbl_faculty(faculty_id, dept_id, faculty_name, faculty_qual, faculty_email, attachmemt)" 
							. " VALUES(null, ".$this->dept_id." , '" . addslashes($this->arr_faculty_name[$i]) . "'" 
							. ", '" . addslashes($this->arr_faculty_qual[$i]) . "'"
							. ", '" . $this->arr_faculty_email[$i]. "'"
							. ", ''";
                // echo $tmpSql; die('ss');
				  $rs = $this->link_id->query($tmpSql);	
				
			    }
			
			}//exit;
			
			if($myerr != "")
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();				
			}			
			else
			{
				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
			
		}
		else if($this->task == 2)
		{
			$upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "tbl_faculty", "attachmemt", "","faculty_id=".$this->faculty_id);
			
			//echo $this->path_upload_file.$upload_old_file;die();
		
			if(isset($_FILES['faculty_img']['name']))
			{
				if(file_exists($this->path_upload_file.$upload_old_file))
				{
					@unlink($this->path_upload_file.$upload_old_file);
				} 
				
				$this->file_upload();
			}
			
			$tmpSql = "UPDATE tbl_faculty SET " 
			        . "  dept_id = ".$this->dept_id					
					. ", faculty_name = '" . addslashes($this->faculty_name_edit) ."'"
					. ", faculty_qual = '" . addslashes($this->faculty_qual_edit) ."'"
					. ", faculty_email = '" . $this->faculty_email_edit ."'"
					. ", attachmemt = '" . $this->faculty_img_edit ."'"
					. "  WHERE faculty_id = " . $this->faculty_id;
					
		   //echo $tmpSql;die("ddd");	
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();
			}
				
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			echo '2';
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
		
  //Upload File
   public function file_upload()
   {
		if(isset($_FILES['faculty_img']['name']))
		{	
		   //echo"aaa";die();
		    $file_name="";
			$ext = strtolower(substr(strrchr($_FILES["faculty_img"]["name"], "."), 1));
			$name = strtolower(substr($_FILES["faculty_img"]["name"], 0, strpos($_FILES["faculty_img"]["name"], ".")));
			$path = $this->path_upload_file . str_replace(" ", "_", $name) . "_" .strtotime("now") . "." . $ext;
					
			$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
			
			if(move_uploaded_file($_FILES['faculty_img']['tmp_name'], $path))
			{
				$this->faculty_img_edit = $file_name;
			}
			else
			{
				$myerr = "File couldn't be uploaded";
			}
		}
		
	}
	
}

$objCurPage = new CurrentPage();
?>