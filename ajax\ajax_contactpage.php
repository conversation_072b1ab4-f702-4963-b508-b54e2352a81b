<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once('../includes/class.phpmailer.php');
session_start();

class CurrentPage
{
	public $link_id;	// Database Link
	public $module_contname;	// Current Module contname
	public $app_config;		// Application Configuration Settings
	
	public $no_menu;
	public $contname, $contmail, $contphone, $contmessage;	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		//--- Set Current Module contname ---
		$this->module_contname = "Request";
		$this->cur_page_url = "ajax_contactpage";
		$this->list_page_url = "ajax_contactpage";
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		if(!isset($_POST["postMe"]))
		{
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]))
		{
			
			$this->readFormData();
			
			if($this->validateFormData() == 0)
			{
				
			}
			else
			{				
				$this->sendToAdmin();							
			}			
		}	

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	    
	public function initFormData()
	{
		$this->contname = "";
		$this->contmail = "";
		$this->contphone = "";
		$this->contmessage = "";
	    
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{	 
	    $this->contname = CommonFunctions::replaceChars($_POST["contname"], 11);	
		$this->contmail = $_POST["contmail"];
		$this->contphone = CommonFunctions::replaceChars($_POST["contphone"], 11);
		$this->contmessage = CommonFunctions::replaceChars($_POST["contmessage"], 11);
		
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		
	    if($myerr!="")
		{			
			echo $myerr;
			return 0;
		}
		else
		{
		return 1;
		}
	}
	
	public function sendToAdmin()
	{	
	    $secretKey = $this->app_config['SECRET_KEY'];
		$token = $_POST['recaptcha_token'];	

		//$url = 'https://www.google.com/recaptcha/api/siteverify';
		$data = [
			'secret' => $secretKey,
			'response' => $token,
			'remoteip' => $_SERVER['REMOTE_ADDR']
		];
		
		$ch = curl_init('https://www.google.com/recaptcha/api/siteverify');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
		
		// Execute the POST request
		$response = curl_exec($ch);
		
		// Check for cURL errors
		if (curl_errno($ch)) {
			echo 'cURL error: ' . curl_error($ch);
			exit();
			return 0;
		}
		
		curl_close($ch);
		
		// Decode JSON response
		$captchaResponse = json_decode($response);
		 //print_r($captchaResponse); //die();

		// Check if score is acceptable (adjust threshold as needed)
		if ($captchaResponse->success && $captchaResponse->score >= 0.5) {	
            if($this->contname != "")
            {
            	
            		$message = '<table width="50%" border="0" align="center" cellpadding="2" cellspacing="1"  
            style="background-color:#DFD7C0;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px">
            		  <tr><td>&nbsp;</td></tr>
                      <tr><td align="center"><table width="95%" border="0" align="center" cellpadding="2" cellspacing="1"  
            style="background-color:#FFF;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px" >
            		  
            		   <tr>
            		   	<td colspan="4">Please find my contact details bellow:</td>
            		   </tr>
            		   <tr>
            				<td colspan="4">&nbsp;</td>
            		  	 </tr>				  
            		  <tr>
            			<td align="left" valign="top">Name</td>
            			<td align="center">:</td>
            			<td align="left">'.$this->contname.'</td>
            		  </tr>';
            		 
            		 $message .='<tr>
            			<td align="left" valign="top">E-Mail ID</td>
            			<td align="center">:</td>
            			<td align="left">'.$this->contmail.' </td>
            		 </tr>';
            		 
            		 if($this->contphone!="")
            		 {
            		 $message .= '<tr>
            			<td align="left" valign="top">Phone</td>
            			<td align="center">:</td>
            			<td align="left">'.$this->contphone.'</td></tr>';
            		 }
            		 
            		 if($this->contmessage!="")
            		 {
            		 $message .= '<tr>
            			<td align="left" valign="top">Suggestion</td>
            			<td align="center">:</td>
            			<td align="left">'.nl2br($this->contmessage).'</td></tr>';
            		 }
            		 
            		 $message .='</table>
                      </td>
                      </tr>
                       <tr><td>&nbsp;</td></tr>
                      </table>';
            		  
            		
            		$to = "<EMAIL>";
            						
            		$subject = "Enquiry - " . $this->contname;										
            		
            		$mail = new PHPMailer(true); // the true param means it will throw exceptions on errors, which we need to catch
            		
            		
            		//$mail->IsSMTP(); // telling the class to use SMTP
            
            		try {
            				
            				$mail->CharSet = 'utf-8';
            				$mail->SMTPDebug = false; // Enables SMTP debug information - SHOULD NOT be active on production servers!
            															
            				$mail->SetFrom("<EMAIL>", $this->contname);
            				
            				$mail->AddReplyTo($this->contmail, $this->contname);
            				$mail->AddAddress($to, '');
            											
            				$mail->Subject = $subject;
            				$mail->AltBody = 'To view the contmessage, please use an HTML compatible contmail viewer!'; // optional - MsgHTML will create an alternate automatically
            				$mail->MsgHTML($message);
            															
            				$mail->Send();
            			}			
            			catch (phpmailerException $e)
            			{
            			  echo $e->errorMessage(); //Pretty error contmessages from PHPMailer
            			  exit();
            			  return 0;
            			} 
            			catch (Exception $e)
            			{
            			  echo $e->getMessage(); //Boring error contmessages from anything else!
            			  exit();
            			  return 0;
            			}
            						
            			 echo "1";
            			 exit();
              }
		}
		else{
		  echo "Invalid Request"; 
		  exit();
		}
	}
}

$objCurPage = new CurrentPage();

?>