<?php 
error_reporting(E_ALL ^ E_DEPRECATED);
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
require_once("../php_mailer/class.phpmailer.php");
@session_start();
/*$inactive = 1200; // Set timeout period in seconds

if (isset($_SESSION['timeout'])) {
    $session_life = time() - $_SESSION['timeout'];
    if ($session_life > $inactive) {
        session_destroy();
        header("Location: index.php");
    }
}
$_SESSION['timeout'] = time();*/

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	
	public $app_config;		// Application Configuration Settings
	
	
	public $news_name;
	public $tmp_pscat_image;
	public $tmp_file;
	
	public $first_name;
	
	public $search_condition;
	
	public $alumni_id;
	
	public $del_ids;
	
	public $alumni_photo;
	
	public $path_alumni_image;
	
	
	public $members_search;
	public $registration_no_search;
	public $txt_email_search;
	public $first_name_search;
	public $middle_name_search;
	public $last_name_search;
	public $txt_city_search;
	public $txt_state_search;
	public $txt_country_search;
	public $gender_search;
	//public $stream_search;
	public $year_of_passing_search;
	public $year_of_passing_report;
	public $dept;
	
	public $upgrade_ids;
	
	public $admin_mail;
	public $status;
	public $active;
	public $show;
	

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Manage Alumni";
		$this->cur_page_url = "alumni.php";
		$this->list_page_url = "home.php";

		//--- Check User Access permissions	
		UserAccess::checkUserLogin("", "admin");
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);/**/
		$this->sortmode = CommonFunctions::getSortDirection("desc");
		
		//--- Get Record Position (crec) passed to this page ----
		$this->reccnt = 20;
		$this->getRecordPostion();

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		$this->maxfilesize = 2*1024*1024;
		
		//--- Initialize Special Properties ---
		$this->initSpecialProperties();
		
		//--- Generate Search Condition ---
		$this->generateSearchCondition();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				//$this->createPart();
				break;
			case 2:
				//--- Edit Record
				//$this->editPart();
				break;
			case 3:
				//--- Delete Record
				$this->deletePart();
				break;
			case 4:
				//--- Alumni Status
				$this->alumniStatus();
				break;
			case 5:
				//--- Alumni Status
				$this->alumniShowinpage();
				break;	
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
		}
		
		if(isset($_POST["cmdDeleteAlumni"]) && !(isset($_POST["alumni_id"])))
		{
			$_SESSION["app_error"] = "No record selected";
		}

		if(isset($_POST["cmdDeleteAlumni"]) && isset($_POST["alumni_id"]))
		{																													
			$this->del_ids =  implode(",", $_POST["alumni_id"]);
			$this->deleteAlumniRecords();
		}
		
		if(isset($_POST["cmdApprove"]) && !(isset($_POST["alumni_id"])))
		{
			$_SESSION["app_error"] = "No record selected";
		}

		if(isset($_POST["cmdApprove"]) && isset($_POST["alumni_id"]))
		{
			$this->upgrade_ids =  implode(",", $_POST["alumni_id"]);
			$this->upgradeAlumniRecords();
		}			
	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Record Position (crec) passed to this page ====
	public function getRecordPostion()
	{
		if(isset($_POST["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_POST["crec"], 0));}
		else if(isset($_GET["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_GET["crec"], 0));}
		else
			{$this->crec=0;}
		return 1;
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		return 1;
	}

    //==== Get Current Record Id ====
	public function getRecordId()
	{
		if(isset($_GET["recid"]))
		{
			$this->alumni_id = intval(CommonFunctions::replaceChars($_GET["recid"], 0));
		}
		else if(isset($_POST["recid"]))
		{
			$this->alumni_id = intval(CommonFunctions::replaceChars($_POST["recid"], 0));
		}
		else
		{
			$this->alumni_id = 0;
		}
		if(isset($_GET["active"]))
		{
			$this->active = CommonFunctions::replaceChars($_GET["active"], 0);
		}
		else
		{
		    $this->active = 3;
		}
		if(isset($_GET["show"]))
		{
			$this->show = CommonFunctions::replaceChars($_GET["show"], 0);
		}
		else
		{
		    $this->show = 3;
		}
		
		return 1;
	}
	
	//==== Delete Existing Record ====
	public function deletePart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		if ($this->alumni_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		
	    $this->deleteRecords();
		return 1;
	}
	
	//=== Make the Alumni Status
	public function alumniStatus()
	{
		//--- Get Record Id ---
		$this->getRecordId();

		if($this->alumni_id != "")
		{
			if($this->active == 1)
			{
				$sql = "UPDATE tbl_alumni SET status = 1 WHERE status = 0 AND alumni_id = " . $this->alumni_id;
				$rs = $this->link_id->query($sql);
				
				$_SESSION['app_message'] = "Alumni Approved...";
				$this->alumniAppMail();
				/*header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );*/
				header("Location: alumni.php");
				die("_");
			}
			else if($this->active == 2)
			{
				$sql = "UPDATE tbl_alumni SET status = 0 WHERE status = 1 AND alumni_id = " . $this->alumni_id;
				$rs = $this->link_id->query($sql);
				
				$_SESSION['app_message'] = "Alumni Pending...";
				header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
			
			$_SESSION['app_message'] = "Alumni Approved...";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			$_SESSION['app_message'] = "Alumni Pending...";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
	}
	
	//===  Alumni Show in Page Status
	public function alumniShowinpage()
	{
		//--- Get Record Id ---
		$this->getRecordId();

		if($this->alumni_id != "")
		{
			if($this->show == 1)
			{
				$sql = "UPDATE tbl_alumni SET is_show = 1 WHERE is_show = 0 AND alumni_id = " . $this->alumni_id;
				$rs = $this->link_id->query($sql);
				$_SESSION['app_message'] = "Alumni added in page ...";
				/*header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );*/
				header("Location: alumni.php");
				die("_");
			}
			else if($this->show == 2)
			{
				$sql = "UPDATE tbl_alumni SET is_show = 0 WHERE is_show = 1 AND alumni_id = " . $this->alumni_id;
				$rs = $this->link_id->query($sql);
				
				$_SESSION['app_message'] = "Alumni removed from page...";
				header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
			$_SESSION['app_message'] = "Alumni added in page...";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			$_SESSION['app_message'] = "Alumni removed from page...";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
	}
	
    //==== Delete Selected Records ====
	public function deleteRecords()
	{
		$tmpSql = "SELECT * FROM tbl_alumni WHERE alumni_id = '" . $this->alumni_id  . "'";	
		$rs = $this->link_id->query($tmpSql);
					
		if( (!$rs) || (!($rec =$rs->fetch_array())) )
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			//echo $tmpSql; die();
		}
		else
		{
			$this->alumni_id = $rec["alumni_id"];
			$this->alumni_photo = $rec["alumni_photo"];
			$this->status = $rec["status"];	
		}
		
		if ($this->alumni_photo !="")
		{
			if(file_exists("../alumni_photo/".$this->alumni_photo))
			{
				unlink("../alumni_photo/".$this->alumni_photo);
			}
		}
		//--- Begin Transaction
		$this->link_id->autocommit(FALSE); 				
				
		//--- Delete records
					
		$tmpSql = "DELETE FROM tbl_alumni WHERE alumni_id ='" . $this->alumni_id . "'";
		$rs3 = $this->link_id->query($tmpSql);
		
		$tmpSql_career = "DELETE FROM tbl_alumni_career WHERE alumni_id ='" . $this->alumni_id . "'";
		$rs4 = $this->link_id->query($tmpSql_career);
		
		if($this->link_id->affected_rows == -1)
		{
			mysql_query("ROLLBACK", $this->link_id);
			$myerr = "Record could not be deleted";
		}	
		else
		{
			//--- Commit Transaction
			$this->link_id->commit();
			$_SESSION["app_message"] = "Record successfully deleted.";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		
	  return 1;
	}
	
	//==== Initialize Special Properties ====
	public function initSpecialProperties()
	{
		$this->members_search=2;
		
		if(isset($_POST["members"]))
		{
			$this->members_search = CommonFunctions::replaceChars($_POST["members"], 11);
		}
		
		if(isset($_POST["txt_registration_no"]))
		{
			$this->registration_no_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_registration_no"]), 11);
		}
		else
		{
			$this->registration_no_search = "";
		}
		if(isset($_POST["txt_email"]))
		{
			$this->txt_email_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_email"]), 11);
		}
		else
		{
			$this->txt_email_search = "";
		}
		
		if(isset($_POST["txt_first_name"]))
		{
			$this->first_name_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_first_name"]), 11);
		}
		else
		{
			$this->first_name_search = "";
		}
		
		if(isset($_POST["txt_middle_name"]))
		{
			$this->txt_middle_name_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_middle_name"]), 11);
		}
		else
		{
			$this->txt_middle_name_search = "";
		}
		
		if(isset($_POST["txt_last_name"]))
		{
			$this->txt_last_name_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_last_name"]), 11);
		}
		else
		{
			$this->txt_last_name_search = "";
		}
		
		if(isset($_POST["txt_city"]))
		{
			$this->txt_city_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_city"]), 11);
		}
		else
		{
			$this->txt_city_search = "";
		}
		if(isset($_POST["txt_state"]))
		{
			$this->txt_state_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_state"]), 11);
		}
		else
		{
			$this->txt_state_search = "";
		}if(isset($_POST["txt_country"]))
		{
			$this->txt_country_search = CommonFunctions::replaceChars(stripslashes($_POST["txt_country"]), 11);
		}
		else
		{
			$this->txt_country_search = "";
		}
		if(isset($_POST["gender_search"]) && $_POST["gender_search"]!="")
		{
			$this->gender_search = $_POST["gender_search"];
		}
		else
		{
			$this->gender_search = "";
		}
		/*if(isset($_POST["stream_search"]) && $_POST["stream_search"]!="")
		{
			$this->stream_search = $_POST["stream_search"];
		}
		else
		{
			$this->stream_search = "";
		}*/
		if(isset($_POST["year_of_passing_search"]) && $_POST["year_of_passing_search"]!="")
		{
			$this->year_of_passing_search = $_POST["year_of_passing_search"];
		}
		else
		{
			$this->year_of_passing_search = "";
		}
		
		if(isset($_REQUEST["yop"]))
		{
			$this->year_of_passing_report = $_REQUEST["yop"];
		}
		else
		{
			$this->year_of_passing_report = "";
		}
		if(isset($_POST["dept"]))
		{
			$this->dept =  CommonFunctions::replaceChars($_POST["dept"], 0);
		}
		else
		{
			$this->dept = "";
		}
		
		return 1;
	}
	
	public function generateSearchCondition()
	{
		//--- Set Condition
		$this->search_condition="";
		
		if($this->members_search != "" && $this->members_search != 2)
		{
		    $this->search_condition .= " AND status = " . $this->members_search . "";
		}
		if($this->registration_no_search != "")
		{
			$this->search_condition .= " AND reg_no LIKE '%" . $this->registration_no_search . "%'";
		}
		if($this->txt_email_search != "")
		{
			$this->search_condition .= " AND email LIKE '%" . $this->txt_email_search . "%'";
		}
		if($this->first_name_search != "")
		{
			$this->search_condition .= " AND first_name LIKE '%" . $this->first_name_search . "%'";
		}
		if($this->txt_middle_name_search != "")
		{
			$this->search_condition .= " AND middle_name LIKE '%" . $this->txt_middle_name_search . "%'";
		}
		if($this->txt_last_name_search != "")
		{
			$this->search_condition .= " AND last_name LIKE '%" . $this->txt_last_name_search . "%'";
		}
		if($this->txt_city_search != "")
		{
			$this->search_condition .= " AND city LIKE '%" . $this->txt_city_search . "%'";
		}
		if($this->txt_state_search != "")
		{
			$this->search_condition .= " AND state LIKE '%" . $this->txt_state_search . "%'";
		}
		if($this->txt_country_search != "")
		{
			$this->search_condition .= " AND country LIKE '%" . $this->txt_country_search . "%'";
		}
		if($this->gender_search != "")
		{
			$this->search_condition .= " AND gender = '" . $this->gender_search . "'";
		}
		/*if($this->stream_search != "")
		{
			$this->search_condition .= " AND stream = '" . $this->stream_search . "'";
		}*/
		if($this->year_of_passing_search != "")
		{
			$this->search_condition .= " AND year_of_passing = '" . $this->year_of_passing_search . "'";
		}
		if($this->year_of_passing_report != "")
		{
			$this->search_condition .= " AND year_of_passing = '" . $this->year_of_passing_report . "'";
		}
		if($this->dept != "")
		{
			$this->search_condition .= " AND dept = '" . $this->dept . "'";
		}
	}

    //=== Initialize Form Data ====
	public function initFormData()
	{
		//--- do nothing
		return 1;
	}
	
    //==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";

		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	public function deleteAlumniRecords()
	{
		$myerr = "";
		$used_id_list = "";
		$used_name_list = "";
		$tmpmsg = "";
		$file_name = "";
		
		//--- Begin Transaction
		$this->link_id->autocommit(FALSE);
		
		$tmpSql = "SELECT * FROM tbl_alumni WHERE alumni_id in (" . $this->del_ids . ")";	
		
	    //--- echo $tmpSql; die();
		
		$rs = $this->link_id->query($tmpSql);
					
		if( (!$rs) || (!($rec =$rs->fetch_array())) )
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			$this->alumni_id = $rec["alumni_id"];
			$this->alumni_photo = $rec["alumni_photo"];
		}
		
		$this->path_alumni_image = "../alumni_photo/";
		
		if($this->alumni_photo != "")
		{
			if(file_exists($this->path_alumni_image.$this->alumni_photo))
			{
				unlink($this->path_alumni_image.$this->alumni_photo);
			}			
			
		}
		//--- Delete records which are not in use
		
		$tmpSql2 = "DELETE FROM tbl_alumni" 
			. " WHERE alumni_id in (" . $this->del_ids . ")";

		$rs2 = $this->link_id->query($tmpSql2);
		
		if($this->link_id->affected_rows == -1)
		{
			mysql_query("ROLLBACK", $this->link_id);
			$myerr = "Record could not be deleted";
		}
		else
		{
			//--- Commit Transaction
			$this->link_id->commit();
			$_SESSION["app_message"] = "record deleted successfully.";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		
	
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}

	public function upgradeAlumniRecords()
	{
		$myerr = "";
		$used_id_list = "";
		$used_name_list = "";
		$tmpmsg = "";
		$file_name = "";

		//--- Begin Transaction
		$this->link_id->autocommit(FALSE);
		
		//--- Update records which are not in use
		if($this->members_search == 0)
		{
			$this->sendApproval();		
			$tmpSql2 = "update tbl_alumni set status=1" 
			. " WHERE status=0 and alumni_id in (" . $this->upgrade_ids . ")";
		
			//echo $tmpSql2; die();
			$rs2 = $this->link_id->query($tmpSql2);
			
			if($this->link_id->affected_rows == -1)
			{
				mysql_query("ROLLBACK", $this->link_id);
				$myerr = "Record could not be updated";
			}
			else
			{
				//--- Commit Transaction
				$this->link_id->commit();
				$_SESSION["app_message"] = "Record updated successfully.";
				header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
		}	
		
	}
	
	public function sendApproval()
	{	
			$tmpSql = "SELECT * FROM tbl_alumni WHERE alumni_id in (" . $this->upgrade_ids . ")";	
		
			//echo $tmpSql; //die();
			
			$rs = $this->link_id->query($tmpSql);
						
			if( (!$rs) || (!($rec =$rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found...";
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
			else
			{
				do 
				{
					$this->alumni_id = $rec["alumni_id"];
					
					$this->alumni_name = $rec["first_name"] . " " . $rec["middle_name"] . " " . $rec["last_name"];
					
					$this->alumni_email = $rec["email"];
					
					//echo $this->alumni_name . "-" . ""; //die();
					
					$this->admin_mail = DatabaseManager::igetDataFromTable($this->link_id, "tbl_admin_user", "user_email", "", "user_slno = 1");
					
					//echo $this->admin_mail;
						
					$headers = "MIME-Version: 1.0\r\n";
					$headers .= "Content-type: text/html; charset=utf-8\r\n";
						
					$message = '';
					$message .= "Dear " . $this->alumni_name . ", ";
					$body = '';
					
					$body .= "<p>You have been registered as an Alumni Member.</p>";
					
					$body .= "<p>You can login with your email id and password.</p>";
					
					$subject = "Approval for Alumni Membership";
					$message .= $body;	
					
					try 
					{
						$mail = new PHPMailer();
						$body = $message;
						$body = str_replace("[\]",'',$body);			
						//$mail->IsSMTP(); // telling the class to use SMTP
						//$mail->Host       = "mail.yourdomain.com"; // SMTP server
						$mail->SMTPDebug  = 0;                     // enables SMTP debug information (for testing)
																   // 1 = errors and messages
																   // 2 = messages only
						//$mail->SMTPAuth   = $this->app_config["smtp_auth"];                  // enable SMTP authentication
						//$mail->SMTPSecure = $this->app_config["smtp_secure"];                 // sets the prefix to the servier
						//$mail->Host       = $this->app_config["smtp_host"];      // sets GMAIL as the SMTP server
						//$mail->Port       = $this->app_config["smtp_port"];                   // set the SMTP port for the GMAIL server
						//$mail->Username   = $this->app_config["mail_from"];  // GMAIL username
						//$mail->Password   = $this->app_config["mail_pass"];            // GMAIL password
						
						//$mail->SMTPAuth = true;  // authentication enabled
						
						$mail->SetFrom($this->admin_mail, "Alumni");					
						$mail->AddReplyTo($this->admin_mail, "");					
						$mail->Subject    = $subject;					
						$mail->AltBody    = "To view the message, please use an HTML compatible email viewer!"; // optional, comment out and test					
						$mail->MsgHTML($body);					
						$address = $this->alumni_email;
						$mail->AddAddress($address, "");
						//$mail->AddAddress("<EMAIL>", "");												
						//$mail->AddCC("<EMAIL>", 'xyz');			
						//$mail->AddAttachment($File);				
					//echo ($headers . "<br/>" . $subject . "<br/>" . $message . "<br>" . $address) . "<br />"; 
					//die();
						if($_SERVER['SERVER_NAME'] != "*************")
						{
							$mail->Send();
						}
					
					}
					
					catch (phpmailerException $e) 
					{				  
					  $e->errorMessage();
					  echo "Mailer Error: " . $mail->ErrorInfo;
					}
					//return 1; 
				}
				while(($rec =$rs->fetch_array()));	
			}
			
			//die();
		//end of class

		}
	
	public function alumniAppMail()
	{
		$tmpSql = "SELECT * FROM tbl_alumni WHERE alumni_id in (" . $this->alumni_id . ")";	
	
			//echo $tmpSql; //die();
		
		$rs = $this->link_id->query($tmpSql);
					
			  if( (!$rs) || (!($rec =$rs->fetch_array())) )
			  {
				  $_SESSION['app_error'] = "Record not found...";
				  header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				 die("_");
			   }
				 else
				  {
					do 
					  {
					  
					$this->alumni_id = $rec["alumni_id"];
					
					$this->alumni_name = $rec["first_name"] . " " . $rec["middle_name"] . " " . $rec["last_name"];
					
					$this->alumni_email = $rec["email"];
					$this->password = $rec["show_pwd"];
					
					//echo $this->alumni_name . "-" . ""; //die();
					
					$this->admin_mail = DatabaseManager::igetDataFromTable($this->link_id, "tbl_admin_user", "user_email", "", "user_slno = 1");
					
					//echo $this->admin_mail;
					
						  if($this->active == 1)
							{

							$to = $this->alumni_email;
	
							$from = "Alumni Admin <".$this->admin_mail.">";			
	
							$subject = "Alumni Account Registration";
	
							$message = '<table width="67%" border="0" align="center" cellpadding="2" cellspacing="1" style="background-color:#EAEAEA;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif;color:#000000;font-size:14px;" >
						   <tr>
						   <td colspan="2"></td>
						   </tr>
						   <tr>
							<td colspan="3">Dear '.$this->alumni_name.'<br/>&nbsp;</td>
						   </tr>
						  <tr>
						  <td colspan="2">&nbsp;</td>
						   </tr>
						  <tr>
						 <td colspan="3">&nbsp;Thank you for registering as an Alumni.</td>
						</tr>  
					   <tr>
				   	<td colspan="3">Your login details are as follows:</td>
				  </tr> 
			  <tr>
				  <td colspan="3">&nbsp;</td>
			  </tr>
		      <tr>
				  <td colspan="2"></td>
			  </tr>
			  <tr>
			  <td align="left" valign="top">&nbsp;<b>Alumni Login ID     : &nbsp;</b></td>
			  <td width="75%" align="left" valign="top">'.$this->alumni_email.'</td>
			  </tr>
			  <tr>
			  <td align="left" valign="top">&nbsp;<b>Password     : &nbsp;</b></td>
			  <td width="75%" align="left" valign="top">'.$this->password.'</td>
			  </tr>
				
				  <tr>
				<td colspan="2">&nbsp;</td>
			    </tr>
					<tr>
				  <td align="center"><a href="http://igitalumni.org.in/"  target = "_blank">Click here to login</a></td>			 
					  </tr>
					  <tr>
						<td colspan="3">&nbsp;</td>
				  	 </tr>
			  
			         <tr>
						<td colspan="3">&nbsp;</td>
				  	 </tr>
					 <tr>
						<td colspan="3">&nbsp;</td>
				  	 </tr>
				    <tr>
						<td colspan="3">Thank You,  </td>
				  	</tr>	
				  	<tr>
						<td colspan="3"> Admin, Alumni ,Sarang</td>
				  	</tr>	
				  	<tr>
						<td colspan="3">&nbsp;</td>
				  	</tr>
			
				</table>';
					
		            $headers  = 'MIME-Version: 1.0' . "\r\n";
		            $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
		            $headers .= "From:" . $from . "\r\n";
		           //echo "Alert:".$message."<br> TO:".$to."From:".$from; die();
		            mail($to, $subject, $message, $headers);
		        // die();
	                }
	             }
				 
			  while(($rec =$rs->fetch_array()));	
		}
			  
	}
}

$objCurPage = new CurrentPage();

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<!--<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>-->
<script type="text/javascript" src="js/jquery-1.10.2.js"></script>
<script type="text/javascript" src="js/jquery-ui.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->
<link href="css/bootstrap.min.css" rel="stylesheet" />

<link href="css/colorbox.css" rel="stylesheet" />
<link href="css/popbox.css" rel="stylesheet" type="text/css" />
<link href="css/accrodiantree.css" rel="stylesheet" />
<!--<script type="text/javascript" src="../js/jquery-1.10.1.min.js"></script>-->
<script type="text/javascript" src="js/jquery-ui-1.10.2.custom.min.js"></script>
<script type="text/javascript" src="js/webwidget_vertical_menu.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<script type="text/javascript" src="js/jquery.colorbox.js"></script>

<script language="JavaScript">
function moveRec(frm, crecVal)
	{
		frm.crec.value=crecVal;
	}
	
function sortPage(sortidx)
	{
	var frm, sortmode;
	frm = eval("document.frmList");
	//frm.sortby.value == sortidx && 
	if (frm.sortmode.value == "asc")
		{
		sortmode = "desc";
		}
	else
		{
		sortmode = "asc";
		}

	frm.action = "<?php echo $objCurPage->cur_page_url; ?>" + "?sortby=" + sortidx + "&sortmode=" + sortmode;
	frm.submit();
	} 
</script>
<script language="JavaScript" src="js/common_js_functions.js"></script>
<script type="text/javascript">
function moveRec(frm, crecVal)
{
	frm.crec.value=crecVal;
}

function sendMail(ids)
{
	$.colorbox({href:'alumni_mail.php?rid='+ids});
	//popUpWindow('alumni_mail.php?rid='+ids,'630','600','100','80','No');
} 

function sendpage(alumni_id)
{
		$.colorbox({href:'view_alumni.php?alumni_id='+alumni_id,onClosed:reloadPage});
		//popUpWindow('view_alumni.php?alumni_id='+alumni_id, 600,500,300,'110','yes');
}

function getAlumniemails(idz){
		//alert(idz);
		$.colorbox({href:'alumni_mail.php?rid='+idz,onClosed:reloadPage});
};

function reloadPage() {
	$("html, body").animate({ scrollTop: 0 }, "slow");
	$("#loadingImage").html('<img src="images/loading.gif" />');
	$("#loadingImage").show();
    location.reload(true); 
}
</script>

</head>

<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:515px;">
  <div id="row">
    <aside class="col-md-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-8 maincontent">
      <div class="imgLoader" id="loadingImage" style="display:none;text-align:center;margin-top:10px;"></div>
      <table width="100%" height="100%" cellpadding="0" cellspacing="0">
              <tr>
                <td height="20" colspan="2" class="pageHeader"><b>Manage Alumni</b></td>
              </tr>
              
              <tr>
                <td colspan="2" valign="top"><form action="alumni.php" method="post" name="frmList" id="frmList">
                    <table width="100%" cellspacing="0" cellpadding="4">
                      <tr>
                        <td height="30" align="center" class="errMsgAdmin"><?php CommonFunctions::displayErrMsg(); ?>
                        <span class="show-statistics" onClick="javascript: $.colorbox({href:'show-statistics.php',onClosed:reloadPage});">Show Statistics</span>
                        </td>
                      </tr>
                      <tr>
                        <td height="30" align="center" class="errMsgAdmin"><table width="850" align="left" cellspacing="0" class="dialogBaseShadow">
                            <tr>
                              <td><table width="100%" cellspacing="0" class="dialogBase search-tbl">
                                  <tr>
                                    <td valign="top"><table width="100%" cellspacing="0" class="formTextWithBorder" >
                                      <tr>
                                        <td height="35" colspan="2"><input type="radio" name="members" value="1" <?php if($objCurPage->members_search =="1") { ?> checked="checked" <?php } ?> onclick="this.form.submit();"/>
                                        Accepted Member </td>
                                        <td height="35" align="left" colspan="2"><input type="radio" name="members" value="0" <?php if($objCurPage->members_search =="0") { ?> checked="checked" <?php } ?> onclick="this.form.submit();"/>
                                        New Member </td>
										 
										 <td height="35" align="left" colspan="6"><input type="radio" name="members" value="2" <?php if($objCurPage->members_search =="2") { ?> checked="checked" <?php } ?> onclick="this.form.submit();"/>
                                         All</td>
                                      </tr>
                                      <tr>
                                        <td width="141" height="35">&nbsp; Registration No:</td>
                                        <td width="209" height="29" align="left"><input type="text" name="txt_registration_no" id="txt_registration_no" class="form-control" maxlength="50" value="<?php echo $objCurPage->registration_no_search; ?>" /></td>
                                        <td class="bodytxt" width="144">&nbsp;&nbsp;&nbsp;&nbsp;Email:<font color="#FF0000"></font></td>
                                        <td colspan="4" align="left" class="bodytxt" style="font-weight:bold;font-size:12px;">
                                        <input name="txt_email" type="text" id="txt_email" class="form-control" value="<?php echo ($objCurPage->txt_email_search); ?>" style="width:64%;"/>               </td>
                                      </tr>
                                      <tr>
                                        <td width="141" height="35">&nbsp; First Name:</td>
                                        <td height="29" align="left"><input name="txt_first_name" type="text" class="form-control" id="txt_first_name" maxlength="50" value="<?php echo $objCurPage->first_name_search; ?>" /></td>
                                        <td height="35">&nbsp;&nbsp;&nbsp;&nbsp;Middle Name:</td>
                                        <td width="237" height="29" align="left"><input type="text" name="txt_middle_name" class="form-control" id="txt_middle_name" maxlength="50" value="<?php echo $objCurPage->txt_middle_name_search; ?>" />                                        </td>
                                        <td width="93" height="35">&nbsp;&nbsp;&nbsp;&nbsp;Last Name:</td>
                                        <td width="204" height="29" align="left"><input type="text" name="txt_last_name" class="form-control" id="txt_last_name" maxlength="50" value="<?php echo $objCurPage->txt_last_name_search; ?>" />                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="141" height="35">&nbsp; City:</td>
                                        <td height="29" align="left"><input name="txt_city" type="text" class="form-control" id="txt_city" maxlength="50" value="<?php echo $objCurPage->txt_city_search; ?>" /></td>
                                        <td height="35">&nbsp;&nbsp;&nbsp;&nbsp;State:</td>
                                        <td height="29" align="left"><input name="txt_state" type="text" class="form-control" id="txt_state" maxlength="50" value="<?php echo $objCurPage->txt_state_search; ?>" />                                        </td>
                                        <td height="35">&nbsp;&nbsp;&nbsp;&nbsp;Country:</td>
                                        <td height="29" align="left"><input name="txt_country" type="text" class="form-control" id="txt_country" maxlength="50" value="<?php echo $objCurPage->txt_country_search; ?>" />                                        </td>
                                      </tr>
                                      <tr>
                                        <td height="35">&nbsp; Gender:</td>
                                        <td height="29" align="left"><select name="gender_search" class="form-control">
                                          <option value="">Select</option>
                                          <option value="M" <?php if(trim($objCurPage->gender_search) == "M"){echo "selected";} ?>>Male</option>
                                          <option value="F" <?php if(trim($objCurPage->gender_search) == "F") {echo "selected";}?>>Female</option>
                                        </select></td>
                                        <td height="35"><span class="bodytxt">&nbsp;&nbsp;&nbsp;&nbsp;Stream:</span></td>
                                         <td><select name="dept" id="dept" class="form-control">
                                                <option value="">--Select--</option>
                                                <option value="Chemical Engineering" <?php if($objCurPage->dept == "Chemical Engineering") { echo "selected";} ?> >Chemical Engineering</option>
                                                <option value="Civil Engineering" <?php if($objCurPage->dept == "Civil Engineering") { echo "selected";} ?>>Civil Engineering</option>
                                                <option value="Electrical Engineering" <?php if($objCurPage->dept == "Electrical Engineering") { echo "selected";} ?>>Electrical Engineering</option>
                                                <option value="Environmental Science and Engineering" <?php if($objCurPage->dept == "Environmental Science and Engineering") { echo "selected";} ?>>Environmental Science & Engineering</option>
                                                <option value="Industrial Power Control and Drives" <?php if($objCurPage->dept == "Industrial Power Control and Drives") { echo "selected";} ?>>Industrial Power Control & Drives</option>
                                                <option value="Master in Computer Science and Application" <?php if($objCurPage->dept == "Master in Computer Science and Application") { echo "selected";} ?>>Master in Computer Science & Application</option>
                                                <option value="Mechanical Engineering" <?php if($objCurPage->dept == "Mechanical Engineering") { echo "selected";} ?>>Mechanical Engineering</option>
                                                <option value="Metallurgical and Materials Engineering" <?php if($objCurPage->dept == "Metallurgical and Materials Engineering") { echo "selected";} ?>>Metallurgical & Materials Engineering</option>                                                
                                                <option value="General Administration" <?php if($objCurPage->dept == "General Administration") { echo "selected";} ?>>General Administration</option>
                                                <option value="Electronics and TC Engg" <?php if($objCurPage->dept == "Electronics and TC Engg") { echo "selected";} ?>>Electronics & TC Engg</option>
                                                <option value="Information Technology" <?php if($objCurPage->dept == "Information Technology") { echo "selected";} ?>>Information Technology</option>
                                            </select>
                                         </td>   
                                        <td height="35" align="center"><span class="bodytxt">&nbsp;&nbsp;&nbsp;&nbsp;Year of Passing:</span></td>
                                        <td height="29" align="left">
                                        <select name="year_of_passing_search" class="form-control">
                                         <option value="">Year</option>
										 <?php for($i=date("Y"); $i>=1980; $i--){?>
                                          <option value="<?php echo $i; ?>" <?php if($objCurPage->year_of_passing_search==$i) { echo "selected";} ?>><?php echo $i; ?></option>
                                          <?php } ?>
                                		 </select>
                                         </td>
                                      </tr>
                                      <tr>
                                        <!--<td height="35"><span class="bodytxt">&nbsp; Degree:</span></td>
                                        <td height="29" align="left">
                                        <select name="stream_search" class="form-control">
                                          <option value="">Select your Program</option>
                                          <option value="B.E / B.Tech" < ? if($objCurPage->stream_search=="B.E / B.Tech") { echo "selected";} ?>>B.E / B.Tech</option>
                                          <option value="M.E / M.Tech" < ? if($objCurPage->stream_search=="M.E / M.Tech") { echo "selected";} ?>>M.E / M.Tech</option>
                                          <option value="MCA" < ? if($objCurPage->stream_search=="MCA") { echo "selected";} ?>>MCA</option>
                                          <option value="MBA" < ? if($objCurPage->stream_search=="MBA") { echo "selected";} ?>>MBA</option>
                                        </select>
                                        </td>-->
                                                                              
                                        </tr>
                                        <tr>                                            
                                            <td height="35" colspan="6" align="right"><input name="postMe2" type="hidden" id="postMe2" value="Y" />
                                          <input name="cmdSearch" type="submit" class="btn btn-default" id="cmdSearch" value="Search" />
&nbsp;
<input name="cmdClearSearch" type="button" class="btn btn-default" id="cmdClearSearch" value="Clear" <?php if($objCurPage->search_condition != ""){ echo "enabled"; } else { echo "disabled"; } ?> onclick="window.location.href='<?php echo $objCurPage->cur_page_url ?>'" />
                                        </tr> 
                                    </table></td>
                                  </tr>
                              </table></td>
                            </tr>
                        </table></td>
                      </tr>
                      
                      <tr>
                        <td align="center" valign="top"><table width="100%" align="left" cellspacing="0" class="dialogBaseShadow alumni-tbl">
                            <tr>
                              <td><table width="97%" cellspacing="0" class="dialogBaseShadow">
                                  <tr>
                                    <td><table width="100%" cellspacing="0" class="dialogBase">
                                        <tr>
                                          <td class="dialogHeader"><strong>List of Alumni</strong></td>
                                        </tr>
                                        <tr>
                                          <td align="right" valign="top" ></td>
                                        </tr>
                                        <tr>
                                          <td align="right"><?php

			$tmpSql = "SELECT count(*) as totrec
				FROM tbl_alumni a "
				. " WHERE 1=1"
				. $objCurPage->search_condition ;
			 $rs = $objCurPage->link_id->query($tmpSql);	
			//echo($tmpSql);
			if((!$rs) || (!($rec = $rs->fetch_array())))
				{
				//echo("no record exists...<br>");
				$totrec=0;
				}
			else
				{
				if(empty($rec["totrec"]))
					{$totrec=0;}
				else
					{$totrec=$rec["totrec"];}
				}

			if( $objCurPage->crec >=$totrec)
				{ 
				if($totrec == 0)
					{$objCurPage->crec = 0;}
				else if($totrec % $objCurPage->reccnt == 0)
					{$objCurPage->crec = $totrec - $objCurPage->reccnt;}
				else
					{$objCurPage->crec = $totrec - ($totrec % $objCurPage->reccnt);}
				}
			if(($totrec- $objCurPage->crec )>$objCurPage->reccnt)
				{$pgrec=$objCurPage->reccnt;}
			else
				{$pgrec=$totrec- $objCurPage->crec ;}
						?>
						  <?php if( $objCurPage->crec  >= $objCurPage->reccnt) {?>
                          <input name="imgleft2" type="image" id="imgleft22" src="images/butt_left2.gif" alt="Show First" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo 0 ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self';" />
                          <input name="imgleft" type="image" id="imgleft" src="images/butt_left.gif" alt="Show Previous" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo  $objCurPage->crec  - $objCurPage->reccnt ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self';" />
                          <?php } else { ?>
                          <img src="images/butt_left2_dis.gif" width="16" height="16" /> <img src="images/butt_left_dis.gif" width="16" height="16" />
                          <?php } ?>
                        &nbsp; <b>
                          <?php if($totrec > 0)
						  	{
							if( ( $objCurPage->crec +1) != ( $objCurPage->crec +$pgrec) )
								{echo("<font color='#0033FF'>" . ( $objCurPage->crec +1) . "</font>-<font color='#0033FF'>" . ( $objCurPage->crec +$pgrec) . "</font> of <font color='#CC0000'>" . $totrec) . "</font>";}
							else
								{echo("<font color='#0033FF'>" . ( $objCurPage->crec +1) . "</font> of <font color='#CC0000'>" . $totrec) . "</font>";}
							}
						else
							{echo("<font color='#990000'>0</font> ");}
						?>
                              </b>&nbsp;
                            <?php if($totrec > ( $objCurPage->crec  + $objCurPage->reccnt)) {?>
                            <input name="imgright" type="image" id="imgright" src="images/butt_right.gif" alt="Show Next" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo  $objCurPage->crec  + $objCurPage->reccnt ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self'" />
                            <input name="imgright2" type="image" id="imgright2" src="images/butt_right2.gif" alt="Show Last" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php if($totrec % $objCurPage->reccnt != 0) {echo($totrec - ($totrec % $objCurPage->reccnt));} else {echo($totrec - $objCurPage->reccnt);} ?>');  this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self'" />
                            <?php } else { ?>
                            <img src="images/butt_right_dis.gif" width="16" height="16" /> <img src="images/butt_right_dis.gif" width="16" height="16" />
                            <?php } ?>
                            <input name="crec" type="hidden" id="crec" value="<?php echo $objCurPage->crec ?>" /></td>
                        </tr>
                        <tr>
                          <td><?php 
				$hasRecs=0;
				$color1 = "#E4E4E4";  
				$color2 = "#F1F1F1";
				$j = 0;
				
				$tmpSql = "SELECT a.alumni_id, a.prefix, a.first_name, a.middle_name, a.last_name, a.year_of_passing, a.reg_no, a.email, a.dept, a.status, a.is_show FROM tbl_alumni a"
				. " WHERE 1=1"
				. " and a.alumni_id <> 0"
				. $objCurPage->search_condition
				. " ORDER BY " . $objCurPage->sortby . " " . $objCurPage->sortmode
				. " LIMIT " .  $objCurPage->crec  . "," . $pgrec;	
											
						//echo $tmpSql;
							
							 $rs = $objCurPage->link_id->query($tmpSql);
					
							 if( (!$rs) || (!($rec = $rs->fetch_array())) )
							{
								echo("<font color='#CC0000'><br>no record found...<br></FONT>");
							}
							else
							{
							?>
                                             
                                        <table width="100%" cellspacing="1" class="formTextWithBorder">
                                            <tr valign="top" class="formHeadingBkg">
                                              <td width="6%" valign="top" align="center">&nbsp;</td>
                                              <td width="9%" height="15" valign="top" style="text-align:left">
                                                <a href="javascript:sortPage(5);" class="blackNoUL">Year </a>
                                                  <?php CommonFunctions::showSortIcon(5, $objCurPage->sortby, $objCurPage->sortmode) ?></td>
                                              <td width="22%" valign="top" style="text-align:left"><a href="javascript:sortPage(8);" class="blackNoUL">Stream</a>
                                                  <?php CommonFunctions::showSortIcon(8, $objCurPage->sortby, $objCurPage->sortmode) ?></td>
                                              <td width="20%" style="text-align:left"><a href="javascript:sortPage(2);" class="blackNoUL">Name</a>
                                                  <?php CommonFunctions::showSortIcon(2, $objCurPage->sortby, $objCurPage->sortmode) ?></td>
                                              <!--<td width="14%" style="text-align:left"><a href="javascript:sortPage(6);" class="blackNoUL">Regd. </a>
                                                  No< ?php CommonFunctions::showSortIcon(6, $objCurPage->sortby, $objCurPage->sortmode) ?></td>-->
                                              <td width="17%" style="text-align:left"><a href="javascript:sortPage(7);" class="blackNoUL">Email</a>
                                                <?php CommonFunctions::showSortIcon(7, $objCurPage->sortby, $objCurPage->sortmode) ?></td>
                                              <td width="10%" align="center"><a href="javascript:sortPage(9);" class="blackNoUL">Status</a>
                                                <?php CommonFunctions::showSortIcon(9, $objCurPage->sortby, $objCurPage->sortmode) ?></td>
                                              <td width="16%" align="center" valign="top">Action</td>
                                            </tr>
                                            <?php do {
                                                $bgcolorx = ($j % 2) ? $color1 : $color2;
                                                $j=$j+1; ?>
                                            <tr bgcolor="<?php echo $bgcolorx;?>" class="dselbkg">
                                              <td align="center" valign="top"><input type="checkbox" name="alumni_id[]" id="chk_alumni_id_<?php echo $j; ?>" value="<?php echo $rec["alumni_id"];?>"></td>
                                              <td height="20" align="left" valign="top"><?php echo $rec["year_of_passing"]; ?>                                                  </td>
                                              <td height="20" align="left" valign="top"><?php echo $rec["dept"]; ?></td>
                                              <td align="left" valign="top"><?php echo $rec["prefix"] . " " . $rec["first_name"] . " " . $rec["middle_name"] .  " " . $rec["last_name"];?></td>
                                              <!--<td align="center" valign="top">< ?php echo $rec["reg_no"]; ?></td>-->
                                              <td align="left" valign="top"><?php echo $rec["email"]; ?></td>
                                              <td align="center" valign="top">
                                            
												<?php 
                                                if($rec["status"] == 1)
                                                {
                                                ?>
                                                 <a href="javascript:window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["alumni_id"] . "&active=2&task=4"); ?>'" title="Deactive Member"><img src="../images/active.png" width="22" height="22" title="Deactive Member" /></a>                                                            	
                                                <?php
                                                }
                                                else if($rec["status"] == 0)
                                                {
                                                ?>
                                                <a href="javascript:window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["alumni_id"] . "&active=1&task=4"); ?>'" title="Accept Member"><img src="../images/inactive.png" width="22" height="22" title="Accept Member" /></a>                                	
                                                <?php
                                                }
                                                ?>
                                              </td>
                                              
                                              <td height="20" align="center" valign="top">   
                                              
                                              <?php 
                                                if($rec["is_show"] == 1)
                                                {
                                                ?>
                                                <a href="javascript:window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["alumni_id"] . "&show=2&task=5"); ?>'" title="Don't show in alumni page"><img src="../images/green-star.png" width="22" height="22" title="Don't show in alumni page" /></a>&nbsp;                                                            	
                                                <?php
                                                }
                                                else if($rec["is_show"] == 0 && $rec["status"] == 1)
                                                {
                                                ?>
                                                <a href="javascript:window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["alumni_id"] . "&show=1&task=5"); ?>'" title="Show in alumni page"><img src="../images/red-star.png" width="22" height="22" title="Show in alumni page" /></a> &nbsp;                               	
                                                <?php
                                                }else{?>
                                                    <span style="margin-right: 9px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                <?php }
                                                ?>
                                                                                             
                                              <a onClick="sendpage(<?php echo $rec["alumni_id"]; ?>)" style="cursor:pointer;">
                                                <img src="images/view2.png" width="20" height="16" border="0" alt="View Details" title="View Details">
                                              </a> 			  	
                &nbsp;<a href="javascript:window.location.href='<?php echo ("alumni_details.php?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&alumni_id=" . $rec["alumni_id"] . "&task=2"); ?>'" title="Edit Record"><img src="images/b_edit5.png" width="16" height="16" border="0" /></a> &nbsp; <a href="javascript:if(window.confirm('Are You Sure ?')){window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["alumni_id"] . "&task=3"); ?>'}" title="Delete Record"><img src="images/b_drop.png" width="16" height="16" border="0" /></a> </td>
                                            </tr>
                                            <?php 
                  //if($j>4) {die("___aa___");break;}
                  }
                  while(($rec =$rs->fetch_array()));?>
                                          </table>
                                        <?php }?>
                                         <script language="JavaScript" type="text/javascript"> <?php echo("nrows = " . $j . ";"); ?></script>
                                        <?php $rws = $j; ?></td>
                                        </tr>
                                        <tr>
                                          <td height="32" align="right"><input name="sortby" type="hidden" id="sortby" value="<?php echo $objCurPage->sortby ?>" />
                                              <input name="sortmode" type="hidden" id="sortmode" value="<?php echo $objCurPage->sortmode ?>" />
                                              <input name="postMe" type="hidden" id="postMe" value="Y" />
											  <input name="cmdAdd" type="button" class="btn btn-default" id="cmdAdd" value="Register Alumni" onclick="window.location.href='<?php echo ("alumni_details.php?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&task=1"); ?>'" />
                                            &nbsp;
											  <!--<input type="button" name="cmdSendMail" id="cmdSendMail" value="Send Mail" class="btn btn-default" onClick="javascript:var idz = getMySelectedCheckboxValue(this.form, 'chk_alumni_id', nrows, 0); if(idz==0){alert('Please select any record to proceed');}else{$.colorbox({href:'alumni_mail.php?mailall=0&rid='+idz});}"/>-->
                                              
                                              <input type="button" name="cmdSendMail" id="cmdSendMail" value="Send Mail" class="btn btn-default" onClick="javascript:var idz = getMySelectedCheckboxValue(this.form, 'chk_alumni_id', nrows, 0); if(idz==0){alert('Please select any record to proceed');}else{getAlumniemails(idz);}"/>
                                              
											 &nbsp;
										<?php //if(1==0)
										//{
										 ?>
		<!--<input type="button" name="cmdMailAll" id="cmdMailAll" value="Send Mail to all" class="btn btn-default" 
							onClick="$.colorbox({href:'alumni_mail_all.php?mailall=1&search_condition=< ?php echo urlencode($objCurPage->search_condition)?>',onClosed:reloadPage});" />-->
												<?php //} ?>
												&nbsp;
											  <?php if($objCurPage->members_search ==0){ ?> <input type="submit" name="cmdApprove" value="Approve" class="btn btn-default" onClick="return window.confirm('Are you sure to Approve ?')"><?php } ?>
										
											<input type="submit" name="cmdDeleteAlumni" value="Delete" class="btn btn-default" onClick="return window.confirm('Are You sure to delete ?')">
											 &nbsp;
                                           <input name="cmdExit" type="button" class="btn btn-default" id="cmdExit" value="Exit"  onclick="window.location.href='<?php echo($objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec); ?>'" />
                                            &nbsp;</td>
                                        </tr>
                                        <tr>
                                          <td height="32" align="right">&nbsp;</td>
                                        </tr>
                                    </table></td>
                                  </tr>
                              </table></td>
                            </tr>
                        </table></td>
                      </tr>
                    </table>
                </form></td>
              </tr>
            </table>
    
    </div>
    <div class="col-sm-1">&nbsp;</div>
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>