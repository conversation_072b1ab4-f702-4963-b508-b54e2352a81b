<?php

require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $image_id;
	public $cat_id; 
	public $cat_name;
	public $upload_image;
	public $upload_image_old;
	

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		//die("--");
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Photo Gallery View";
		$this->cur_page_url = "ajax_gallery_img_view.php";
		$this->list_page_url = "ajax_gallery_img_view.php";
		
		//--- Check User Access permissions	
		UserAccess::checkUserLogin("", "admin");
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getRecordId();
		
		$tmpSql = "SELECT g.image_id,g.cat_id,g.upload_image,c.cat_id,c.cat_name FROM		
		tbl_photo_gallery g ,tbl_photo_category c where g.cat_id = c.cat_id and g.cat_id = " . $this->cat_id ;
		//echo $tmpSql;die();
		$rs = $this->link_id->query($tmpSql);
		
		if( (!$rs) || (!($rec = $rs->fetch_array())) )
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			    $this->image_id = $rec["image_id"];
				$this->cat_name = $rec["cat_name"];
				$this->upload_image = $rec["upload_image"];
				$this->upload_image_old = $this->upload_image;
		}		
		
			
	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id); 
	}

//==== Get Current Record Id ====
	public function getRecordId()
	{
		if(isset($_GET["recid"]))
		{
			$this->cat_id = intval(CommonFunctions::replaceChars($_GET["recid"], 0));
		}
		else if(isset($_POST["recid"]))
		{
			$this->cat_id = intval(CommonFunctions::replaceChars($_POST["recid"], 0));
		}
		else
		{
			$this->cat_id = 0;
		}
		
		return 1;
	}	
	
}

$objCurPage = new CurrentPage();

?>

<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
    <tr>
        <td>
            <form name="frmPDV" id="frmPDV" method="post" action="">
            <table width="534" border="0" align="center" cellpadding="2" cellspacing="0" class="dialogBase">
                  <tr>
                    <td colspan="2" class="dialogHeader" align="left">&nbsp;View Photo Gallery Details</td>
                  </tr>
                  <tr>
                    <td colspan="2" align="center"><?php CommonFunctions::displayErrMsg(); ?></td>
                  </tr>
                  <tr>                                      	
                    <td width="50%" valign="top">
                        <table width="100%" border="0" cellspacing="2" cellpadding="2">
                        <tr>
                        <td align="left"><b>Category Details</b></td>
                        </tr>
                          <tr>                                              
                            <td width="33%" height="25" align="left">Category Name</td>
                            <td width="4%" align="left">:</td>
                         <td width="63%" align="left">
                         <?php echo $objCurPage->cat_name; ?></td>
                          </tr>
                           
                         
                    </table>
                    </td>                                        
                  </tr>                  
                  <tr>
                    <td align="left"><b>&nbsp;Gallery Details</b></td>
                  </tr>									                                        
                  <tr>
                    <td align="left">
                        <table width="100%" border="0" cellspacing="2" cellpadding="2">
                          <tr>
                            <?php
								$tmpSql_img = "SELECT * FROM  tbl_photo_gallery  WHERE cat_id IN (SELECT cat_id FROM tbl_photo_category WHERE cat_id = ". $objCurPage->cat_id . ")";   
								
								$rs_img = $objCurPage->link_id->query($tmpSql_img);
                                                                                                
								if( (!$rs_img) || (!($rec_img = $rs_img->fetch_array())) )
                                {
                                
                                }
                                else
                                {
                                   $i=1;
                                    do
                                    { 
                                    ?>
                                    <?php  if($rec_img["upload_image"] != "" && $rec_img["upload_image"] != ""){ ?>                                     
                                         		<td align="center"><img src="<?php echo '../gallery_images/'.$rec_img["upload_image"]; ?>" height="60" width="60"  alt="<?php echo $rec_img["upload_image"];?>"/><br /><font size="2.0"><?php echo $rec_img["upload_image"];?></font></td>                                 
                                    <?php if($i == 5){ echo "</tr><tr>"; $i = 0;} } ?>	
                                    <?php 	$i++;																
                                     }while($rec_img = $rs_img->fetch_array());
                                }                                               
                           ?>
                           </tr> 
                        </table>
                        </td>
                  </tr>        
                  <tr>
                    <td align="left">                        
                   </td>
                  </tr>
                  <tr>
                    <td align="left">&nbsp;</td>
                  </tr>
                  <tr>
                    <td align="left">
                    </td>
                  </tr>
                  <tr>
                    <td align="left"></td>
                  </tr>   
             </table>
          </form>  
        </td>
    </tr>                       
</table>