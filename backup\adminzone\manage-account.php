<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	
	public $app_config;
	public $user_id;

	public $user_name, $user_age, $user_email, $user_gen, $user_pwd, $user_con, $user_add,$hobby, $user_cpwd, 
	$res, $hoblist;
	
		
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		//--- Set Current Module Name ---
		$this->module_name = "Manage Account";
		$this->cur_page_url = "manage-account.php";
		$this->list_page_url = "manage-account.php";
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
		//--- Get Sort Index and Sort Order

						
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
			//echo($this->task); exit;	
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createDetail();
				break;
			case 2:
				//--- Edit Record
				$this->editDetail();
				break;
			default:
				$this->task = 0;
				//header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				die("_");
		}
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->user_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->user_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->user_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->user_id = 0;
			}
		}
		
		return 1;
	}
	
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 2;
		}
		
		return 1;
	}

	//==== Create New Record ====
	public function createDetail()
	{
		//--- Get Record Id ---
		//$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}		
		return 1;
	}
	//==== Edit Existing Record ====
	public function editDetail()
	{
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		return 1;
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			/*$this->user_id = "";	
			$this->announcement_headline = "";	
			$this->announcement_details = "";	
			$this->announcement_date = date('m-d-Y');	*/		
			
		}
		else if($this->task == 2) 
		{
			$this->hoblist=array();
		   if($_SESSION["currentLogonUser"]->cur_user_login_name != "")
		   {
			//--- Edit Mode. Initialize fields by getting values from Database
				$tmpSql = "SELECT user_id, user_name, user_email, user_gender, user_password from admin_details WHERE user_name = '" . $_SESSION["currentLogonUser"]->cur_user_login_name  . "'";
				//echo $tmpSql;
				$rs = $this->link_id->query($tmpSql);
				if( (!$rs) || (!($rec = $rs->fetch_array())) )
				{
					$_SESSION['app_error'] = "Record not found...";
					die("_");
				}
				else
				{
					$this->user_id = $rec["user_id"];
					//echo $this->user_id;
					$this->user_name = $rec["user_name"];
					//$this->user_age = $rec["user_age"];
					$this->user_email = $rec["user_email"];
					$this->user_gen = $rec["user_gender"];
					//$this->user_con = $rec["user_contact_no"];
					//$this->user_add = $rec["user_address"];
				}
		    }
		}
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()  
	{	
		return 1;
	}
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";										
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";					
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<script type="text/javascript" src="js/jquery-1.10.1.min.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/admin.css" rel="stylesheet" />
<link href="css/font-awesome.min.css" rel="stylesheet" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script language="javascript" type="text/javascript">
$(document).ready(function(){
	$("#loadingImage").hide();	
	$('#cmdSave').click(function()
	{	
	    $("#loadingImage").hide();	
		validateSave();
	});
});
function validateSave()
{
	$("#loaderImg").html('<img src="images/bar-circle.gif" />');
	$("#loaderImg").show();
	$('#frmEditAccount').submit();
}
$('#frmEditAccount').ajaxForm({	
	target: '',
	success: function(data){
		$("#loadingImage").hide();
		if(data == 1)
		{	alert("Update Successfully");
			window.location="manage-account.php";
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			return false;					
		}
		else
		{
			$("#loaderImg").empty();
			$("#loaderImg").hide();	
			$('#showError').html(data);
			return false;
		}
	}			
});
</script>
</head>
<body>
<div id="header">
	<div class="container">
  <?php include_once("header.php") ?>
  </div>
</div>
</div>
</div>
<section class="container" style="margin-top:18px; min-height:515px;">
  <div id="row">
    <aside  class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    
    <div class="col-md-8 maincontent" style="border:1px solid #ccc; padding:15px;">
    	<h1>Manage Account</h1>
    	<div class="col-sm-6">
        	  <div class="error"><?php CommonFunctions::displayErrMsg(); ?></div>
        	 <div id="showError" class="showError"></div>
        	<form role="form" method="post" name="frmEditAccount" id="frmEditAccount" action="adminajax/edit_account.php">
        		
                <div class="form-group">
    				<label for="email">Name:</label>
  					 <input type="text"name="user_name" id="user_name" placeholder="Name" value="<?php echo $objCurPage->user_name; ?>"class="form-control" readonly="readonly">
 				 </div>
                
                <div class="form-group">
    				<label for="email">Email address:</label>
  					 <input type="email" name="user_email" id="user_email" value="<?php echo $objCurPage->user_email?>"  class="form-control" >
 				 </div>
                 
                 <button type="submit" class="btn btn-default">Submit</button>
                 <input type="hidden" name="task" id="task" value="2" />
                 <input type="hidden" name="user_id" value="<?php echo $objCurPage->user_id;?>" />
                 <input type="hidden" name="postMe" value="Y" />
            	
        	</form>
        </div>
        <div class="col-sm-6">&nbsp;</div>
     
    </div>
    
    <div class="col-sm-1">&nbsp;</div>
    
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>