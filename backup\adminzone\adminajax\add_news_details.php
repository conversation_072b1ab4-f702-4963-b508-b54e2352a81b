<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	
	public $app_config;
	
	public $prec_edit;
	public $maxfilesize;
	public $file_path;
	public $news_id;
	public $news_upload, $announcement_headline, $announcement_details,$announcement_date;
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		$this->maxfilesize = 4*1024*1024;
		$this->file_path = "../../news_files/";
		//--- Set Current Module Name ---
		$this->module_name = "News Details";
		$this->cur_page_url = "add_news_details.php";
		$this->list_page_url = "add_news_details.php";
		
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
				
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}

	}
	
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->news_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->news_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->news_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->news_id = 0;
			}
		}
		return 1;
	}
	//==== Create New Record ====
	public function createPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}
		return 1;
	}
	//==== Edit Existing Record ====
	public function editPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
        
		if ($this->news_id == 0)

		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}
		return 1;
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{			
		$arr_date = explode("-",$_POST["news_date"]);
		$this->announcement_date = $arr_date[2]."-".$arr_date[0]."-".$arr_date[1];
		$this->announcement_headline = CommonFunctions::replaceChars($_POST["heading"], 0);
		$this->announcement_details = CommonFunctions::replaceChars($_POST["details"], 0);
					
		if(isset($_POST["news_upload_edit"]))
		{
			$this->news_upload = $_POST["news_upload_edit"];			
		}
		return 1;
	}
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{						
		$myerr = "";
		$file_ext = "doc,pdf,xls,xlsx,docx,jpg,jpeg";
		
		if($this->announcement_date == "")
		{
			$myerr = "Please Select Date";
		}
		else if(trim($this->announcement_headline) == "")
		{
			$myerr = "Please Specify Headline";
		}
		if($myerr == "")
		{
			if(!empty($_FILES['news_upload']['name']))
			{
				if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['news_upload']['name']), $file_ext) == 0)
				{
					$myerr = "Un-supported file format, please upload files type - " . $file_ext;
				}
				else if(($_FILES['news_upload']['size'] > $this->maxfilesize))
				{
					$myerr = "File size should not be more than 4MB";
				}
			}		
		}
		if($myerr != "")
		{			
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		//--- Begin Transaction		
		$this->link_id->autocommit(FALSE);
				
		if($this->task == 1)
		{  	
			//--- Insert Data			
			$tmpSql = "INSERT INTO news(news_id, heading, news_date, details, attachmemt, is_new) 
					   VALUES(null, "
					   ."'".addslashes($this->announcement_headline)
					   ."', '".$this->announcement_date."', '"
					   .addslashes($this->announcement_details)."','','1')";
					
			$rs = $this->link_id->query($tmpSql);				
			
			$this->news_id = mysqli_insert_id($this->link_id);
									
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";
				//printf("error: %s\n", mysqli_errno($this->link_id));				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}			
			else
			{				
				$this->news_upload();	
				$tmpSql2 = "UPDATE news SET attachmemt = '" . $this->news_upload ."'"
					    . " WHERE news_id = " . $this->news_id;
				
				$rs2 = $this->link_id->query($tmpSql2);
						
				$this->link_id->commit();			
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
		}
		else if($this->task == 2)
		{
		    $upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "news", "attachmemt", "","news_id=".$this->news_id);
		
			if(isset($_FILES['news_upload']['name']))
			{
				if(file_exists($this->file_path.$upload_old_file))
				{
					@unlink($this->file_path.$upload_old_file);	
				} 
				
				$this->news_upload();
			}
						
			$tmpSql = "UPDATE news SET " 					
					. "  heading = '" . addslashes($this->announcement_headline) ."'"
					. ", news_date = '" . $this->announcement_date ."'"
					. ", details = '" . addslashes($this->announcement_details) ."'"
					. ", attachmemt = '" . $this->news_upload ."'"
					. "  WHERE news_id = " . $this->news_id;
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();				
			}
		
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{			
			return 1;
		}
	}
	
		//Upload File
   public function news_upload()
   {
		if(isset($_FILES['news_upload']['name']))
		{	
			$ext = strtolower(substr(strrchr($_FILES["news_upload"]["name"], "."), 1));
			$name = strtolower(substr($_FILES["news_upload"]["name"], 0, strpos($_FILES["news_upload"]["name"], ".")));
			$path = $this->file_path . str_replace(" ", "_", $name) . "_" .$this->news_id. "." . $ext;
					
			$file_name = $path;
			
			if(move_uploaded_file($_FILES['news_upload']['tmp_name'], $file_name))
			{
				$this->news_upload = (substr($file_name, strrpos($file_name, "/")+1));
			}
			else
			{
				$myerr = "File couldn't be uploaded";
			}
		}
	}		
}

$objCurPage = new CurrentPage();

?>