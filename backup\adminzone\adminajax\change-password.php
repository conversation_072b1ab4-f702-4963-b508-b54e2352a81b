<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");


/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	
	public $app_config;
	public $user_old_pwd, $user_new_pwd, $user_cpwd, $user_id;
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}

		//--- Set Current Module Name ---
		$this->module_name = "Change Password";
		$this->cur_page_url = "change-password.php";
		$this->list_page_url = "change-password.php";
		
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 1;
				//sheader("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}

	}
	public function editPart()
	{
		
		//--- Get Record Id ---
		//$this->getRecordId();
     	
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{

				$this->saveData();
			}
		}
		return 1;
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}

	//==== Create New Record ====
	public function createPart()
	{	
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();			
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}
		return 1;
	}

	

	//=== Initialize Form Data ====
	public function initFormData()
	{
		return 1;
	}

	public function readFormData()
	{
		$this->user_id = CommonFunctions::replaceChars($_POST["user_id"],0);
		$this->user_new_pwd = CommonFunctions::replaceChars($_POST["user_new_pwd"],0);
		$this->user_cpwd = CommonFunctions::replaceChars($_POST["user_cpwd"],0);

		return 1;
	}
	
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{	 		
		$myerr = "";
		$number = preg_match('@[0-9]@', $this->user_new_pwd);
		$uppercase = preg_match('@[A-Z]@', $this->user_new_pwd);
		$lowercase = preg_match('@[a-z]@', $this->user_new_pwd);
		$specialChars = preg_match('@[^\w]@', $this->user_new_pwd);	

		if($this->user_new_pwd == "")
		{
			$myerr = "Please specify password";
		}
		//else if(strlen($this->user_new_pwd) < 8 || !$number || !$uppercase || !$lowercase || !$specialChars)
		//else if(!preg_match("#.*^(?=.{8,20})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*\W).*$#", $this->user_new_pwd))
		else if(!$number || !$uppercase || !$lowercase || !$specialChars)
		{
			$myerr="New password must contain at least one number, one upper case letter, one lower case letter and one special character.";
		}
		else if(strlen($this->user_new_pwd) <= 4)
		{
			$myerr="Your password is poor.";
		}
		else if(strlen($this->user_new_pwd) <= 6)
		{
			$myerr="Your password is weak.";
		}
		else if(strlen($this->user_new_pwd) > 15)
		{
			$myerr="Your password should not be more than 15 characters.";
		}
		else if($this->user_cpwd == "")
		{
			$myerr="Please specify confirm password";
		}
		/*else if(!preg_match("#.*^(?=.{8,20})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*\W).*$#", $this->user_cpwd))
		{
			$myerr="Password must be at least 8 characters in length and must contain at least one number, one upper case letter, one lower case letter and one special character.";
		}*/
		else if($this->user_cpwd != $this->user_new_pwd)
		{
			$myerr="Password not matched";
		}
		
		if($myerr != "")
		{			
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		
		$myerr = "";
		//--- Begin Transaction		
		$this->link_id->autocommit(FALSE);
				
		if($this->task == 1)
		{			
			
		}
		else if($this->task == 2)
		{
			//echo 'task-2';
		    $tmpSql = "UPDATE admin_details SET " 
					. " user_password = '" . md5($this->user_new_pwd) ."'"
					. ", user_date = '" . date('Y-m-d') ."'"
					. "  WHERE user_id=".$this->user_id."";
	
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be Updated";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();				
			}
		
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{			
			return 1;
		}
	}		
}

$objCurPage = new CurrentPage();

?>