<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/interface.AppPage.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	
	public $app_config;
	
	public $alumni_id;
	public $user_password;
	public $user_password_enc;
	public $user_email;
	public $user_cpassword;	
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		//--- Set Current Module Name ---
		$this->module_name = "Chane Password";
		$this->cur_page_url = "ajax_change_pwd.php";
		$this->list_page_url = "ajax_change_pwd.php";
		
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createUser();
				break;
			case 2:
				//--- Edit Record
				$this->editUser();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}

	}



	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->alumni_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->alumni_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->alumni_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->alumni_id = 0;
			}
		}
		
		return 1;
	}


	//==== Create New Record ====
	public function createUser()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editUser()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		if ($this->alumni_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			//--- Add Mode. Initialize field values to blank or zero or null
			$this->alumni_id = 0;			
			$this->user_password = "";
			$this->user_password_enc = "";
			//$this->user_email = "";
		}
		else if($this->task == 2)
		{
			//--- Edit Mode. Initialize fields by getting values from Database
			$tmpSql = "SELECT *"
				. " FROM tbl_alumni "
				. " WHERE alumni_id = " . $this->alumni_id;
				
			$rs = $this->link_id->query($tmpSql);
						
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found...";
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
				die("_");
			}
			else
			{
				$this->alumni_id = $rec["alumni_id"];
				$this->user_password = $rec["show_pwd"];
				$this->user_password_enc = $rec["password"];				
				//$this->user_email = $rec["email"];
			}
		
		}
		
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{
		$this->user_password = CommonFunctions::replaceChars($_POST["user_password"], 0);
		$this->user_cpassword=CommonFunctions::replaceChars($_POST["user_cpassword"], 0);
		//$this->user_email = CommonFunctions::replaceChars($_POST["user_email"], 0);	

		return 1;
	}
	
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";

		if($this->user_password == "")
		{
			$myerr ="Please specify new password";
		}			
		else if($this->user_cpassword == "")
		{
			$myerr ="Please specify confirm password";
		}
		else if($this->task == 1 && $this->user_password != "" && $this->user_password != $this->user_cpassword)
		{
			$myerr ="Password and Confirm Password doesnot match";
		}
		else if($this->task == 2 && $this->user_password != "" || $this->user_cpassword != "")
		{
			if($this->task == 2 && $this->user_password == "")
			{
				$myerr ="Password is not specified";
			}			
			else if($this->task == 2 && $this->user_cpassword == "")
			{
				$myerr ="Confirm Password is not specified";
			}
			else if($this->task == 2 && $this->user_password != "" && $this->user_password != $this->user_cpassword)
			{
				$myerr ="Password and Confirm Password doesnot match";
			}
		}
		
							
		if($myerr != "")
		{
			//$_SESSION['app_error'] = $myerr;
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}

	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction 
		$this->link_id->autocommit(FALSE);
	
		if($this->task == 1)
		{						
			//--- Insert Data
			$tmpSql = "INSERT INTO tbl_alumni(alumni_id, show_pwd, password)" 
				. " VALUES(null"
				. ", '" . $this->user_password . "'"
				. ", md5('" . $this->user_password . "')"
				. ")";
			
			$rs = $this->link_id->query($tmpSql);
			if($this->link_id->affected_rows == -1)
			{				
				$myerr = "Data could not be saved";
				$this->link_id->rollback();
				echo $myerr;
				exit();
			}			
			else
			{
				$this->link_id->commit();
				$_SESSION["app_message"] = "Profile successfully saved";
				echo '1';
				exit();				
			}
		}
		else if($this->task == 2)
		{
			$tmpSql = "UPDATE tbl_alumni SET "
			."show_pwd = '". $this->user_password ."'"
			.", password = md5('". $this->user_password ."')"
			. " WHERE alumni_id = " . $this->alumni_id . "";	
			//echo $tmpSql;exit;
			
			$rs = $this->link_id->query($tmpSql);		
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();				
				echo $myerr;
				exit();
			}			
			else
			{
				$this->link_id->commit();
				$_SESSION["app_message"] = "Password successfully updated";
				echo '1';
				exit();				
			}
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}

$objCurPage = new CurrentPage();

?>