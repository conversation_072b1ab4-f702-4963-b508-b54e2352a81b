<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
     public $link_id;          // Database Link
     public $module_name;     // Current Module Name
     public $task;               // Type of Task (1: Add, 2: Edit, 0: Nothing....)
     public $cur_page_url;     // Current Page URL
     public $list_page_url;     // List Page URL
     public $sortby;               //--- sortby: 1/2/3/4...    
     public $sortmode;          //--- sortmode: asc/desc
     public $app_config;
     public $placement_notice_id;
     public $file_path, $maxfilesize, $details, $upload_file_edit;
     public $title, $weblink, $placement_notice_date, $upload_file;

     //=== Class Constructor ===/
     function __construct()
     {
          global $config;          // Global Config Settings

          //--- Connect TO Database ---
          $this->link_id = DatabaseManager::iconnectDB();
          if (!$this->link_id) {
               die(DatabaseManager::isqlError());
          }
          $this->app_config = $config;
          //--- Set Current Module Name ---
          $this->module_name = "Placement Notice Details";
          $this->cur_page_url = "placement_notice_details.php";
          $this->list_page_url = "placement_notice_list.php";

          //--- Check User Access permissions
          UserAccess::checkUserLogin("", "admin");

          //--- Get Sort Index and Sort Order
          $this->sortby = CommonFunctions::getSortIndex(1);
          $this->sortmode = CommonFunctions::getSortDirection("asc");

          $this->file_path = "../placement_files/placement_notice/";

          //--- Get Task Type (Add/Edit) and Current Record Id ---
          $this->getTaskType();
          //echo($this->task); exit;	
          $this->maxfilesize = 4 * 1024 * 1024;

          //--- Execute a Task ---
          switch ($this->task) {
               case 1:
                    //--- Add Record
                    $this->createPlacementNotice();
                    break;
               case 2:
                    //--- Edit Record
                    $this->editPlacementNotice();
                    break;
               default:
                    //echo("i'm 0"); exit;
                    $this->task = 0;
                    header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
                    die("_");
          }
     }
     //=== Class Destructor ===/
     function __destruct()
     {
          //--- Disconnect from Database ---
          //DatabaseManager::disconnectDB($this->link_id);
     }
     public function getTaskType()
     {
          if (isset($_POST["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
          } else if (isset($_GET["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
          } else {
               $this->task = 0;
          }
          if ($this->task == 0) {
               $this->task = 1;
          }
          return 1;
     }
     //==== Get Current Record Id ====
     public function getRecordId()
     {
          if ($this->task == 1) {
               $this->placement_notice_id = 0;
          } else if ($this->task == 2) {
               if (isset($_GET["recid"])) {
                    $this->placement_notice_id = CommonFunctions::replaceChars($_GET["recid"], 0);
               } else if (isset($_POST["recid"])) {
                    $this->placement_notice_id = CommonFunctions::replaceChars($_POST["recid"], 0);
               } else {
                    $this->placement_notice_id = 0;
               }
          }

          return 1;
     }

     //==== Create New Record ====
     public function createPlacementNotice()
     {
          //--- Get Record Id ---
          $this->getRecordId();

          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          }
          return 1;
     }
     //==== Edit Existing Record ====
     public function editPlacementNotice()
     {
          //--- Get Record Id ---
          $this->getRecordId();

          if ($this->placement_notice_id == 0) {
               $_SESSION['app_error'] = "Record not found...";
               header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
               die("_");
          }
          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          }
          return 1;
     }
     //=== Initialize Form Data ====
     public function initFormData()
     {
          if ($this->task == 1) {
               $this->placement_notice_id = 0;
               $this->title = "";
			   $this->details = "";
			   $this->weblink = "";
               $this->placement_notice_date = date('m-d-Y');
          } else if ($this->task == 2) {
               //--- Edit Mode. Initialize fields by getting values from Database
               $tmpSql = "SELECT * "
                    . " FROM tbl_placement_notice "
                    . " WHERE placement_notice_id = " . $this->placement_notice_id;
               $rs = $this->link_id->query($tmpSql);

               if ((!$rs) || (!($rec = $rs->fetch_array()))) {
                    $_SESSION['app_error'] = "Record not found";
                    header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
                    die("_");
               } else {
                    $this->placement_notice_id = $rec["placement_notice_id"];
                    $this->title = stripslashes($rec["title"]);
					$this->details = stripslashes($rec["details"]);
					$this->weblink = stripslashes($rec["weblink"]);
					$this->placement_notice_date = CommonFunctions::formatMyDateTime($rec["placement_notice_date"],"m-d-Y");
                    $this->upload_file = $rec["attachmemt"];
                    $this->upload_file_edit = $this->upload_file;
               }
          }
          return 1;
     }
     //=== Read Form Data ====
     public function readFormData()
     {
          return 1;
     }
     //==== Validate Form Data (Returns: 1 / 0) ====
     public function validateFormData()
     {
          $myerr = "";
          if ($myerr != "") {
               $_SESSION['app_error'] = $myerr;
               return 0;
          } else {
               return 1;
          }
     }
     //==== Save Data ====
     public function saveData()
     {
          $myerr = "";
          //--- In case of any error set the Session variable ---
          if ($myerr != "") {
               $_SESSION['app_error'] = $myerr;
               return 0;
          } else {
               //--- If no error and the user is available the page is already redirected to another page ---
               return 1;
          }
     }
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
     <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>

     <link rel="stylesheet" href="css/admin.css" type="text/css" />
     <link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
     <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
     <script src="//code.jquery.com/jquery-1.12.4.js"></script>
     <script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
     <script type="text/javascript" src="js/jquery.form.3.10.js"></script>

     <link href="css/font-awesome.min.css" rel="stylesheet" />
     <link href="css/bootstrap.min.css" rel="stylesheet" />
     <script type="text/javascript" src="js/tcal.js"></script>
     <link href="css/tcal.css" rel="stylesheet" type="text/css" />
     <!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->

     <script language="javascript" type="text/javascript">
          $(document).ready(function() {

               $("#loadingImage").hide();
               $('#cmdSavePlacemetNotice').click(function() {
                    $("#loadingImage").hide();
                    $('#frmPlacementNoticeDetail').submit();
                    $("#loaderImg").html('<img src="images/progress-bar.gif" />');
                    $("#loaderImg").show();
               });

               return false;
          });
     </script>

     <script language="javascript" type="text/javascript">

          function validateSave() {
               $('#frmPlacementNoticeDetail').submit();
               $("#loaderImg").html('<img src="images/progress-bar.gif" />');
               $("#loaderImg").show();
          }

          $('#frmPlacementNoticeDetail').ajaxForm({
               target: '',
               success: function(data) {
                    if (data) {
                         $("#loadingImage").hide();
                         if (data == 1) {
                              $("#loaderImg").empty();
                              $("#loaderImg").hide();
                              location.href = '<?php echo $objCurPage->list_page_url; ?>';
                              return false;
                         } else {
                              $("#loaderImg").empty();
                              $("#loaderImg").hide();
                              $('#showError').html(data);
                              return false;
                         }

                    }
                    return false;
               }
          });
     </script>

</head>

<body>
     <div id="header">
          <div class="container">
               <?php include_once("header.php") ?>
          </div>
     </div>
     <section class="container" style="margin-top:18px; min-height:520px;">
          <div id="row">
               <aside class="col-sm-3 sidebar sidebar-right">
                    <!--left_menu start here-->
                    <?php include_once("lftmenu.php"); ?>
               </aside>
               <!--left_menu ends here-->
               <div class="col-md-9 maincontent" style="border:1px solid #ccc;">
                    <!--<p>Date: <input type="text" id="datepicker"></p>-->
                    <div id="show_error"><?php CommonFunctions::displayErrMsg(); ?></div>
                    <div id="showError" style="text-align:center; color:#F00;"></div>
                    <form name="frmPlacementNoticeDetail" id="frmPlacementNoticeDetail" method="post" action="adminajax/ajax_placement_notice_details.php" enctype="multipart/form-data">
                         <table width="98%" cellpadding="4" cellspacing="0" border="0" class="dialogBaseShadow">
                              <tr>
                                   <td>
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" class="dialogBase">
                                             <tr>
                                                  <td class="dialogHeader">
                                                       <h1><?php if ($objCurPage->task == 1) {
                                                                 echo ("Add ");
                                                            } else {
                                                                 echo ("Edit ");
                                                            } ?> Placement Notice</h1>
                                                  </td>
                                             </tr>
                                             <tr>
                                                  <td>
                                                       <table width="100%" cellpadding="5" cellspacing="3" border="0" class="formTextWithBorder">
                                                            <tr>
                                                                 <td align="center" id="ref_link" colspan="2">
                                                                      <div class="tableRow">
                                                                           <div class="imgLoader" id="loaderImg" style="display:none; margin:0 auto"></div>
                                                                      </div>
                                                                 </td>
                                                            </tr>

                                                            <tr>
                                                                 <td colspan="2">&nbsp;</td>
                                                            </tr>
                                                            <tr>
                                                                 <td align="left" valign="middle" colspan="2">

                                                                      <?php if ($objCurPage->task == 1) { ?>
                                                                           <table width="100%" border="0" cellpadding="0" cellspacing="1">
                                                                               
                                                                               <tr valign="top">
                                                                                     <td width="26%" height="15">Date</td>
                                                                                     <td width="50%">
                                                                                          <input type="text" class="tcal form-control" name="placement_notice_date" value="<?php echo $objCurPage->placement_notice_date;?>" readonly />
                                                                                     </td>
                                                                               </tr>
                                                                                 
                                                                               <tr valign="top">
                                                                                     <td width="26%" height="15">Title</td>
                                                                                     <td width="50%">
                                                                                          <input type="text" name="title" id="title" value="" class="form-control" />
                                                                                     </td>
                                                                                </tr>
                                                                                
                                                                                <tr valign="top"> 
                                                                                      <td width="22%" height="15">Description</td>
                                                                                      <td width="25%">
                                                                                        <textarea name="details" class="form-control" rows="4" cols="50" style="height: 150px !important;"></textarea>
                                                                                      </td>
                                                                                </tr>
                                                                                
                                                                                <tr valign="top">
                                                                                     <td width="26%" height="15">Weblink</td>
                                                                                     <td width="50%">
                                                                                          <input type="text" name="weblink" id="weblink" value="" class="form-control" />
                                                                                     </td>
                                                                                </tr>

                                                                                <tr valign="top">
                                                                                     <td width="22%" align="left">Upload File<br />
                                                                                          <font size="1" color="#666666"> (File Types .pdf, .doc, .docx, .xls, .xlsx Max Size: 4MB)</font>
                                                                                     </td>
                                                                                     <td width="50%" align="left">
                                                                                          <input type="file" name="placement_notice_file_0" value="" />
                                                                                          <input type="hidden" name="placement_notice_file_HX" value="1" />
                                                                                     </td>
                                                                                </tr>

                                                                           </table>

                                                                      <?php
                                                                      } else { ?>
                                                                           <div id="edit_placement_notice_div">

                                                                                <table width="100%" border="0" cellpadding="0" cellspacing="1">
                                                                                
                                                                                  <tr valign="top">
                                                                                      <td width="26%" height="15">Date</td>
                                                                                      <td width="50%">
                                                                                           <input type="text" class="tcal form-control" name="placement_notice_date" value="<?php echo $objCurPage->placement_notice_date;?>" readonly />
                                                                                      </td>
                                                                                  </tr>
                                                                                     
                                                                                  <tr valign="top">
                                                                                          <td width="26%" height="15">Title</td>
                                                                                          <td width="50%">
                                                                                               <input type="text" name="title" value="<?php echo $objCurPage->title; ?>" class="form-control" />
                                                                                          </td>
                                                                                  </tr>
                                                                                  
                                                                                  <tr valign="top"> 
                                                                                      <td width="22%" height="15">Description</td>
                                                                                      <td width="25%">
                                                                                        <textarea name="details" class="form-control" rows="4" cols="50" style="height: 150px !important;"><?php echo $objCurPage->details;?></textarea>
                                                                                      </td>
                                                                                  </tr>
                                                                                
                                                                                  <tr valign="top">
                                                                                     <td width="26%" height="15">Weblink</td>
                                                                                     <td width="50%">
                                                                                          <input type="text" name="weblink" id="weblink" value="<?php echo $objCurPage->weblink;?>" class="form-control" />
                                                                                     </td>
                                                                                  </tr>

                                                                                  <tr valign="top">
                                                                                          <td width="22%" align="left">Upload File<br />
                                                                                               <font size="1" color="#666666"> (File Types .pdf, .doc, .docx, .xls, .xlsx Max Size: 4MB)</font>
                                                                                          </td>
                                                                                          <td width="50%" align="left">
                                                                                               <input type="file" name="placement_notice_file" value="" />
                                                                                               <input type="hidden" name="placement_notice_file_edit" id="placement_notice_file_edit" value="<?php echo $objCurPage->upload_file_edit; ?>" />&nbsp;&nbsp;
                                                                                               <?php if ($objCurPage->upload_file_edit != "") { ?>
                                                                                                    <i>
                                                                                                         <font color="#003399"><small>(Last file: <b><a target="_blank" href="<?php echo $objCurPage->file_path . $objCurPage->upload_file_edit;  ?>" class="generalink">View</a></b>)</small></font>
                                                                                                    </i>
                                                                                               <?php } ?>
                                                                                          </td>
                                                                                     </tr>
                                                                                </table>
                                                                           </div>
                                                                      <?php } ?>
                                                                 </td>
                                                            </tr>

                                                            <tr>
                                                                 <td align="left" valign="middle" colspan="2">&nbsp;</td>
                                                            </tr>
                                                            <tr>
                                                                 <td align="left" valign="middle" colspan="2">&nbsp;</td>
                                                            </tr>
                                                            <tr id="rowExit">
                                                                 <td align="right" valign="middle" colspan="2">
                                                                      <div class="imgLoader" id="loaderImg" style="display:none;"></div>

                                                                      <input type="hidden" name="recid" id="recid" value="<?php echo $objCurPage->placement_notice_id; ?>" />
                                                                      <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />
                                                                      <input type="hidden" name="postMe" value="Y" />
                                                                      <input type="button" name="cmdSavePlacemetNotice" id="cmdSavePlacemetNotice" value="<?php if ($objCurPage->task == 1) {
																			echo ("Save");
																	   } else {
																			echo ("Update");
																	   } ?>" class="btn btn-default" />
                                                                      <input type="button" name="cmdExitPlacemetNotice" id="cmdExitPlacemetNotice" class="btn btn-default" onClick="window.location.href='placement_notice_list.php'" value="Exit" />
                                                                 </td>
                                                            </tr>
                                                       </table>
                                                  </td>
                                             </tr>
                                        </table>
                                   </td>
                              </tr>
                         </table>
                    </form>

               </div>
          </div>
     </section>
     <?php include_once('footer.php'); ?>
</body>

</html>