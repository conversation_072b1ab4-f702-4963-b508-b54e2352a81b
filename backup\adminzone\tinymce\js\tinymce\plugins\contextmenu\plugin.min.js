tinymce.PluginManager.add("contextmenu",function(e){var t;e.on("contextmenu",function(n){var i;if(n.preventDefault(),i=e.settings.contextmenu||"link image inserttable | cell row column deletetable",t)t.show();else{var o=[];tinymce.each(i.split(/[ ,]/),function(t){var n=e.menuItems[t];"|"==t&&(n={text:t}),n&&(n.shortcut="",o.push(n))});for(var a=0;a<o.length;a++)"|"==o[a].text&&(0===a||a==o.length-1)&&o.splice(a,1);t=new tinymce.ui.Menu({items:o,context:"contextmenu"}),t.renderTo(document.body)}var r={x:n.pageX,y:n.pageY};e.inline||(r=tinymce.DOM.getPos(e.getContentAreaContainer()),r.x+=n.clientX,r.y+=n.clientY),t.moveTo(r.x,r.y),e.on("remove",function(){t.remove(),t=null})})});