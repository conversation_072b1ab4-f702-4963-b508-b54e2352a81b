<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $app_config;
	public $dept_id;
	public $faculty_id;
	public $faculty_name,$faculty_qual,$faculty_email,$upload_file;
	public $file_path, $maxfilesize, $faculty_img_edit, $faculty_name_arr, $upload_file_arr, $faculty_qual_arr, $faculty_email_arr;

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		//--- Set Current Module Name ---
		$this->module_name = "Faculty Details";
		$this->cur_page_url = "faculty_details.php";
		$this->list_page_url = "faculty_list.php";
		
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		$this->file_path = "../faculty_images/";
						
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
			//echo($this->task); exit;	
		$this->maxfilesize = 2*1024*1024;	
			
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createNewsDetail();
				break;
			case 2:
				//--- Edit Record
				$this->editNewsDetail();
				break;
			default:
				//echo("i'm 0"); exit;
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				die("_");
		}
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		return 1;
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->faculty_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->faculty_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->faculty_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->faculty_id = 0;
			}
		}
		
		return 1;
	}


	//==== Create New Record ====
	public function createNewsDetail()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}		
		return 1;
	}
	//==== Edit Existing Record ====
	public function editNewsDetail()
	{
		//--- Get Record Id ---
		$this->getRecordId();
        
		if ($this->faculty_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			die("_");
		}
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		return 1;
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			$this->faculty_id = 0;	
			$this->faculty_name_arr = array();
			$this->faculty_qual_arr = array();
			$this->faculty_email_arr = array();			
			$this->upload_file_arr = array();
					
		}
		else if($this->task == 2) 
		{
			//--- Edit Mode. Initialize fields by getting values from Database
			$tmpSql = "SELECT * "
				. " FROM tbl_faculty "
				. " WHERE faculty_id = " . $this->faculty_id;
										
							
			$rs = $this->link_id->query($tmpSql);
						
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found";
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
				die("_");
			}
			else
			{
				$this->faculty_id = $rec["faculty_id"];
				$this->dept_id = $rec["dept_id"];
				$this->faculty_name = stripslashes($rec["faculty_name"]);	
				$this->faculty_qual = stripslashes($rec["faculty_qual"]);
				$this->faculty_email = $rec["faculty_email"];
				$this->upload_file = $rec["attachmemt"];
				$this->faculty_img_edit = $this->upload_file; 
						
			}
				
		}
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{	
		return 1;
	}
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";										
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";					
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>

<link rel="stylesheet" href="css/admin.css" type="text/css" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="//code.jquery.com/jquery-1.12.4.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>

<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script type="text/javascript" src="js/tcal.js"></script>
<link href="css/tcal.css" rel="stylesheet" type="text/css" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->

<script language="javascript" type="text/javascript">
$(document).ready(function(){
	
	$("#loadingImage").hide();	
	
	<?php if($objCurPage->task==1) {?>
		displayGridItem();
	<?php } ?>
	
	$('#cmdSaveFaculty').click(function(){	
	    $("#loadingImage").hide();	
		$('#frmFacultyDetail').submit();
		$("#loaderImg").html('<img src="images/progress-bar.gif" />');
	    $("#loaderImg").show();
	});
	
	return false;
});

</script>

<script language="javascript" type="text/javascript">

function displayGridItem()
{
	//add_item 1
	$('.addVidButton').click(function() {
		
		if($('.vidItem').size() >= 2){
			$(".addVidButton").hide();
		}
		var oval, otxt;
		$('#vidContainer').append('<div class="vidItem" style=" margin-bottom:10px;">' + $(".vidItemSample").html() + '</div>');	
			
			//this code is to set focus on first textbox	
			//========================================================	
			var new_div = $('#vidContainer .vidItem').last();
			
			if(new_div.size() > 0){
				new_div.find("input").each(function(n) {
					if(n == 0){
						$(this).focus();
					}
				});
				
				new_div.find("input[type='file']").each(function() {
					$(this).attr('name', 'faculty_img_' + $('#vidContainer .vidItem').size() );
					$(this).attr('idx', $('#vidContainer .vidItem').size() );
				});
				
				new_div.find("input[type='radio']").each(function() {
					$(this).attr('name', 'mark_new_' + $('#vidContainer .vidItem').size() );
					$(this).attr('idx', $('#vidContainer .vidItem').size() );
				});

				
				//this to find all a tag element
				new_div.find("a").each(function() {
					$(this).click(function(){	
						if($('#vidContainer .vidItem').size() > 1){		
							$(".addVidButton").show();
							var objVidItm = $(this);
							var curIdx = $($(this).parent().parent().parent().find("input[type='file']")[0]).attr('idx');
							curIdx = parseInt(curIdx);
							$(this).parent().parent().parent().parent().parent().siblings(".vidItem").each(function(){
								cfile = $(this).find("input[type='file']")[0];
								//cnew = $(this).find("input[type='radio']")[0];
								//console.log(cnew);
								//cnew_hdn = $(this).find("input[name='mark_new_HX']")[0];
								anIdx = $(cfile).attr('idx');
								//alert(anIdx);
								anIdx = parseInt(anIdx);
								if(anIdx > curIdx) {
									$(cfile).attr('name', 'news_file_' + (anIdx-1) );
									$(cfile).attr('idx', (anIdx-1) );
									//$(cnew).attr('name', 'mark_new_' + (anIdx-1) );
									//$(cnew).attr('idx', (anIdx-1) );
									//$(cnew_hdn).attr('id', 'mark_new_HX_' + (anIdx-1) );
									
								}
							});
										
							$(objVidItm).parent().parent().parent().parent().parent().remove();
							
						}else{
							$(this).parent().parent().find("input:text").each(function(){
								$(this).val("");
							});
						}
	
					});
				});	
			}
			//call to initialize tcal after clickind add more
			f_tcalInit();	
				
	});
	
	$(".vidItem").find("a").each(function() {

		$(this).click(function(){			
			if($('#vidContainer .vidItem').size() > 1){					
				$(this).parent().parent().parent().parent().parent().remove();
			}else{
				$(this).parent().parent().find("input:text").each(function(){
					$(this).val("");
				});
			}
		});
	});
	
	<?php
	if( $objCurPage->task == 1){
		 if(count($objCurPage->upload_file_arr) == 0 ) { ?>
			$('.addVidButton').trigger("click");  //this event trigger the click event of addVidButton
		                                       // for the first time form is loaded
	<?php }
	} 
	?>
}

function validateSave()
{
	$('#frmFacultyDetail').submit();
	$("#loaderImg").html('<img src="images/progress-bar.gif" />');
	$("#loaderImg").show();
}

$('#frmFacultyDetail').ajaxForm({	
	target: '',
	success: function(data){
		if(data){
			$("#loadingImage").hide();				
			if(data == 1)
			{
				$("#loaderImg").empty();
				$("#loaderImg").hide();
				location.href='<?php echo $objCurPage->list_page_url; ?>';
				return false;					
			}
			else
			{
			    $("#loaderImg").empty();
				$("#loaderImg").hide();
				$('#showError').html(data);
				return false;
			}
			
		}
		return false;
	}			
});

</script>

</head>
<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:520px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-9 maincontent" style="border:1px solid #ccc;">
    <!--<p>Date: <input type="text" id="datepicker"></p>-->
 			<div id="show_error"><?php CommonFunctions::displayErrMsg(); ?></div>
            <div id="showError" style="text-align:center; color:#F00;"></div>
 <form name="frmFacultyDetail" id="frmFacultyDetail" method="post" action="adminajax/ajax_faculty_details.php" enctype="multipart/form-data">     
            <table width="98%" cellpadding="4" cellspacing="0" border="0" class="dialogBaseShadow">
                <tr>
                    <td>                    
                        <table width="100%" cellpadding="0" cellspacing="0" border="0" class="dialogBase">
                            <tr>
                                <td class="dialogHeader"><h1><?php if($objCurPage->task == 1){ echo("Add "); }else{ echo("Edit "); } ?> New Faculty</h1></td>
                          </tr>
                            <tr>
                                <td>
                     <table width="100%" cellpadding="5" cellspacing="3" border="0" class="formTextWithBorder">
                        <tr>
                            <td align="center" id="ref_link" colspan="2">
                                <div class="tableRow">
                                    <div class="imgLoader" id="loaderImg" style="display:none; margin:0 auto"></div>
                                </div>
                            </td>
                        </tr> 
                        
                        <tr>
                            <td width="25%">&nbsp;Select Department</td>
                            <td width="75%" align="left" id="ref_link">
                               <select name="dept_id" id="dept_id" class="form-control" style=" width:35%;">
                                <option value="">Select Department</option>
                                <?php $tsql = "SELECT dept_id, dept_name "
                                . " FROM tbl_department"
                                . " ORDER BY dept_id DESC";
                                    
                                    $rstyp = $objCurPage->link_id->query($tsql);
                                        
                                    if( (!$rstyp) || (!($rectyp = $rstyp->fetch_array())) )
                                    {
                                        
                                    }
                                    else
                                    {
                                        do { 
                                            ?>
                                                <option style="display:block;" value="<?php echo $rectyp["dept_id"]; ?>" <?php if($objCurPage->dept_id == $rectyp["dept_id"]) {?>selected<?php } ?>><?php echo $rectyp["dept_name"]; ?></option>
                                            <?php 
                                           }while($rectyp = $rstyp->fetch_array());
                                    }		 
                                ?>
                            </select>
                            </td>
                        </tr> 
                        
                        <tr><td colspan="2">&nbsp;</td></tr>   

                        <tr>                                 
                          <td align="left" valign="middle" colspan="2">
                          
                            <?php if($objCurPage->task == 1)
                            {?>
                            
                            <div id="vidContainer">
                                <div>
                                   <div id="qvar_addmore" align="center"><a title="Add" class="addVidButton" style="cursor:pointer;float:right;"><img height="16" align="absmiddle" width="16" src="images/b_add.png" border="0" /> Add more</a>&nbsp;&nbsp;</div>
                                   
                                </div>
                                
                                <div class="vidItemSample" style="display:none; margin-bottom:10px;">
                                    <table width="100%" border="0" cellpadding="0" cellspacing="1">
                                      
                                      <tr valign="top"> 
                                         <td width="26%" height="15">Name</td>
                                          <td width="50%">
                                            <input type="text" name="faculty_name[]" value="" class="form-control" />
                                          </td>
                                      </tr>
                                      
                                      <tr valign="top"> 
                                         <td width="26%" height="15">Qualification</td>
                                          <td width="50%">
                                            <input type="text" name="faculty_qual[]" value="" class="form-control" />
                                          </td>
                                      </tr>
                                      
                                      <tr valign="top"> 
                                         <td width="26%" height="15">Email</td>
                                          <td width="50%">
                                            <input type="email" name="faculty_email[]" value="" class="form-control" />
                                          </td>
                                      </tr>
                                      
                                      <tr valign="top"> 
                                          <td width="22%" align="left">Upload photo<br />
                                          <font size="1" color="#666666"> (File Types .jpg,.jpeg,.png Max Size: 2MB)</font></td>
                                          <td width="50%" align="left">
                                          <input type="file" name="faculty_img_0" value="" />
                                          <input type="hidden" name="faculty_img_HX[]" value="1" />
                                          </td>
                                      </tr>
                                      
                                      <tr valign="top"> 
                                         <td width="12%" align="right">&nbsp;</td>
                                         <td width="12%" align="right">
                                          <a title="Remove" class="delVidButton"><img height="16" align="absmiddle" width="16" src="images/b_drop.png" border="0" /></a>
                                          </td>
                                      </tr>
                                      
                                    </table>
                                </div>
                                
                                
                            </div>
                            
                             <?php	
                                }
                                
                                else
                                {?>
                                    <div id="edit_news_div">
                                    
                                       <table width="100%" border="0" cellpadding="0" cellspacing="1">
                                       
                                          <tr valign="top"> 
                                             <td width="26%" height="15">Title</td>
                                              <td width="50%">
                                                <input type="text" name="faculty_name_edit" value="<?php echo $objCurPage->faculty_name; ?>" class="form-control" />
                                              </td>
                                          </tr>
                                          
                                          <tr valign="top"> 
                                             <td width="26%" height="15">Qualification</td>
                                              <td width="50%">
                                                <input type="text" name="faculty_qual_edit" value="<?php echo $objCurPage->faculty_qual; ?>" class="form-control" />
                                              </td>
                                          </tr>
                                          
                                          <tr valign="top"> 
                                             <td width="26%" height="15">Email</td>
                                              <td width="50%">
                                                <input type="email" name="faculty_email_edit" value="<?php echo $objCurPage->faculty_email; ?>" class="form-control" />
                                              </td>
                                          </tr>
                                          
                                          <tr valign="top"> 
                                              <td width="22%" align="left">Upload photo<br />
                                              <font size="1" color="#666666"> (File Types .jpg,.jpeg,.png   Max Size: 2MB)</font></td>
                                              <td width="50%" align="left">
                                              <input type="file" name="faculty_img" value="" />
                                              <input type="hidden" name="faculty_img_edit" id="faculty_img_edit" value="<?php echo $objCurPage->faculty_img_edit;?>"  />&nbsp;&nbsp;
            <? if($objCurPage->faculty_img_edit != "") { ?>
            <i><font color="#003399"><small>(Last file: <b><a target="_blank" href="<?php echo $objCurPage->file_path . $objCurPage->faculty_img_edit ;  ?>" class="generalink">View</a></b>)</small></font></i>
            <? } ?>
                                              </td>
                                          </tr>
                                          
                                        </table>
                                    
                                    </div>
                                    
                               <?php }
                            ?>
                         </td>
                        </tr>     
                                                           
                        <tr>
                          <td align="left" valign="middle" colspan="2">&nbsp;</td>
                        </tr>
                        <tr>
                          <td align="left" valign="middle" colspan="2">&nbsp;</td>
                        </tr>
                        <tr id="rowExit">
                          <td align="right" valign="middle" colspan="2">
                            <div class="imgLoader" id="loaderImg" style="display:none;"></div>
                            
                            <input type="hidden" name="recid" id="recid" value="<?php echo $objCurPage->faculty_id; ?>" />
                            <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />                                            
                            <input type="hidden" name="postMe" value="Y" />      
                            <input type="button" name="cmdSaveFaculty" id="cmdSaveFaculty" value="<?php if($objCurPage->task==1) {echo("Save");} else {echo("Update");} ?>" class="btn btn-default" />                                                  
                            <!--<input type="button" name="cmdSaveFaculty" id="cmdSaveFaculty" class="btn btn-default" value="Save" />-->           
                            <input type="button" name="cmdExitFaculty" id="cmdExitFaculty" class="btn btn-default" onClick="window.location.href='faculty_list.php'" value="Exit" />	
                          </td>
                        </tr>                                 
                                    </table>                        
                                </td>
                            </tr>
                        </table>
                   </td>
               </tr>
           </table>           
       </form>

     </div>
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>