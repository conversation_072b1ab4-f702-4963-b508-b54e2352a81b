<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	
	public $app_config;
	public $user_name, $user_email,  $user_add, $user_id;
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}

		//--- Set Current Module Name ---
		$this->module_name = "Edit Account";
		$this->cur_page_url = "edit_account.php";
		//$this->list_page_url = "registration.php";
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 1;
				//sheader("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				exit();
		}

	}
	public function editPart()
	{
		//--- Get Record Id ---
		//$this->getRecordId();
     	
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				if ($this->user_email == '')
				{
					$_SESSION['app_error'] = "Record not found...";			
					exit();
				}
				
				//echo $this->user_email; exit('dfd');
				$this->saveData();
			}
		}
		return 1;
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}

	//==== Create New Record ====
	public function createPart()
	{	
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();			
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}
		return 1;
	}

	

	//=== Initialize Form Data ====
	public function initFormData()
	{
		return 1;
	}

	public function readFormData()
	{	
		
		$this->user_id= CommonFunctions::replaceChars($_POST["user_id"],0);
		$this->user_name = CommonFunctions::replaceChars($_POST["user_name"],0);
		$this->user_email = CommonFunctions::replaceChars($_POST["user_email"], 0);

		return 1;
	}
	
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{	
		$myerr = "";
		if($this->user_name == ""){
			$myerr = "please specify your name";
		}else if(trim($this->user_email == "")){
			$myerr = "please specify your email";
		}else if (!filter_var($this->user_email, FILTER_VALIDATE_EMAIL)) {
			$myerr = "Please specify vaild email";
		}
		if($myerr != "")
		{			
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		//--- Begin Transaction		
		$this->link_id->autocommit(FALSE);
				
	 if($this->task == 2)
		{
			
		    $tmpSql = "UPDATE admin_details SET " 					
					. "  user_name = '" . $this->user_name ."'"
					. ", user_email = '" . $this->user_email ."'"
					. "  WHERE user_id = '" . $this->user_id ."'";
			
			//echo $tmpSql; die("---");
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be Updated";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();				
			}
		}
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{			
			return 1;
		}
	}		
}

$objCurPage = new CurrentPage();
?>