/*OM*/

/*Change row colour*/
function sel(frm,cbox)
	{
	var ele=cbox;
	while (ele.tagName!="TR")
		{ele=ele.parentNode;}
	if(cbox.checked)
		{ele.className="selbkg"}
	else
		{ele.className="dselbkg"}
	}

/*Select all checkboxes*/
function selectAll(frm, chkall, chkname)
	{
	var cboxes = eval("document." + frm.name + "." + chkname);
	if(cboxes != null)
		{
		if(cboxes.length>0)
			{
			for(var m=0; m < cboxes.length; m++)
				{
				cboxes[m].checked = chkall.checked;
				sel(frm, cboxes[m]);
				}
			}
		else
			{
			cboxes.checked = chkall.checked;
			sel(frm, cboxes);
			}
		}
	}

/*Check if single checkbox is selected*/
function isSingleSelected(frm, chkname, recs)
	{
	var cbox;
	var cnt = 0;
	if(recs == 0)
		{
		alert("no record selected");
		return false;
		}

	for(var m=1; m <= recs; m++)
		{
		cbox = eval("document." + frm.name + "." + chkname + "_" + m);
		
		if(cbox != null)
			{
			if(cbox.checked == true)
				{cnt++;}
			}
		}

	if(cnt == 1)
		{return true;}
	else
		{
		alert("Please select only a single record to proceed...");
		return false;
		}
	}

/*Check if any (1+) checkbox is selected*/
function isAnySelected(frm, chkname, recs)
	{
	var cbox;
	var cnt = 0;
	if(recs == 0)
		{
		alert("no record selected");
		return false;
		}

	for(var m=1; m <= recs; m++)
		{
		cbox = eval("document." + frm.name + "." + chkname + "_" + m);
		if(cbox != null)
			{
			if(cbox.checked == true)
				{cnt++;}
			}
		}
	
	if(cnt >= 1)
	{
		
		return true;
	}
	else
		{
				
		alert("Please select one or more record to proceed...");
		return false;
		}
	}
	
	

function getMySelectedCheckboxValue(frm, chkname, recs, defaultval)
{
	var cbox, idlist;
	var cnt = 0;
	var idlist = "";
	if(recs == 0)
	{
		//alert("no record selected");
		idlist = defaultval;
		return idlist;
	}

	for(var m=1; m <= recs; m++)
	{
		//cbox = eval("document." + frm.name + "." + chkname + "_" + m);
		cbox = document.getElementById(chkname + "_" + m);
		if(cbox != null)
		{
			if(cbox.checked == true)
			{
				cnt++;
				if(idlist != "")
				{
					idlist = idlist + ",";
				}
				idlist = idlist + cbox.value;
			}
		}
	}

	if(idlist == "")
	{
		idlist = defaultval;
	}
	
	return idlist;
}


/*Get selected checkbox value */
function getSelectedCheckboxValue(frm, chkname, recs, defaultval)
{
		
	var cbox, idlist;
	var cnt = 0;
	var idlist = "";
	if(recs == 0)
	{
		//alert("no record selected");
		idlist = defaultval;
		return idlist;
	}

	for(var m=1; m <= recs; m++)
	{
		cbox = eval("document." + frm.name + "." + chkname + "_" + m);
		if(cbox != null)
		{
			if(cbox.checked == true)
			{
				cnt++;
				if(idlist != "")
				{
					idlist = idlist + ",";
				}
				idlist = idlist + cbox.value;
			}
		}
	}

	if(idlist == "")
	{
		idlist = defaultval;
	}
	
	return idlist;
}

/*Open Popup Window*/
var popUpWin=0;
function popUpWindow(URLStr,mywidth,myheight,myleft,mytop,myscroll)
	{
	if(popUpWin)
		{
		if(!popUpWin.closed) 
			{popUpWin.close();}
		}
	popUpWin = open(URLStr, 'popUpWin', 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=' + myscroll + ',resizable=no,copyhistory=yes,width='+mywidth+',height='+myheight+',left='+myleft+', top='+mytop+',screenX='+myleft+',screenY='+mytop+'');
	}

/*Open Popup Window 2*/
var popUpWin2=0;
function popUpWindow2(URLStr,mywidth,myheight,myleft,mytop,myscroll,myresize,mymenu,mystatus)
	{
	if(popUpWin2)
		{
		if(!popUpWin2.closed) 
			{popUpWin2.close();}
		}
	popUpWin2 = open(URLStr, 'popWin2', 'toolbar=no,location=no,directories=no,status=' + mystatus + ',menubar=' + mymenu + ',scrollbars=' + myscroll + ',resizable=' + myresize + ',copyhistory=yes,width='+mywidth+',height='+myheight+',left='+myleft+', top='+mytop+',screenX='+myleft+',screenY='+mytop+'');
	}


/*Check if single checkbox is selected*/
/*function isSingleSelected_bak(frm, chkname, recs)
	{
	if(recs == 0)
	  {
	  alert("no record");
	  return false;
	  }
	else
	  {
	  alert(eval("document." + frm.name + "." + chkname + "_" + 1));
	  alert("record found");
	  return false;
	  }
	
	var cboxes = eval("document." + frm.name + "." + chkname + "_" + 1);
	var cnt = 0;
	if(cboxes != null)
		{
		if(cboxes.length>0)
			{
			for(var m=0; m < cboxes.length; m++)
				{
				if(cboxes[m].checked == true)
					{cnt++;}
				}
			}
		else
			{
			if(cboxes.checked == true)
				{cnt++;}
			}
		}

	if(cnt == 1)
		{return true;}
	else
		{
		alert("Please select only a single row to proceed...");
		return false;
		}
	}
*/

/*
function getoptions(frm,srcList,targetList,location)
      {
		var objSrcList = eval(frm.name + '.' + srcList);
		var objTargetList = eval(frm.name + '.' + targetList);
		
		//var frm1 = document.frm;
		var prog = objSrcList.options[objSrcList.selectedIndex].value;
		params = "pcode=" + prog;


	    var XMLHttpRequestObject = false; 
   		
		var msxmlhttp = new Array( 
			'Msxml2.XMLHTTP.5.0', 
			'Msxml2.XMLHTTP.4.0', 
			'Msxml2.XMLHTTP.3.0', 
			'Msxml2.XMLHTTP', 
			'Microsoft.XMLHTTP'); 
		
		for (i=0;i<msxmlhttp.length;i++) 
		{ 
			try 
			{ 
				XMLHttpRequestObject = new ActiveXObject(msxmlhttp[i]); 
			} 
			catch (e) 
			{ 
				XMLHttpRequestObject = null; 
			} 
		} 
		if(!XMLHttpRequestObject && typeof XMLHttpRequest!= "undefined") 
		{ 
			XMLHttpRequestObject = new XMLHttpRequest(); 
		} 
		if (!XMLHttpRequestObject) 
		{ 
			XMLHttpRequestObject = false; 
		} 
		
        if(XMLHttpRequestObject) {
			
		var randomnumber;
		randomnumber = Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11);
		XMLHttpRequestObject.open("GET",location+"?" + params + "&a="+randomnumber, true); 
		
        XMLHttpRequestObject.onreadystatechange = function() 
          { 
		  	
            if (XMLHttpRequestObject.readyState == 4 && 
              XMLHttpRequestObject.status == 200) 
			{ 
			var tmpdiv;
	      
					var xmldoc = XMLHttpRequestObject.responseXML;
					
			
					var data_nodes = xmldoc.getElementsByTagName("data"); 
					var n_data = data_nodes.length;
					var combined_data;
					var sdesc, sequip, fulldesc;
					//alert(n_data);
					objTargetList.options.length = 1;
					
					for (i = 0; i < n_data; i++) {
						combined_data = "";
						var node_data_code = data_nodes[i].getElementsByTagName("data_code");
						var node_data_title = data_nodes[i].getElementsByTagName("data_title");
						
						tmp_data_code = unescape(node_data_code[0].firstChild.nodeValue.replace(/\+/g,' '));
						tmp_data_title = unescape(node_data_title[0].firstChild.nodeValue.replace(/\+/g,' '));
						//alert(brcode+brname);
						objTargetList.options.length += 1; 
						objTargetList.options[i+1].value = tmp_data_code;
						objTargetList.options[i+1].text = tmp_data_title;
						
						}
		
            } 
          } 

          XMLHttpRequestObject.send(null); 
        }
      }
*/






//--- URL Encode for Javascript (used mostly in popup windows)
function urlencode(s) {
  s = encodeURIComponent(s);
  return s.replace(/~/g,'%7E').replace(/%20/g,'+');
 }



//--- Populate a Dropdownbox (select element) using AJAX
function getoptions2(frm, targetList, location, params, aCallbackFunction)
      {
		//var objSrcList = eval(frm.name + '.' + srcList);
		var objTargetList = eval('document.' + frm.name + '.' + targetList);
		
		//var frm1 = document.frm;
		//var prog = objSrcList.options[objSrcList.selectedIndex].value;
		//params = "pcode=" + prog;

		var XMLHttpRequestObject = getXMLHttpRequestObject();
	    //var XMLHttpRequestObject = false; 
   		
		/*
		var msxmlhttp = new Array( 
			'Msxml2.XMLHTTP.5.0', 
			'Msxml2.XMLHTTP.4.0', 
			'Msxml2.XMLHTTP.3.0', 
			'Msxml2.XMLHTTP', 
			'Microsoft.XMLHTTP'); 
		
		for (i=0;i<msxmlhttp.length;i++) 
		{ 
			try 
			{ 
				XMLHttpRequestObject = new ActiveXObject(msxmlhttp[i]); 
			} 
			catch (e) 
			{ 
				XMLHttpRequestObject = null; 
			} 
		} 
		if(!XMLHttpRequestObject && typeof XMLHttpRequest!= "undefined") 
		{ 
			XMLHttpRequestObject = new XMLHttpRequest(); 
		} 
		if (!XMLHttpRequestObject) 
		{ 
			XMLHttpRequestObject = false; 
		} 
		*/
		
        if(XMLHttpRequestObject) {
			
		var randomnumber;
		randomnumber = Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11);
		XMLHttpRequestObject.open("GET",location+"?" + params + "&a="+randomnumber, true); 
		
        XMLHttpRequestObject.onreadystatechange = function() 
          { 
		  	
            if (XMLHttpRequestObject.readyState == 4 && 
              XMLHttpRequestObject.status == 200) 
			{ 
			var tmpdiv;
	      
					var xmldoc = XMLHttpRequestObject.responseXML;
					//alert(xmldoc);
			
					var data_nodes = xmldoc.getElementsByTagName("data"); 
					var n_data = data_nodes.length;
					var combined_data;
					var sdesc, sequip, fulldesc;
					//alert(n_data);
					objTargetList.options.length = 1;
					
					for (i = 0; i < n_data; i++) {
						combined_data = "";
						var node_data_code = data_nodes[i].getElementsByTagName("data_code");
						var node_data_title = data_nodes[i].getElementsByTagName("data_title");
						
						tmp_data_code = unescape(node_data_code[0].firstChild.nodeValue.replace(/\+/g,' '));
						tmp_data_title = unescape(node_data_title[0].firstChild.nodeValue.replace(/\+/g,' '));
						//alert(brcode+brname);
						objTargetList.options.length += 1; 
						objTargetList.options[i+1].value = tmp_data_code;
						objTargetList.options[i+1].text = tmp_data_title;
						
						}
					
					if(!(aCallbackFunction === undefined))
					{
						aCallbackFunction();
					}
            } 
          } 

          XMLHttpRequestObject.send(null); 
        }
      }

//--- Get a new XML HTTP Request Object Old
function getXMLHttpRequestObjectOld()
{
	var XMLHttpRequestObject = false; 
	
	var msxmlhttp = new Array( 
		'Msxml2.XMLHTTP.5.0', 
		'Msxml2.XMLHTTP.4.0', 
		'Msxml2.XMLHTTP.3.0', 
		'Msxml2.XMLHTTP', 
		'Microsoft.XMLHTTP'); 
	
	for (i=0;i<msxmlhttp.length;i++) 
	{ 
		try 
		{ 
			XMLHttpRequestObject = new ActiveXObject(msxmlhttp[i]); 
		} 
		catch (e) 
		{ 
			XMLHttpRequestObject = null; 
		} 
	} 
	if(!XMLHttpRequestObject && typeof XMLHttpRequest!= "undefined") 
	{ 
		XMLHttpRequestObject = new XMLHttpRequest(); 
	} 
	if (!XMLHttpRequestObject) 
	{ 
		XMLHttpRequestObject = false; 
	} 
	
	
	
	xmlhttp=null;
	if (window.XMLHttpRequest)
	  {// code for Firefox, Mozilla, IE7, etc.
	  xmlhttp=new XMLHttpRequest();
	  }
	else if (window.ActiveXObject)
	  {// code for IE6, IE5
	  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
	  }
	if (xmlhttp!=null)
	  {
	  xmlhttp.onreadystatechange=state_Change;
	  xmlhttp.open("GET",url,true);
	  xmlhttp.send(null);
	  }
	else
	  {
	  alert("Your browser does not support XMLHTTP.");
	  }

	
	
	
	return(XMLHttpRequestObject);
}

//--- Get a new XML HTTP Request Object
function getXMLHttpRequestObject()
{
	var XMLHttpRequestObject = null;
	if (window.XMLHttpRequest)
	{
		// code for Firefox, Mozilla, IE7, etc.
		XMLHttpRequestObject = new XMLHttpRequest();
	}
	else if (window.ActiveXObject)
	{
		// code for IE6, IE5
		XMLHttpRequestObject = new ActiveXObject("Microsoft.XMLHTTP");
	}
	return(XMLHttpRequestObject);
}

//--- Process a PHP script using AJAX and refresh contents (innerHTML) inside a Div or Label ---
function callAJAX(objTargetDiv, location, params, aCallbackFunction)
{
	//var objSrcList = eval(frm.name + '.' + srcList);
	//var objTargetList = eval(frm.name + '.' + targetList);
	//var objTargetDiv = document.getElementById();
	
	//var frm1 = document.frm;
	//var prog = objSrcList.options[objSrcList.selectedIndex].value;
	//params = "pcode=" + prog;

	var XMLHttpRequestObject = getXMLHttpRequestObject(); 
	
	if(XMLHttpRequestObject != null) 
	{
		var randomnumber;
		randomnumber = Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11);
		
		objTargetDiv.innerHTML = "Loading...";
		
		XMLHttpRequestObject.open("GET",location+"?" + params + "&a="+randomnumber, true); 
		
		XMLHttpRequestObject.onreadystatechange = function() 
		{ 
		
			if (XMLHttpRequestObject.readyState == 4 && 
				XMLHttpRequestObject.status == 200) 
			{ 
				var text_html_output = XMLHttpRequestObject.responseText;
				if(objTargetDiv)
				{
					objTargetDiv.innerHTML = text_html_output;
				}
				if(!(aCallbackFunction === undefined))
				{
					aCallbackFunction(text_html_output);
				}
			} 
		} 

		XMLHttpRequestObject.send(null); 
	}
}

function hideItem(itmId)
{
	var curItm = document.getElementById(itmId);
	if(curItm)
	{
		curItm.style.display='none';
	}
}

/* Show HTML Element */
function showItem(itmId)
{
	var curItm = document.getElementById(itmId);
	if(curItm)
	{
		curItm.style.display='block';
	}
}
//--- Show a Div at a specified position
function showDivAtPos(divID, xPos, yPos)
{
	var objDiv = document.getElementById(divID);
	if(!objDiv)
	{
		return false;
	}
	
	objDiv.style.left = xPos + 'px';
	objDiv.style.top = yPos + 'px';

	objDiv.style.display = "block";
}

//--- Hide a Div
function hideDiv(divID)
{
	var objDiv = document.getElementById(divID);
	if(!objDiv)
	{
		return false;
	}
	
	objDiv.style.display = "none";
}


/*
============================================================
Capturing The Mouse Position in IE4-6 & NS4-6
(C) 2000 www.CodeLifter.com
Free for all users, but leave in this  header

Part One:
Set up a form named "Show" with text fields named "MouseX"
and "MouseY".  Note in the getMouseXY() function how fields
are addressed, thus: document.FormName.FieldName.value

Part Two:
Use JavaScript ver 1.2 so older browsers ignore the script.
The &lt;script must be *after* the &lt;form   since the form
and fields must exist *prior* to being called in the script.
*/


// Detect if the browser is IE or not.
// If it is not IE, we assume that the browser is NS.
var IE = document.all?true:false

// If NS - that is, !IE - then set up for mouse capture
if (!IE) document.captureEvents(Event.MOUSEMOVE)

// Temporary variables to hold mouse x-y pos.s
var mouse_x = 0
var mouse_y = 0

// Main function to retrieve mouse x-y pos.s
function getMouseXY(e) {
    if (IE) { // grab the x-y pos.s if browser is IE
	//srleft = document.body.scrollLeft ? document.body.scrollLeft : document.documentElement.scrollLeft;
	//window.status= mouse_x + "_" + event.clientX + "_" + srleft;
    if(!document.body)
	{
		return ;
	}
	mouse_x = event.clientX + (document.body.scrollLeft ? document.body.scrollLeft : document.documentElement.scrollLeft);
    mouse_y = event.clientY + (document.body.scrollTop ? document.body.scrollTop : document.documentElement.scrollTop);
	
  } else {  // grab the x-y pos.s if browser is NS
    mouse_x = e.pageX
    mouse_y = e.pageY
  }  
  // catch possible negative values in NS4
  if (mouse_x < 0){mouse_x = 0}
  if (mouse_y < 0){mouse_y = 0}  
  // show the position values in the form named Show
  // in the text fields named MouseX and MouseY
  //document.Show.MouseX.value = mouse_x
  //document.Show.MouseY.value = mouse_y
  
  //window.status= mouse_x + "_" + mouse_y;

  return true
}

// Set-up to use getMouseXY function onMouseMove
//document.onmousemove = getMouseXY;
//--- Populate a Dropdownbox (select element) using AJAX
function getoptions3(frm, objTargetList, location, params, aCallbackFunction)
      {
		//var objSrcList = eval(frm.name + '.' + srcList);
		//var objTargetList = eval('document.' + frm.name + '.' + targetList);
			
		//var frm1 = document.frm;
		//var prog = objSrcList.options[objSrcList.selectedIndex].value;
		//params = "pcode=" + prog;

		//alert(location+"?" + params);
		
	    var XMLHttpRequestObject = false; 
   		
		var msxmlhttp = new Array( 
			'Msxml2.XMLHTTP.5.0', 
			'Msxml2.XMLHTTP.4.0', 
			'Msxml2.XMLHTTP.3.0', 
			'Msxml2.XMLHTTP', 
			'Microsoft.XMLHTTP'); 
		
		for (i=0;i<msxmlhttp.length;i++) 
		{ 
			try 
			{ 
				XMLHttpRequestObject = new ActiveXObject(msxmlhttp[i]); 
			} 
			catch (e) 
			{ 
				XMLHttpRequestObject = null; 
			} 
		} 
		if(!XMLHttpRequestObject && typeof XMLHttpRequest!= "undefined") 
		{ 
			XMLHttpRequestObject = new XMLHttpRequest(); 
		} 
		if (!XMLHttpRequestObject) 
		{ 
			XMLHttpRequestObject = false; 
		} 
		
        if(XMLHttpRequestObject) {
			
		var randomnumber;
		randomnumber = Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11) + '' + Math.floor(Math.random()*11);
		XMLHttpRequestObject.open("GET",location+"?" + params + "&a="+randomnumber, true); 
		
        XMLHttpRequestObject.onreadystatechange = function() 
          { 
		  	
            if (XMLHttpRequestObject.readyState == 4 && 
              XMLHttpRequestObject.status == 200) 
			{ 
			var tmpdiv;
	      
					var xmldoc = XMLHttpRequestObject.responseXML;
					
			
					var data_nodes = xmldoc.getElementsByTagName("data"); 
					var n_data = data_nodes.length;
					var combined_data;
					var sdesc, sequip, fulldesc;
					//alert(n_data);
					objTargetList.options.length = 1;
					
					for (i = 0; i < n_data; i++) {
						combined_data = "";
						var node_data_code = data_nodes[i].getElementsByTagName("data_code");
						var node_data_title = data_nodes[i].getElementsByTagName("data_title");
						
						tmp_data_code = unescape(node_data_code[0].firstChild.nodeValue.replace(/\+/g,' '));
						tmp_data_title = unescape(node_data_title[0].firstChild.nodeValue.replace(/\+/g,' '));
						//alert(brcode+brname);
						objTargetList.options.length += 1; 
						objTargetList.options[i+1].value = tmp_data_code;
						objTargetList.options[i+1].text = tmp_data_title;
						
						}
					
					if(!(aCallbackFunction === undefined))
					{
						aCallbackFunction();
					}
            } 
          } 

          XMLHttpRequestObject.send(null); 
        }
      }

