<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
     public $link_id;          // Database Link
     public $module_name;     // Current Module Name
     public $task;               // Type of Task (1: Add, 2: Edit, 0: Nothing....)
     public $cur_page_url;     // Current Page URL
     public $list_page_url;     // List Page URL
     public $sortby;               //--- sortby: 1/2/3/4...    
     public $sortmode;          //--- sortmode: asc/desc
     public $app_config;
     public $time_id;
     public $news_date, $news_title, $news_details;
     public $file_path, $maxfilesize, $upload_file_edit, $mark_new;
     public  $lec_title, $branch, $semester, $upload_file;

     //=== Class Constructor ===/
     function __construct()
     {
          global $config;          // Global Config Settings

          //--- Connect TO Database ---
          $this->link_id = DatabaseManager::iconnectDB();
          if (!$this->link_id) {
               die(DatabaseManager::isqlError());
          }
          $this->app_config = $config;
          //--- Set Current Module Name ---
          $this->module_name = "Mentorship Details";
          $this->cur_page_url = "mentorship_details.php";
          $this->list_page_url = "mentorship_list.php";

          //--- Check User Access permissions
          UserAccess::checkUserLogin("", "admin");

          //--- Get Sort Index and Sort Order
          $this->sortby = CommonFunctions::getSortIndex(1);
          $this->sortmode = CommonFunctions::getSortDirection("asc");

          $this->file_path = "../mentor_files/";

          //--- Get Task Type (Add/Edit) and Current Record Id ---
          $this->getTaskType();
          //echo($this->task); exit;	
          $this->maxfilesize = 4 * 1024 * 1024;

          //--- Execute a Task ---
          switch ($this->task) {
               case 1:
                    //--- Add Record
                    $this->createLectureDetail();
                    break;
               case 2:
                    //--- Edit Record
                    $this->editLectureDetail();
                    break;
               default:
                    //echo("i'm 0"); exit;
                    $this->task = 0;
                    header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
                    die("_");
          }
     }
     //=== Class Destructor ===/
     function __destruct()
     {
          //--- Disconnect from Database ---
          //DatabaseManager::disconnectDB($this->link_id);
     }
     public function getTaskType()
     {
          if (isset($_POST["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
          } else if (isset($_GET["task"])) {
               $this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
          } else {
               $this->task = 0;
          }
          if ($this->task == 0) {
               $this->task = 1;
          }
          return 1;
     }
     //==== Get Current Record Id ====
     public function getRecordId()
     {
          if ($this->task == 1) {
               $this->time_id = 0;
          } else if ($this->task == 2) {
               if (isset($_GET["recid"])) {
                    $this->time_id = CommonFunctions::replaceChars($_GET["recid"], 0);
               } else if (isset($_POST["recid"])) {
                    $this->time_id = CommonFunctions::replaceChars($_POST["recid"], 0);
               } else {
                    $this->time_id = 0;
               }
          }

          return 1;
     }


     //==== Create New Record ====
     public function createLectureDetail()
     {
          //--- Get Record Id ---
          $this->getRecordId();

          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          }
          return 1;
     }
     //==== Edit Existing Record ====
     public function editLectureDetail()
     {
          //--- Get Record Id ---
          $this->getRecordId();

          if ($this->time_id == 0) {
               $_SESSION['app_error'] = "Record not found...";
               header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
               die("_");
          }
          //---- Check if Form is submitted ----
          if (!isset($_POST["postMe"])) {
               //--- Form is not posted
               $this->initFormData();
          }
          return 1;
     }
     //=== Initialize Form Data ====
     public function initFormData()
     {
          if ($this->task == 1) {
               $this->time_id = 0;
               $this->lec_title = "";
               $this->branch = "";
               $this->semester = "";

               $this->news_title_arr = array();
               $this->news_date = date('m-d-Y');
               $this->news_detail_arr = array();
               $this->upload_file_arr = array();
               $this->mark_new = 0;
          } else if ($this->task == 2) {
               //--- Edit Mode. Initialize fields by getting values from Database
               $tmpSql = "SELECT * "
                    . " FROM tbl_mentorship "
                    . " WHERE id = " . $this->time_id;


               $rs = $this->link_id->query($tmpSql);

               if ((!$rs) || (!($rec = $rs->fetch_array()))) {
                    $_SESSION['app_error'] = "Record not found";
                    header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
                    die("_");
               } else {
                    $this->time_id = $rec["time_id"];
                    // $this->lec_title = $rec["title"];
                    $this->branch = $rec["branch"];
                    $this->semester = $rec["semester"];
                    $this->upload_file = $rec["attachmemt"];
                    $this->upload_file_edit = $this->upload_file;
               }
          }
          return 1;
     }
     //=== Read Form Data ====
     public function readFormData()
     {
          return 1;
     }
     //==== Validate Form Data (Returns: 1 / 0) ====
     public function validateFormData()
     {
          $myerr = "";
          if ($myerr != "") {
               $_SESSION['app_error'] = $myerr;
               return 0;
          } else {
               return 1;
          }
     }
     //==== Save Data ====
     public function saveData()
     {
          $myerr = "";
          //--- In case of any error set the Session variable ---
          if ($myerr != "") {
               $_SESSION['app_error'] = $myerr;
               return 0;
          } else {
               //--- If no error and the user is available the page is already redirected to another page ---
               return 1;
          }
     }
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
     <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>

     <link rel="stylesheet" href="css/admin.css" type="text/css" />
     <link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
     <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
     <script src="//code.jquery.com/jquery-1.12.4.js"></script>
     <script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
     <script type="text/javascript" src="js/jquery.form.3.10.js"></script>

     <link href="css/font-awesome.min.css" rel="stylesheet" />
     <link href="css/bootstrap.min.css" rel="stylesheet" />
     <script type="text/javascript" src="js/tcal.js"></script>
     <link href="css/tcal.css" rel="stylesheet" type="text/css" />
     <!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->

     <script language="javascript" type="text/javascript">
          $(document).ready(function() {

               $("#loadingImage").hide();

               <?php if ($objCurPage->task == 1) { ?>
                    // displayGridItem();
               <?php } ?>

               $('#cmdSaveNews').click(function() {
                    $("#loadingImage").hide();
                    $('#frmNewsDetail').submit();
                    $("#loaderImg").html('<img src="images/progress-bar.gif" />');
                    $("#loaderImg").show();
               });

               return false;
          });
     </script>

     <script language="javascript" type="text/javascript">
          function displayGridItem() {
               //add_item 1
               $('.addVidButton').click(function() {

                    if ($('.vidItem').size() >= 2) {
                         $(".addVidButton").hide();
                    }
                    var oval, otxt;
                    $('#vidContainer').append('<div class="vidItem" style=" margin-bottom:10px;">' + $(".vidItemSample").html() + '</div>');

                    //this code is to set focus on first textbox	
                    //========================================================	
                    var new_div = $('#vidContainer .vidItem').last();

                    if (new_div.size() > 0) {
                         new_div.find("input").each(function(n) {
                              if (n == 0) {
                                   $(this).focus();
                              }
                         });

                         new_div.find("input[type='file']").each(function() {
                              $(this).attr('name', 'news_file_' + $('#vidContainer .vidItem').size());
                              $(this).attr('idx', $('#vidContainer .vidItem').size());
                         });

                         new_div.find("input[type='radio']").each(function() {
                              $(this).attr('name', 'mark_new_' + $('#vidContainer .vidItem').size());
                              $(this).attr('idx', $('#vidContainer .vidItem').size());
                         });


                         //this to find all a tag element
                         new_div.find("a").each(function() {
                              $(this).click(function() {
                                   if ($('#vidContainer .vidItem').size() > 1) {
                                        $(".addVidButton").show();
                                        var objVidItm = $(this);
                                        var curIdx = $($(this).parent().parent().parent().find("input[type='file']")[0]).attr('idx');
                                        curIdx = parseInt(curIdx);


                                        var curnewyIdx = $($(this).parent().parent().parent().find("input.mark_newy[type='radio']")[0]).attr('idx');
                                        curnewyIdx = parseInt(curnewyIdx);

                                        $(this).parent().parent().parent().parent().parent().siblings(".vidItem").each(function() {
                                             cfile = $(this).find("input[type='file']")[0];
                                             //console.log(cfile);
                                             anIdx = $(cfile).attr('idx');
                                             //alert(anIdx);
                                             anIdx = parseInt(anIdx);
                                             if (anIdx > curIdx) {
                                                  $(cfile).attr('name', 'news_file_' + (anIdx - 1));
                                                  $(cfile).attr('idx', (anIdx - 1));
                                             }

                                             cnewy = $(this).find("input.mark_newy[type='radio']")[0];
                                             //console.log(cnewy);
                                             newIdxy = $(cnewy).attr('idx');
                                             //alert(newIdxy);
                                             newIdxy = parseInt(newIdxy);
                                             if (newIdxy > curnewyIdx) {
                                                  $(cnewy).attr('name', 'mark_new_' + (newIdxy - 1));
                                                  $(cnewy).attr('idx', (newIdxy - 1));
                                             }

                                             cnewn = $(this).find("input.mark_newn[type='radio']")[0];
                                             //console.log(cnewn);
                                             //cnew_hdn = $(this).find("input[name='mark_new_HX']")[0];
                                             newIdxn = $(cnewn).attr('idx');
                                             newIdxn = parseInt(newIdxn);

                                             //alert(newIdxn);
                                             if (newIdxn > curnewyIdx) {
                                                  $(cnewn).attr('name', 'mark_new_' + (newIdxn - 1));
                                                  $(cnewn).attr('idx', (newIdxn - 1));
                                             }
                                        });

                                        $(objVidItm).parent().parent().parent().parent().parent().remove();

                                   } else {
                                        $(this).parent().parent().find("input:text").each(function() {
                                             $(this).val("");
                                        });
                                   }

                              });
                         });
                    }
                    //call to initialize tcal after clickind add more
                    f_tcalInit();

               });

               $(".vidItem").find("a").each(function() {

                    $(this).click(function() {
                         if ($('#vidContainer .vidItem').size() > 1) {
                              $(this).parent().parent().parent().parent().parent().remove();
                         } else {
                              $(this).parent().parent().find("input:text").each(function() {
                                   $(this).val("");
                              });
                         }
                    });
               });

               <?php
               if ($objCurPage->task == 1) {
                    if (count($objCurPage->upload_file_arr) == 0) { ?>
                         $('.addVidButton').trigger("click"); //this event trigger the click event of addVidButton
                         // for the first time form is loaded
               <?php }
               }
               ?>
          }

          function validateSave() {
               $('#frmNewsDetail').submit();
               $("#loaderImg").html('<img src="images/progress-bar.gif" />');
               $("#loaderImg").show();
          }

          $('#frmNewsDetail').ajaxForm({
               target: '',
               success: function(data) {
                    if (data) {
                         $("#loadingImage").hide();
                         if (data == 1) {
                              $("#loaderImg").empty();
                              $("#loaderImg").hide();
                              location.href = '<?php echo $objCurPage->list_page_url; ?>';
                              return false;
                         } else {
                              $("#loaderImg").empty();
                              $("#loaderImg").hide();
                              $('#showError').html(data);
                              return false;
                         }

                    }
                    return false;
               }
          });
     </script>

</head>

<body>
     <div id="header">
          <div class="container">
               <?php include_once("header.php") ?>
          </div>
     </div>
     <section class="container" style="margin-top:18px; min-height:520px;">
          <div id="row">
               <aside class="col-sm-3 sidebar sidebar-right">
                    <!--left_menu start here-->
                    <?php include_once("lftmenu.php"); ?>
               </aside>
               <!--left_menu ends here-->
               <div class="col-md-9 maincontent" style="border:1px solid #ccc;">
                    <!--<p>Date: <input type="text" id="datepicker"></p>-->
                    <div id="show_error"><?php CommonFunctions::displayErrMsg(); ?></div>
                    <div id="showError" style="text-align:center; color:#F00;"></div>
                    <form name="frmNewsDetail" id="frmNewsDetail" method="post" action="adminajax/ajax_mentorship_details.php" enctype="multipart/form-data">
                         <table width="98%" cellpadding="4" cellspacing="0" border="0" class="dialogBaseShadow">
                              <tr>
                                   <td>
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" class="dialogBase">
                                             <tr>
                                                  <td class="dialogHeader">
                                                       <h1><?php if ($objCurPage->task == 1) {
                                                                 echo ("Add ");
                                                            } else {
                                                                 echo ("Edit ");
                                                            } ?> Mentorship</h1>
                                                  </td>
                                             </tr>
                                             <tr>
                                                  <td>
                                                       <table width="100%" cellpadding="5" cellspacing="3" border="0" class="formTextWithBorder">
                                                            <tr>
                                                                 <td align="center" id="ref_link" colspan="2">
                                                                      <div class="tableRow">
                                                                           <div class="imgLoader" id="loaderImg" style="display:none; margin:0 auto"></div>
                                                                      </div>
                                                                 </td>
                                                            </tr>

                                                            <tr>
                                                                 <td colspan="2">&nbsp;</td>
                                                            </tr>
                                                            <tr>
                                                                 <td align="left" valign="middle" colspan="2">

                                                                      <?php if ($objCurPage->task == 1) { ?>

                                                                           <!-- <div id="vidContainer"> -->
                                                                           <!-- <div>
                                                                                     <div id="qvar_addmore" align="center"><a title="Add" class="addVidButton" style="cursor:pointer;float:right;"><img height="16" align="absmiddle" width="16" src="images/b_add.png" border="0" /> Add more</a>&nbsp;&nbsp;</div>

                                                                                </div> -->

                                                                           <!-- <div class="vidItemSample" style="display:none; margin-bottom:10px;"> -->
                                                                           <table width="100%" border="0" cellpadding="0" cellspacing="1">

                                                                                <tr valign="top">
                                                                                     <td width="26%" height="15">Branch</td>
                                                                                     <td width="50%">
                                                                                          <select class="form-control" name="branch">
                                                                                               <option value="">-</option>
                                                                                               <option value="Mechanical">Mechanical Engineering</option>
                                                                                               <option value="Electrical">Electrical Engineering</option>
                                                                                               <option value="Chemical">Chemical Engineering</option>
                                                                                               <option value="Electronics">Electronics and Telecommunication Engineering</option>
                                                                                               <option value="Civil">Civil Engineering</option>
                                                                                               <option value="Ceramic">Ceramic Engineering</option>
                                                                                               <option value="Civil">Civil Engineering</option>
                                                                                               <option value="Metallurgy">Metallurgy Engineering</option>
                                                                                               <option value="1st">1st Year (Math &amp; Science)</option>
                                                                                          </select>
                                                                                     </td>
                                                                                </tr>
                                                                                


                                                                                <tr valign="top">
                                                                                     <td width="22%" align="left">Upload Document<br />
                                                                                          <font size="1" color="#666666"> (File Types .pdf Max Size: 4MB)</font>
                                                                                     </td>
                                                                                     <td width="50%" align="left">
                                                                                          <input type="file" name="news_file_0" value="" />
                                                                                          <input type="hidden" name="news_file_HX" value="1" />
                                                                                     </td>
                                                                                </tr>



                                                                                <tr valign="top">
                                                                                     <td width="12%" align="right">&nbsp;</td>
                                                                                     <td width="12%" align="right">
                                                                                          <!-- <a title="Remove" class="delVidButton"><img height="16" align="absmiddle" width="16" src="images/b_drop.png" border="0" /></a> -->
                                                                                     </td>
                                                                                </tr>

                                                                           </table>
                                                                           <!-- </div> -->


                                                                           <!-- </div> -->

                                                                      <?php
                                                                      } else { ?>
                                                                           <div id="edit_news_div">

                                                                                <table width="100%" border="0" cellpadding="0" cellspacing="1">

                                                                                     <tr valign="top">
                                                                                          <td width="26%" height="15">Branch</td>
                                                                                          <td width="50%">
                                                                                               <select class="form-control" name="branch">
                                                                                                    <option value="">-</option>
                                                                                                    <option value="Mechanical" <?php if ($objCurPage->branch == "Mechanical") {
                                                                                                                                       echo "selected";
                                                                                                                                  } ?>>Mechanical Engineering</option>
                                                                                                    <option value="Electrical" <?php if ($objCurPage->branch == "Electrical") {
                                                                                                                                       echo "selected";
                                                                                                                                  } ?>>Electrical Engineering</option>
                                                                                                    <option value="Chemical" <?php if ($objCurPage->branch == "Chemical") {
                                                                                                                                  echo "selected";
                                                                                                                             } ?>>Chemical Engineering</option>
                                                                                                    <option value="Electronics" <?php if ($objCurPage->branch == "Electronics") {
                                                                                                                                       echo "selected";
                                                                                                                                  } ?>>Electronics and Telecommunication Engineering</option>
                                                                                                    <option value="Civil" <?php if ($objCurPage->branch == "Civil") {
                                                                                                                                  echo "selected";
                                                                                                                             } ?>>Civil Engineering</option>
                                                                                                    <option value="Ceramic" <?php if ($objCurPage->branch == "Ceramic") {
                                                                                                                                  echo "selected";
                                                                                                                             } ?>>Ceramic Engineering</option>
                                                                                                    <option value="Civil" <?php if ($objCurPage->branch == "Civil") {
                                                                                                                                  echo "selected";
                                                                                                                             } ?>>Civil Engineering</option>
                                                                                                    <option value="Metallurgy" <?php if ($objCurPage->branch == "Metallurgy") {
                                                                                                                                       echo "selected";
                                                                                                                                  } ?>>Metallurgy Engineering</option>
                                                                                                    <option value="1st" <?php if ($objCurPage->branch == "1st") {
                                                                                                                             echo "selected";
                                                                                                                        } ?>>1st Year (Math &amp; Science)</option>
                                                                                               </select>
                                                                                          </td>
                                                                                     </tr>

                                                                                     


                                                                                     <tr valign="top">
                                                                                          <td width="22%" align="left">Upload Document<br />
                                                                                               <font size="1" color="#666666"> (File Types .pdf,.jpg,.jpeg,.doc,.docx,.xsl,.xslx Max Size: 4MB)</font>
                                                                                          </td>
                                                                                          <td width="50%" align="left">
                                                                                               <input type="file" name="news_file" value="" />
                                                                                               <input type="hidden" name="news_file_edit" id="news_file_edit" value="<?php echo $objCurPage->upload_file_edit; ?>" />&nbsp;&nbsp;
                                                                                               <?php if ($objCurPage->upload_file_edit != "") { ?>
                                                                                                    <i>
                                                                                                         <font color="#003399"><small>(Last file: <b><a target="_blank" href="<?php echo $objCurPage->file_path . $objCurPage->upload_file_edit;  ?>" class="generalink">View</a></b>)</small></font>
                                                                                                    </i>
                                                                                               <?php } ?>
                                                                                          </td>
                                                                                     </tr>



                                                                                </table>

                                                                           </div>

                                                                      <?php }
                                                                      ?>
                                                                 </td>
                                                            </tr>

                                                            <tr>
                                                                 <td align="left" valign="middle" colspan="2">&nbsp;</td>
                                                            </tr>
                                                            <tr>
                                                                 <td align="left" valign="middle" colspan="2">&nbsp;</td>
                                                            </tr>
                                                            <tr id="rowExit">
                                                                 <td align="right" valign="middle" colspan="2">
                                                                      <div class="imgLoader" id="loaderImg" style="display:none;"></div>

                                                                      <input type="hidden" name="recid" id="recid" value="<?php echo $objCurPage->time_id; ?>" />
                                                                      <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />
                                                                      <input type="hidden" name="postMe" value="Y" />
                                                                      <input type="button" name="cmdSaveNews" id="cmdSaveNews" value="<?php if ($objCurPage->task == 1) {
                                                                                                                                            echo ("Save");
                                                                                                                                       } else {
                                                                                                                                            echo ("Update");
                                                                                                                                       } ?>" class="btn btn-default" />
                                                                      <!--<input type="button" name="cmdSaveNews" id="cmdSaveNews" class="btn btn-default" value="Save" />-->
                                                                      <input type="button" name="cmdExitNews" id="cmdExitNews" class="btn btn-default" onClick="window.location.href='mentorship_list.php'" value="Exit" />
                                                                 </td>
                                                            </tr>
                                                       </table>
                                                  </td>
                                             </tr>
                                        </table>
                                   </td>
                              </tr>
                         </table>
                    </form>

               </div>
          </div>
     </section>
     <?php include_once('footer.php'); ?>
</body>

</html>