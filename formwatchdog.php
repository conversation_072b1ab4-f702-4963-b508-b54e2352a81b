<?php
// === Telegram Bot Config (shared across all servers) ===
$secrets = include(__DIR__ . '/../private/secrets.env.php');
$telegramBotToken = $secrets['bot_token'];
$telegramChatId = $secrets['chat_id2'];

// ====== CONFIG ======
$hashFile = __DIR__ . '/../private/.form_hash.json';
$logFile  = __DIR__ . '/../private/form_log.txt';
$directoryToScan = __DIR__;
$siteName = 'UGIE'; // 🔁 << UNIQUE NAME for this site

$allowedExtensions = ['php', 'html', 'htm'];
$suspiciousPatterns = [
     '/<input[^>]+type=["\']?file["\']?/i',
     '/action=["\']?(http:|\/\/)/i',
     '/<input[^>]+name=["\']?(cmd|exec|shell)["\']?/i',
];

// ====== LOAD OLD HASHES ======
$oldHashes = file_exists($hashFile) ? json_decode(file_get_contents($hashFile), true) : [];
$currentHashes = [];

$alerts = [];

$rii = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directoryToScan));
foreach ($rii as $file) {
     if ($file->isDir()) continue;

     $ext = pathinfo($file->getFilename(), PATHINFO_EXTENSION);
     if (!in_array(strtolower($ext), $allowedExtensions)) continue;

     $content = file_get_contents($file->getPathname());

     if (!preg_match_all('/<form[\s\S]*?<\/form>/i', $content, $matches)) continue;

     foreach ($matches[0] as $formHtml) {
          $hash = hash('sha256', $formHtml);
          $fileKey = $file->getRealPath() . '::' . $hash;
          $currentHashes[$fileKey] = $hash;

          // Check for changes
          if (!isset($oldHashes[$fileKey])) {
               $alertMsg = "🚨 *{$siteName} Form change detected in:\n" .
                    $file->getRealPath();

               foreach ($suspiciousPatterns as $pattern) {
                    if (preg_match($pattern, $formHtml)) {
                         $alertMsg .= "\n⚠️ Suspicious content matched: `$pattern`";
                    }
               }

               $alerts[] = $alertMsg;
          }
     }
}

// ====== SEND TELEGRAM ALERTS ======
if (!empty($alerts)) {
     foreach ($alerts as $alert) {
          sendTelegramMessage($alert);
     }
} else {
     #sendTelegramMessage("✅ *{$siteName}*: Form Watchdog: All clear. No new or modified forms detected.");
}

// ====== SAVE NEW HASHES ======
file_put_contents($hashFile, json_encode($currentHashes, JSON_PRETTY_PRINT));

// ====== LOG FORM SUBMISSIONS IF RUN AS ENDPOINT ======
// Update the form submission logging section
$sensitiveFields = array(
     'password',
     'password2',
     'passwd',
     'pass',
     'pwd',
     'creditcard',
     'credit_card',
     'cc_number',
     'pan',
     'securitycode',
     'cvv',
     'ssn',
     'sin',
     'username',
     'uname',
     'user',
     'email',
     'login'
);

if (!empty($_SERVER) && isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
     $safePost = array();
     foreach ($_POST as $key => $value) {
          // Check if field is sensitive
          $isSensitive = false;
          foreach ($sensitiveFields as $pattern) {
               if (stripos($key, $pattern) !== false) {
                    $isSensitive = true;
                    break;
               }
          }

          // Store value or [REDACTED] if sensitive
          $safePost[$key] = $isSensitive ? '[REDACTED]' : $value;
     }
     $logData = array(
          'timestamp' => date('Y-m-d H:i:s'),
          'ip'        => $_SERVER['REMOTE_ADDR'],
          'referer'   => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'N/A',
          'agent'     => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'N/A',
          'url'       => $_SERVER['REQUEST_URI'],
          'fields'    => $safePost
     );

     // Use error handling for file operations
     try {
          if (!file_put_contents($logFile, json_encode($logData) . PHP_EOL, FILE_APPEND)) {
               error_log("Failed to write to log file: $logFile");
          } else {
               sendTelegramMessage("✅ *{$siteName}*: Form Watchdog: Form submission logged");
          }
     } catch (Exception $e) {
          error_log("Error writing to log file: " . $e->getMessage());
     }
}


// ====== TELEGRAM FUNCTION ======
// Update Telegram function with error handling
// function sendTelegramMessage($message) {
//     global $telegramBotToken, $telegramChatId;

//     if (empty($telegramBotToken) || empty($telegramChatId)) {
//         error_log('Telegram configuration missing');
//         return false;
//     }

//     $url = "https://api.telegram.org/bot" . $telegramBotToken . "/sendMessage";
//     $data = array(
//         'chat_id' => $telegramChatId,
//         'text'    => $message,
//         'parse_mode' => 'Markdown'
//     );

//     // Use cURL instead of file_get_contents
//     try {
//         $ch = curl_init();
//         if ($ch === false) {
//             throw new Exception('Failed to initialize cURL');
//         }

//         curl_setopt_array($ch, array(
//             CURLOPT_URL => $url,
//             CURLOPT_RETURNTRANSFER => true,
//             CURLOPT_POST => true,
//             CURLOPT_POSTFIELDS => http_build_query($data),
//             CURLOPT_SSL_VERIFYPEER => true,
//             CURLOPT_HTTPHEADER => array(
//                 'Content-Type: application/x-www-form-urlencoded'
//             )
//         ));

//         $result = curl_exec($ch);
        
//         if ($result === false) {
//             throw new Exception(curl_error($ch));
//         }

//         $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
//         if ($httpCode !== 200) {
//             throw new Exception("HTTP Code: $httpCode, Response: $result");
//         }

//         curl_close($ch);
//         return true;

//     } catch (Exception $e) {
//         error_log("Telegram error: " . $e->getMessage());
//         if (isset($ch) && is_resource($ch)) {
//             curl_close($ch);
//         }
//         return false;
//     }
// }
function sendTelegramMessage($message) {
    global $telegramBotToken, $telegramChatId;

    if (empty($telegramBotToken) || empty($telegramChatId) || !is_array($telegramChatId)) {
        error_log('Telegram configuration missing or invalid chat ID list');
        return false;
    }

    foreach ($telegramChatId as $chatId) {
        $url = "https://api.telegram.org/bot{$telegramBotToken}/sendMessage";
        $data = array(
            'chat_id' => $chatId,
            'text'    => $message,
            // 'parse_mode' => 'Markdown', // Uncomment if you're using Markdown-safe content
        );

        try {
            $ch = curl_init();
            if ($ch === false) {
                throw new Exception('Failed to initialize cURL');
            }

            curl_setopt_array($ch, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => http_build_query($data),
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/x-www-form-urlencoded'
                )
            ));

            $result = curl_exec($ch);

            if ($result === false) {
                throw new Exception("cURL error: " . curl_error($ch));
            }

            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if ($httpCode !== 200) {
                throw new Exception("HTTP Code: $httpCode, Response: $result");
            }

            curl_close($ch);

        } catch (Exception $e) {
            error_log("Telegram send error (chat_id $chatId): " . $e->getMessage());
            if (isset($ch) && is_resource($ch)) {
                curl_close($ch);
            }
            // Continue to next chat ID even if this one fails
        }
    }

    return true;
}
