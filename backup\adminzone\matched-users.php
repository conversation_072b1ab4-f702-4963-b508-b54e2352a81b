<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $app_config;
	public $user_id;
	public $user_name, $user_age, $user_email, $user_gen, $user_pwd, $user_con, $user_add,$hobby, $user_cpwd, 
	$res, $hoblist, $hob_list_user, $cnt_hobbies;
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		//--- Set Current Module Name ---
		$this->module_name = "Matched User";
		$this->cur_page_url = "matched-users.php";
		//$this->list_page_url = "announcement_list.php";
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
		//--- Get Sort Index and Sort Order
						
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
			//echo($this->task); exit;	
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createDetail();
				break;
			case 2:
				//--- Edit Record
				$this->editDetail();
				break;
			default:
				//echo("i'm 0"); exit;
				$this->task = 0;
				//header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				die("_");
		}
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->user_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->user_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->user_id = CommonFunctions::replaceChars($_POST["recid"], 0);			
			}
			else
			{
				$this->user_id = 0;
			}
		}
		
		return 1;
	}
	
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 2;
		}
		
		return 1;
	}

	//==== Create New Record ====
	public function createDetail()
	{
		//--- Get Record Id ---
		//$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}		
		return 1;
	}
	//==== Edit Existing Record ====
	public function editDetail()
	{
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		return 1;
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			/*$this->user_id = "";	
			$this->announcement_headline = "";	
			$this->announcement_details = "";	
			$this->announcement_date = date('m-d-Y');	*/		
			
		}
		else if($this->task == 2) 
		{
			$this->hoblist=array();
			if($_SESSION["currentLogonUser"]->cur_user_login_name != "")
			{
				//--- Edit Mode. Initialize fields by getting values from Database
				$tmpSql = "SELECT user_id, user_name, user_age, user_email, user_gender, user_password," ."user_contact_no, user_address from admin_details WHERE user_email = '" . $_SESSION["currentLogonUser"]->cur_user_login_name  . "'";
				
				//echo $tmpSql;					
				$rs = $this->link_id->query($tmpSql);
							
				if( (!$rs) || (!($rec = $rs->fetch_array())) )
				{
					$_SESSION['app_error'] = "Record not found...";
					
					die("_");
				}
				else
				{
					$this->user_id = $rec["user_id"];
					//echo $this->user_id;					
					if($this->user_id !="")
					{
						$tmpsql_new="SELECT a2.user_id, a2.user_name as user_name, a2.user_age, a2.user_email, a2.user_gender,"
				." a2.user_contact_no, a2.user_address, b2.hob_id , group_concat(b2.hob_list order by "
				." b2.hob_list) as hobbies, count(b2.hob_list) as cnt_hobbies FROM (admin_details "
				." a1 INNER JOIN tbl_hobbies b1 ON b1.user_id = a1.user_id) INNER JOIN (admin_details "
				." a2 INNER JOIN tbl_hobbies b2 ON b2.user_id = a2.user_id) ON a2.user_id <> a1.user_id "
				." AND b2.hob_list = b1.hob_list WHERE a1.user_id = ".$this->user_id." GROUP BY "
				." a2.user_id order by cnt_hobbies desc, a2.user_name LIMIT 10;";
						//echo $tmpsql_new;  die("---");
						$rs1 = $this->link_id->query($tmpsql_new);
						if( (!$rs1) || (!($rec1 = $rs1->fetch_array())) )
						{}
						else
						{
							$i=0;
							do{
								$this->hob_list_user[$i] = $rec1["user_name"];
								$this->cnt_hobbies [$i] =$rec1["hobbies"];
								$i++;
								}while($rec1 = $rs1->fetch_array());								
						}
						//print_r ($this->hoblist); die("---");
					}
				}
		    }
		}
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()  
	{	
		return 1;
	}
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";										
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";					
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Manage Account</title>
<script type="text/javascript" src="js/jquery-1.10.1.min.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/admin.css" rel="stylesheet" />
<script language="javascript" type="text/javascript">
$(document).ready(function(){
	$("#loadingImage").hide();	
	$('#cmdSave').click(function()
	{	
	    $("#loadingImage").hide();	
		validateSave();
	});
});
function validateSave()
{
	$("#loaderImg").html('<img src="images/bar-circle.gif" />');
	$("#loaderImg").show();
	$('#frmEditAccount').submit();
}
$('#frmEditAccount').ajaxForm({	
	target: '',
	success: function(data){
		$("#loadingImage").hide();
		if(data == 1)
		{	alert("Update Successfully");
			window.location="home.php";
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			return false;					
		}
		else
		{
			$("#loaderImg").empty();
			$("#loaderImg").hide();	
			$('#showError').html(data);
			return false;
		}
	}			
});
</script>
</head>
<body>
<div id="header" style="width:auto; height:117px; ">
  <?php include_once("header.php") ?>
</div>
</div>
</div>
<div id="mid_container">
  <div id="left_menu" style="width:23%; height:auto; float:left;"> <!--left_menu start here-->
    <?php include_once("lftmenu.php"); ?>
  </div>
  <!--left_menu ends here-->
  
  <div id="right_menu" >
    <form method="post" name="frmEditAccount" id="frmEditAccount" action="adminajax/add_registration.php">
      <table width="100%" height="100%" cellpadding="0" cellspacing="0">
        <tr>
          <td valign="top"><table width="100%" cellspacing="0" cellpadding="4">
              <?php if(!empty($_SESSION["app_message"]) || !empty($_SESSION["app_error"])){ ?>
              <tr>
                <td height="30" align="center"><?php CommonFunctions::displayErrMsg(); ?></td>
              </tr>
              <?php } ?>
              <tr>
                <td><div id="showError" class="showError"></div></td>
              </tr>
              <tr>
                <td align="left" valign="top"><table width="100%" cellspacing="0" class="dialogBaseShadow">
                    <tr>
                      <td><table width="100%" cellspacing="0" class="dialogBase">
                          <tr>
                            <td class="dialogHeader" colspan="2" style="text-align:center;"><h2>Matched User</h2></td>
                          </tr>
                          <tr>
                            <th style="border-bottom:1px solid #ccc"><h3>User Name</h3></th>
                            <th style="border-bottom:1px solid #ccc"><h3>Hobbies</h3></th>
                          </tr>
                          <?php	
						  $pieces=NULL;							  
								  	for($i=0; $i<count($objCurPage->hob_list_user); $i++){
										$pieces=explode(",", $objCurPage->cnt_hobbies[$i]);
											/*foreach($pieces as $key => $value) {
											   echo "<li>$key: $value</li>";*/
									?>
                          <tr > <?php echo '<td style="padding:1%;  border-bottom:1px solid #ccc">'.$objCurPage->hob_list_user[$i] . "</td>  <td style='padding:1%;  border-bottom:1px solid #ccc;  border-left:1px solid #ccc'><span>".$objCurPage->cnt_hobbies[$i]."</span></td>"; ?>
                            <?php } //}echo  $value;
							?>
                          </tr>
                          <tr>
                            <td height="32" align="right">&nbsp;</td>
                          </tr>
                        </table></td>
                    </tr>
                  </table></td>
              </tr>
            </table></td>
        </tr>
      </table>
    </form>
  </div>
</div>
<?php include_once('footer.php');?>
</body>
</html>