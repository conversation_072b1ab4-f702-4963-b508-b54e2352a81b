<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $reccnt;			//--- Number of Records to be displayed per page
	
	public $app_config;		// Application Configuration Settings
	
	public $id_list,$photo_id;		//--- Selected Id List
	public $cat_id; 
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
			
		//--- Set Current Module Name ---
		$this->module_name = "Gallery Category List";
		$this->cur_page_url = "managephotocategory_list.php";
		$this->list_page_url = "managephotocategory_list.php";

		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");		
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(4);
		$this->sortmode = CommonFunctions::getSortDirection("asc");

		//--- Get Record Position (crec) passed to this page ----
		$this->reccnt = 20;
		$this->getRecordPostion();

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();

		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 3:
				//--- Delete Record
				$this->task = 0;
				$this->deleteRecords();
				break;
				
			default:
				$this->task = 0;
		}
		
		if(isset($_POST["cmdDelete"]))
		{
			$this->deleteAllGalleryCategory();
		}
		
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	
	//==== Get Record Position (crec) passed to this page ====
	public function getRecordPostion()
	{
		if(isset($_POST["crec"]))
		{
			$this->crec = intval(CommonFunctions::replaceChars($_POST["crec"], 0));
		}
		else if(isset($_GET["crec"]))
		{
			$this->crec = intval(CommonFunctions::replaceChars($_GET["crec"], 0));
		}
		else
		{
			$this->crec=0;
		}
			
		if(($this->crec % $this->reccnt) != 0)
		{
			$this->crec = $this->crec - ($this->crec % $this->reccnt);
		}

		if($this->crec < 0)
		{
			$this->crec=0;
		}
		
		return 1;
	}
	

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		
		return 1;
	}

	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if(isset($_GET["recid"]))
		{
			$this->cat_id = intval(CommonFunctions::replaceChars($_GET["recid"], 0));
		}
		else if(isset($_POST["recid"]))
		{
			$this->cat_id = intval(CommonFunctions::replaceChars($_POST["recid"], 0));
		}
		else
		{
			$this->cat_id = 0;
		}

		return 1;
	}


	public function deleteAllGalleryCategory()
	{
		
		if(isset($_POST["cmdDelete"]))
		{			
			$recid="";
			
			$arr_nums = array();

			if(isset($_POST["cat_ids"]))
			{
				$arr_nums=$_POST["cat_ids"];
				$i=0;
				
				$recid = implode(",",$arr_nums);
								
				if($recid !="")
				{
																				
					$tmpSql_img = "SELECT * FROM tbl_photo_gallery WHERE cat_id IN (" . $recid . ")";
					
					$rs_img = $this->link_id->query($tmpSql_img);
					
					if( (!$rs_img) || (!($rec_img = $rs_img->fetch_array())) )																
					{}
					else
					{
						do
						{ 
							if ($rec_img["upload_image"] !="")
							{		
								if(file_exists("../gallery_images/".$rec_img["upload_image"]))
								{
									@unlink("../gallery_images/".$rec_img["upload_image"]);
									@unlink("../gallery_images/"."thumb_". $rec_img["upload_image"]);
									@unlink("../gallery_images/"."medium_". $rec_img["upload_image"]);
								}	
							}
						}while($rec_img = $rs_img->fetch_array());
					}
					
					$tmpSql_catimg = "SELECT * FROM tbl_photo_category WHERE cat_id IN (" . $recid . ")";
					$rs_catimg = $this->link_id->query($tmpSql_catimg);
					if( (!$rs_catimg) || (!($rec_catimg = $rs_catimg->fetch_array())) )																
					{}
					else
					{
						do
						{ 
							if ($rec_catimg["cat_photo"] !="")
							{		
								if(file_exists("../gallery_cat_images/".$rec_catimg["cat_photo"]))
								{
									@unlink("../gallery_cat_images/".$rec_catimg["cat_photo"]);
									@unlink("../gallery_cat_images/"."thumb_". $rec_catimg["cat_photo"]);
									@unlink("../gallery_cat_images/"."medium_". $rec_catimg["cat_photo"]);
								}	
							}
						}while($rec_catimg = $rs_catimg->fetch_array());
					}	
			
					//--- Begin Transaction
					$this->link_id->autocommit(FALSE);
					
					$tmpSql_img = "DELETE FROM tbl_photo_gallery WHERE cat_id IN (" . $recid .")" ;	
					$rs_img = $this->link_id->query($tmpSql_img);
													
					$tmpSql3 = "DELETE FROM tbl_photo_category WHERE cat_id IN(" . $recid.")";
					$rs3 = $this->link_id->query($tmpSql3);
									
					//--- Commit Transaction
					$this->link_id->commit();
										
					$myerr="Record deleted successfully";
					$_SESSION["app_message"] = $myerr;					
					header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
					die("_");
				}
			}
			else
			{
				$_SESSION["app_error"] = "No record selected";
			}
		}
	}


	//=== Initialize Form Data ====
	public function initFormData()
	{
		//--- do nothing
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{
		
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	

//==== Delete Selected Records ====

	public function deleteRecords()
	{	
	
	  
		
		$this->getRecordId();

		if ($this->cat_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{	
		   
			$this->photo_id = DatabaseManager::igetDataFromTable($this->link_id, "tbl_photo_gallery", "image_id", 0,"cat_id = ".$this->cat_id);
			//echo $this->photo_id;die("dddd");
			if($this->photo_id != 0)
			{					
				$tmpSql_img = "SELECT * FROM tbl_photo_gallery WHERE cat_id = '" . $this->cat_id  . "'";	
				$rs_img = $this->link_id->query($tmpSql_img);
																				
				if( (!$rs_img) || (!($rec_img = $rs_img->fetch_array())) )
				{}
				else
				{
					do
					{ 
						if ($rec_img["upload_image"] !="")
						{		
							if(file_exists("../gallery_images/".$rec_img["upload_image"]))
							{
								unlink("../gallery_images/".$rec_img["upload_image"]);
								unlink("../gallery_images/"."thumb_". $rec_img["upload_image"]);
								unlink("../gallery_images/"."medium_". $rec_img["upload_image"]);
							}	
						}
					}while($rec_img = $rs_img->fetch_array());
				}
				
			 }
			 
			 //--- Delete records
			
			//--- Begin Transaction
			$this->link_id->autocommit(FALSE); 
			
			$tmpSql_img = "DELETE FROM tbl_gallery WHERE cat_id =" . $this->cat_id ;	
			$rs_img = $this->link_id->query($tmpSql_img);	
			
			
			
			$upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "tbl_photo_category", "cat_photo", "","cat_id=".$this->cat_id);
			if($upload_old_file != "")
			{
				if(file_exists("../gallery_cat_images/".$upload_old_file))
				{
					unlink("../gallery_cat_images/".$upload_old_file);
					unlink("../gallery_cat_images/"."thumb_". $upload_old_file);
					unlink("../gallery_cat_images/"."medium_". $upload_old_file);

				}
				/*CommonFunctions::removeFiles($this->file_path, $upload_old_file);	
				CommonFunctions::removeFiles($this->file_path, "medium_".$upload_old_file);			
				CommonFunctions::removeFiles($this->file_path, "thumb_".$upload_old_file);*/
			}
			
				
			
			$tmpSql3 =  "DELETE FROM tbl_photo_category WHERE cat_id =" . $this->cat_id;
			$rs3 = $this->link_id->query($tmpSql3);
			
			if($this->link_id->affected_rows == -1)
			{
				$this->link_id->rollback();
				$myerr = "Record could not be deleted";
			}	
			else
			{		
				//--- Commit Transaction
				$this->link_id->commit();
				$_SESSION["app_message"] = "Record successfully deleted.";
				header("Location: " . $this->cur_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}	
									
		 if($myerr != "")
			{
				$_SESSION['app_error'] = $myerr;
				return 0;
			}
			else
			{
				//--- If no error and the user is available the page is already redirected to another page ---
				return 1;
			}
		}
		
	}
		
}

$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script type="text/javascript" src="js/jquery.colorbox.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script language="javascript" type="text/javascript">
$(document).ready(function() {
	//--- Lightbox
	$(".popThumb").colorbox({rel:''});

});
</script>
<script language="JavaScript">
function moveRec(frm, crecVal)
	{
	frm.crec.value=crecVal;
	}
	
function sortPage(sortidx)
	{
	var frm, sortmode;
	frm = eval("document.frmList");
	
	if (frm.sortby.value == sortidx && frm.sortmode.value == "asc")
		{
		sortmode = "desc";
		}
	else
		{
		sortmode = "asc";
		}

	frm.action = "<?= $objCurPage->cur_page_url ?>" + "?sortby=" + sortidx + "&sortmode=" + sortmode;
	frm.submit();
	} 
	
  function viewGallery_img(cat_id)
   {   
  		if(cat_id > 0)
		{	
			$.get('adminajax/ajax_category_image_view.php?task=1&recid='+cat_id,  function(data) {
				if(data)
				{	
					$.colorbox({html:data});											
				}
				else
				{				
					return false;
				}							  
											  
			});	
		}
   }
	
</script>
</head>

<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:520px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-8 maincontent">
      <form name="frmList" method="post" action="<?php echo $_SERVER['PHP_SELF'];?>">
        <table width="100%" cellspacing="0" cellpadding="4">
          <tr>
            <td height="30" align="center" class="errMsgAdmin"><?php CommonFunctions::displayErrMsg(); ?></td>
          </tr>
          <tr>
            <td align="center" valign="top"><table width="670" cellpadding="0" cellspacing="0" class="dialogBaseShadow">
                <tr>
                  <td><table width="100%" cellpadding="2" cellspacing="0" class="dialogBase">
                      <tr>
                        <td class="dialogHeader"><strong>Photo Category Lists</strong></td>
                      </tr>
                      <tr>
                        <td align="right" valign="top" ><?php	
                                    
                                            $tmpSql = "SELECT count(*) as totrec "
                                            . " FROM tbl_photo_category a  "
                                            . " WHERE 1 ";
                                    
                                    $rs = $objCurPage->link_id->query($tmpSql);
                                                        
                                    if( (!$rs) || (!($rec = $rs->fetch_array())) )
                                        {
                                        
                                        $totrec=0;
                                        }
                                    else
                                        {
                                        if(empty($rec["totrec"]))
                                            {$totrec=0;}
                                        else
                                            {$totrec=$rec["totrec"];}
                                        }
                        
                                    if( $objCurPage->crec >=$totrec)
                                        { 
                                        if($totrec == 0)
                                            {$objCurPage->crec = 0;}
                                        else if($totrec % $objCurPage->reccnt == 0)
                                            {$objCurPage->crec = $totrec - $objCurPage->reccnt;}
                                        else
                                            {$objCurPage->crec = $totrec - ($totrec % $objCurPage->reccnt);}
                                        }
                                    if(($totrec- $objCurPage->crec )>$objCurPage->reccnt)
                                        {$pgrec=$objCurPage->reccnt;}
                                    else
                                        {$pgrec=$totrec- $objCurPage->crec ;}
                                                        
										 ?>
                          <?php if( $objCurPage->crec  >= $objCurPage->reccnt) {?>
                          <input name="imgleft2" type="image" id="imgleft22" src="../images/butt_left2.gif" alt="Show First" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo 0; ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self';" />
                          <input name="imgleft" type="image" id="imgleft" src="../images/butt_left.gif" alt="Show Previous" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo  $objCurPage->crec  - $objCurPage->reccnt; ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self';" />
                          <?php } else { ?>
                          <img src="images/butt_left2_dis.gif" width="16" height="16" /> <img src="images/butt_left_dis.gif" width="16" height="16" />
                          <?php } ?>
                          &nbsp; <b>
                          <?php if($totrec > 0)
													{
													if( ( $objCurPage->crec +1) != ( $objCurPage->crec +$pgrec) )
														{echo("<font color='#0033FF'>" . ( $objCurPage->crec +1) . "</font>-<font color='#0033FF'>" . ( $objCurPage->crec +$pgrec) . "</font> of <font color='#CC0000'>" . $totrec) . "</font>";}
													else
														{echo("<font color='#0033FF'>" . ( $objCurPage->crec +1) . "</font> of <font color='#CC0000'>" . $totrec) . "</font>";}
													}
												else
													{echo("<font color='#990000'>0</font> ");}
												?>
                          </b>&nbsp;
                          <?php if($totrec > ( $objCurPage->crec  + $objCurPage->reccnt)) {?>
                          <input name="imgright" type="image" id="imgright" src="../images/butt_right.gif" alt="Show Next" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php echo  $objCurPage->crec  + $objCurPage->reccnt ?>'); this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self'" />
                          <input name="imgright2" type="image" id="imgright2" src="../images/butt_right2.gif" alt="Show Last" width="16" height="16" border="0" onclick="javascript: moveRec(this.form, '<?php if($totrec % $objCurPage->reccnt != 0) {echo($totrec - ($totrec % $objCurPage->reccnt));} else {echo($totrec - $objCurPage->reccnt);} ?>');  this.form.action='<?php echo ($objCurPage->cur_page_url); ?>'; this.form.target='_self'" />
                          <?php } else { ?>
                          <img src="images/butt_right_dis.gif" width="16" height="16" /> <img src="images/butt_right2_dis.gif" width="16" height="16" />
                          <?php } ?>
                          <input name="crec" type="hidden" id="crec" value="<?php echo $objCurPage->crec; ?>" /></td>
                      </tr>
                      <tr>
                        <td><?php 
                                                    $hasRecs=0;
                                                    $j=0;
                                                    
                                                        $tmpSql = "SELECT cat_id, cat_name, cat_photo, displayorder FROM tbl_photo_category a WHERE 1 "								
                                                        . " ORDER BY " . $objCurPage->sortby . " " . $objCurPage->sortmode
                                                        . " LIMIT " .  $objCurPage->crec  . "," . $pgrec ;
                                                    
													//echo $tmpSql; exit;
                                                    $rs = $objCurPage->link_id->query($tmpSql);
                                                    
                                                    if( (!$rs) || (!($rec = $rs->fetch_array())) )
                                                    {
                                                        echo("<font color='#CC0000'><br>no record found...<br></FONT>");
                                                    }
                                                    else
                                                    {
                                                    ?>
                          <table width="100%" cellspacing="1" cellpadding="4" class="formTextWithBorder">
                            <tr valign="top" class="formHeadingBkg">
                              <td width="6%">&nbsp;</td>
                              <td width="51%" style="text-align:left;">Title</td>
                              <td width="15%" style="text-align:center">View Photo</td>
                              <td width="23%" style="text-align:center">Action</td>
                            </tr>
                            <?php do {
                                               $j=$j+1; ?>
                            <tr class="<?php if($j % 2 == 0){echo("shade1bkg");}else{echo("shade2bkg");} ?>" id="item-<?php echo $rec['cat_id'] ?>"  >
                              <td  valign="top" align="center"><div class="squaredThree">
                                  <input type="checkbox" value="<?=$rec["cat_id"] ?>" id="squaredThree<?php echo $rec["cat_id"]; ?>" name="cat_ids[]" />
                                  <label for="squaredThree<?php echo $rec["cat_id"]; ?>"></label>
                                </div></td>
                              <td width="51%" valign="top"><?php echo $rec["cat_name"];?></td>
                              <td align="center"><?php if(!empty($rec['cat_photo'])){ ?>
                                <a style="cursor:pointer;" class="popThumb" href="" onclick="viewGallery_img(<?php echo $rec["cat_id"];?>);" ><img src="images/view2.png" width="18" height="17" border="0" /></a>
                                <?php }else{ echo "--"; }?></td>
                              <td align="center" valign="top"><a href="javascript:window.location.href='<?php echo ("gallery_category_details.php?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["cat_id"] . "&task=2"); ?>'" title="Edit Gallery"><img src="images/b_edit5.png" width="16" height="16" border="0"></a> &nbsp; <a href="javascript:if(window.confirm('Are You Sure ?')){window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&recid=" . $rec["cat_id"] . "&task=3"); ?>'}" title="Delete Gallery"><img src="images/b_drop.png" width="16" height="16" border="0"></a></td>
                            </tr>
                            <?php 					 
                                              }
                                              while($rec = $rs->fetch_array());?>
                          </table>
                          <?php }?>
                          <script language="JavaScript"> <?php echo("nrows = " . $j . ";"); ?></script>
                          <?php $rws = $j; ?></td>
                      </tr>
                      <tr>
                        <td height="32" align="right"><input name="sortby" type="hidden" id="sortby" value="<?= $objCurPage->sortby ?>">
                          <input name="sortmode" type="hidden" id="sortmode" value="<?= $objCurPage->sortmode ?>">
                          <input name="postMe" type="hidden" id="postMe" value="Y">
                          <input name="cmdAdd" type="button" class="btn btn-default" id="cmdAdd" value="Add" onClick="window.location.href='<?php echo ("gallery_category_details.php?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&task=1"); ?>'" >
                          &nbsp;
                          <input name="cmdDelete" type="submit" class="btn btn-default" id="cmdDelete" onClick="javascript:if(window.confirm('Are You Sure ?')){window.location.href='<?php echo ($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&task=5"); ?>'}" value="Delete">
                          <input name="cmdExit" type="button" class="btn btn-default" id="cmdExit" onClick="window.location.href='navigator.php'" value="Exit">
                          &nbsp;</td>
                      </tr>
                    </table></td>
                </tr>
              </table>
              <br>
              <br></td>
          </tr>
        </table>
      </form>
      <div class="col-sm-1">&nbsp;</div>
    </div>
    <script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script> 
    <script type="text/javascript">
var fixHelper = function(e, ui) {
		ui.children().each(function() {
		$(this).width($(this).width());
	});
	return ui;
}	
$(document).ready(function(e) {
    $(".formTextWithBorder").sortable(
	{
				items:"tr",
				helper: fixHelper,
				update:function(e, ui){
				Changecatdisplayorder();
	}
		//changeorder()
	});
	
	

	
function Changecatdisplayorder()
{
	
	 var orderRImage = $(".formTextWithBorder").sortable("serialize");
	 //alert(orderRImage);
	 $.ajax({
        			type: "POST", 
					url: "ajax/ajax_sortcategory.php",
					data: orderRImage+'&task=4',		
       				success: function(res_data) {
						//alert(res_data);
						if(res_data==1)
						{
						//alert("Success");
						return false;	
					 	} 
					else {
                    alert('Unable to sort due to some error');
				    return false;
            }
        }
    });		
}
});


</script> 
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>