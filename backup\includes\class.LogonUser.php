<?php
class LogonUser
{

	//----------------------------------------------------------------
	//--- Member Variables
	private $_sessionId;
	public $cur_user_login_time;
	public $cur_user_num_id;
	public $cur_user_login_name;
	public $cur_user_full_name;
	public $cur_user_privileges;
	public $cur_user_group_type;
	public $cur_user_group_name;
	public $cur_user_table;
	public $cur_user_table_field_prefix;
	public $cur_user_special_login_for;
	public $cur_user_online_status;
	
	
	public $cur_user_lastlogin_time; // for last login time
	public $last_login_time;        //  for current login time 
	
	//--- Constructor ---
	public function __construct()
	{
		$this->_sessionId = session_id();
 		$this->cur_user_login_time = time() + (5*60+30)*60;
		$this->cur_user_num_id = 0;
		$this->cur_user_login_name = "";
		$this->cur_user_full_name = "";
		$this->cur_user_privileges = "";
		$this->cur_user_group_type = "";
		$this->cur_user_group_name = "";
		$this->cur_user_table = "";
		$this->cur_user_table_field_prefix = "";
		$this->cur_user_special_login_for = "";
		$this->cur_user_online_status = 0;
		$this->cur_user_lastlogin_time="";
		$this->last_login_time="";
	}

	//--- Class Destructor ---
	function __destruct()
	{
		//--- do nothing
	}
	
	//--- Get Session Id ---
	public function getSessionId()
	{
		return $this->_sessionId;
	}

	//--- Get Session Id ---
	public function getLoginTimeFormatted()
	{
		return CommonFunctions::formatMyDateTime($this->cur_user_login_time, "d-M-Y h:i:s A", 1, "");
	}

	//----------------------------------------------------------------
}
?>
