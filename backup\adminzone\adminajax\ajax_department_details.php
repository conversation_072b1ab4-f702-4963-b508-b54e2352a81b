<?php
//print_r($_POST); die();

require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $dept_id;
	public $dept_name,$dept_desc;
	public $arr_dept_name, $arr_dept_desc, $dept_name_edit, $dept_desc_edit ;
	
			
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		//--- Set Current Module Name ---
		$this->module_name = "Ajax Department Details";
		$this->cur_page_url = "ajax_department_details.php";
		$this->list_page_url = "ajax_department_details.php";
				
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
			
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
								
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 0;
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
			
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->dept_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->dept_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->dept_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->dept_id = 0;
			}
		}
		
		//echo $this->dept_id;die("tendetid");
		
		return 1;
	}


	//==== Create New Record ====
	public function createPart()
	{
	
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
         	
		if ($this->dept_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		$this->arr_dept_name = array();
		$this->arr_dept_desc = array();
		$this->dept_id = "";		
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{ 	
	   //echo "aaa";die();
	   if(isset($_POST["dept_name"]))
		{
			$i = 0;
			foreach($_POST["dept_name"] as $val)
			{				
				$this->arr_dept_name[$i] = $val;				
				$i++;
			}
		}
		
		if(isset($_POST["dept_desc"]))
		{
			$i = 0;
			foreach($_POST["dept_desc"] as $val)
			{	
				$this->arr_dept_desc[$i] = CommonFunctions::replaceChars($val, 0);				
				$i++;
			}
		}
		//print_r($this->arr_dept_desc);exit();
		
		if($this->task == 2)
		{
			$this->dept_name_edit = CommonFunctions::replaceChars($_POST["dept_name_edit"], 0);
			$this->dept_desc_edit = CommonFunctions::replaceChars($_POST["dept_desc_edit"], 0);
			
		}
							
		return 1;
	}	

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";
		
		if($this->task == 1){
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->arr_dept_name); $x++)
				{
					if($this->arr_dept_name[$x] == "")
					{
						$myerr = "Please specify department name -".$x;
						break;
					}
					else if(DatabaseManager::iexistsInTable($this->link_id, "tbl_department", "dept_id <> " . $this->dept_id . " and dept_name = '" . $this->arr_dept_name[$x] . "' ") == 1)
					{
						$myerr = "Department Name: ".$this->arr_dept_name[$x]. " already exists.";
					}
				}
				
			}

	    }
		
		if($this->task == 2){
			
			if($myerr == "")	
			{
					if($this->dept_name_edit == "")
					{
						$myerr = "Please specify department name";
					}
				
			}	
	    }

		if($myerr != "")
		{
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction 
		$this->link_id->autocommit(FALSE);
			
		if($this->task == 1)
		{
			//echo count($this->arr_dept_name);die("ddd");
			for($i=1; $i<count($this->arr_dept_name); $i++)
			{
				//--- Insert Data
				$tmpSql = "INSERT INTO tbl_department(dept_id, dept_name, dept_desc)" 
						. " VALUES(null, '" . addslashes($this->arr_dept_name[$i]) . "'" 
						. ", '" . addslashes($this->arr_dept_desc[$i]) . "'"
						. ")";
				//echo $tmpSql;									
				$rs = $this->link_id->query($tmpSql);
				
				if($this->link_id->affected_rows == -1)
				{								
					break;
				}			
			
			}//exit;
			
			if($myerr != "")
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();				
			}			
			else
			{
				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
			
		}
		else if($this->task == 2)
		{
			$tmpSql = "UPDATE tbl_department SET " 					
					. "  dept_name = '" . addslashes($this->dept_name_edit) ."'"
					. ", dept_desc = '" . addslashes($this->dept_desc_edit) ."'"
					. "  WHERE dept_id = " . $this->dept_id;
					
		   //echo $tmpSql	;die("ddd");	
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();
			}
				
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			echo '2';
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
}

$objCurPage = new CurrentPage();
?>