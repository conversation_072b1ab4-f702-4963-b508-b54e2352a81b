<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
require_once("../php_mailer/class.phpmailer.php");
@session_start();

$inactive = 600; // Set timeout period in seconds

if (isset($_SESSION['timeout'])) {
    $session_life = time() - $_SESSION['timeout'];
    if ($session_life > $inactive) {
        session_destroy();
        header("Location: index.php");
    }
}
$_SESSION['timeout'] = time();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	
	public $app_config;		// Application Configuration Settings
	
	public $alumni_id;
	public $password;
	public $alumni_cpwd;
	public $reg_no;
	public $prefix;
	public $first_name;
	public $middle_name;
	public $last_name;
	public $gender;
	public $date_of_birth;
	public $d_day;
	public $d_mon;
	public $d_year;
	public $anniversary_day;
	public $anniversary_month;
	public $alumni_email;
	public $family_info;
	public $alumni_photo;
	public $alumni_photo_edit;
	public $dept;
	//public $stream;
	
	public $year_of_passing;	
	public $qualification;
	public $expertise;
	public $membership;
	public $street;
	public $city;
	public $state;
	public $country;
	public $per_address;
	public $office_phone;
	public $res_phone;
	public $mobile_phone;
	public $fax;
	public $email;
	public $link_url;
	public $institute_alumni;
	public $p_year;
	public $monthNames;
	public $fullname;
	
	public $designation;
	public $organisation;
	public $organisation_arr; 
	public $designation_arr;
	public $from_arr;
	public $to_arr;
	public $from;
	public $to;
	public $career_id;
	
	public $arr_career_id,$arr_career_organisation, $arr_career_designation, $arr_career_from, $arr_career_to;
	
	//public $mark_new;
	

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Manage Alumni";
		$this->cur_page_url = "alumni_details.php";
		$this->list_page_url = "alumni.php";

		//--- Check User Access permissions	
		UserAccess::checkUserLogin("", "admin");
		
		//------array
		$this->monthNames=array("Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec");
				
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(2);/**/
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		//--- Get Record Position (crec) passed to this page ----
		$this->reccnt = 10;
		//$this->getRecordPostion();

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		$this->maxfilesize = 1*1024*1024;
		$this->fullname="";
		//--- Initialize Special Properties ---
		$this->initSpecialProperties();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			case 3:
				//--- Delete Record
				//$this->deletePart();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
		}
	
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}

		return 1;
	}
	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->alumni_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["alumni_id"]))
			{
				$this->alumni_id = intval(CommonFunctions::replaceChars($_GET["alumni_id"], 0));
			}
			else if(isset($_POST["alumni_id"]))
			{
				$this->alumni_id = intval(CommonFunctions::replaceChars($_POST["alumni_id"], 0));
			}
			else
			{
				$this->alumni_id = 0;
			}
			
		}
		
		return 1;
	}

	//==== Initialize Special Properties ====
	public function initSpecialProperties()
	{
		return 1;
		
	}

	//==== Create New Record ====
	public function createPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();			
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editPart()
	{ 
		//--- Get Record Id ---
		$this->getRecordId();
		
		if ($this->alumni_id == 0)
		{
			$_SESSION['app_error'] = "Record not found..";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
			//echo $this->alumni_id; die();
		}

		return 1;
	}
	
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			//--- Add Mode. Initialize field values to blank or zero or null
			 $this->password="";
			 $this->alumni_cpwd="";
			 $this->reg_no="";
			 $this->prefix="";
			 $this->first_name="";
			 $this->middle_name="";
			 $this->last_name="";
			 $this->gender="";
			 $this->date_of_birth="";
			 $this->anniversary_day=0;
			 $this->anniversary_month=0;
			 $this->alumni_email="";
			 $this->family_info="";
			 $this->alumni_photo="";	 
			 $this->dept="";
			// $this->stream="";
			 $this->year_of_passing="";	
			 $this->qualification="";
			 $this->expertise="";
			 $this->membership="";
			 $this->street="";
			 $this->city="";
			 $this->state="";
			 $this->country="";
			 $this->per_address="";
			 $this->office_phone="";
			 $this->res_phone="";
			 $this->mobile_phone="";
			 $this->fax="";
			 $this->email="";
			 $this->link_url="";
			 $this->institute_alumni="";
			// $this->mark_new = 0;
			
			 
			/*$this->organisation="";
			$this->designation="";
            $this->from = "";
			$this->to = "";*/
			
			$this->organisation_arr = array();	
			$this->designation_arr = array();
			$this->from_arr = array();
			$this->to_arr = array();
			$this->career_id = "";
	 
		}
		else if($this->task == 2)
		{
			//--- Edit Mode. Initialize fields by getting values from Database
				
			$tmpSql = "SELECT alumni_id, password, show_pwd, reg_no, prefix, first_name, middle_name, last_name, gender, date_of_birth, anniversary_day, anniversary_month, family_info, alumni_photo, dept, year_of_passing, qualification, expertise, membership, street, city, state, country, per_address, office_phone, res_phone, mobile_phone, fax, email, link_url, institute_alumni, status, is_show FROM tbl_alumni"
				. " WHERE alumni_id = '" . $this->alumni_id . "'";
			//echo $tmpSql; die();

			$rs = $this->link_id->query($tmpSql);		
						
			if( (!$rs) || (!($rec =$rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found...";
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
			else
			{
				 $this->alumni_id = $rec["alumni_id"];
				 $this->password=$rec["password"];
				 $this->alumni_cpwd=$rec["password"];
				 $this->reg_no=$rec["reg_no"];
				 $this->prefix=$rec["prefix"];
				 $this->first_name=$rec["first_name"];
				 $this->middle_name=$rec["middle_name"];
				 $this->last_name=$rec["last_name"];
				 $this->gender=$rec["gender"];
				 $this->date_of_birth=$rec["date_of_birth"];
				 list($this->d_year,$this->d_mon,$this->d_day)=explode("-",$this->date_of_birth);				
				 $this->anniversary_day=$rec["anniversary_day"];
				 $this->anniversary_month=$rec["anniversary_month"];
				 $this->email=$rec["email"];
				 $this->family_info=stripslashes($rec["family_info"]);
				 $this->alumni_photo=$rec["alumni_photo"];				
				 $this->alumni_photo_edit=$this->alumni_photo; 
				 $this->dept=$rec["dept"];
				 //$this->stream=$rec["stream"];
				 $this->year_of_passing=$rec["year_of_passing"];
				 $this->qualification=stripslashes($rec["qualification"]);
				 $this->expertise=stripslashes($rec["expertise"]);
				 $this->membership=stripslashes($rec["membership"]);
				 //$this->designation=stripslashes($rec["designation"]);
				 //$this->salary=stripslashes($rec["salary"]);
				 //$this->organisation=stripslashes($rec["organisation"]);
				 $this->street=stripslashes($rec["street"]);
				 $this->city=stripslashes($rec["city"]);
				 $this->state=stripslashes($rec["state"]);
				 $this->country=stripslashes($rec["country"]);
				 $this->per_address=stripslashes($rec["per_address"]);
				 $this->office_phone=$rec["office_phone"];
				 $this->res_phone=$rec["res_phone"];
				 $this->mobile_phone=$rec["mobile_phone"];
				 $this->fax=$rec["fax"];
				 $this->link_url=stripslashes($rec["link_url"]);
				 $this->institute_alumni=stripslashes($rec["institute_alumni"]);
				 
				 /*$this->organisation = stripslashes($rec["organisation"]);
			     $this->designation = stripslashes($rec["designation"]);
				 $this->from = ($rec["from"]);
			     $this->to = ($rec["to"]);*/
				 
				 //$this->mark_new= $rec["is_show"];
			}
		}
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{
		$this->alumni_id = intval(CommonFunctions::replaceChars($_POST["alumni_id"], 0));
		
		if ($this->task==1)
		{
			$this->password = CommonFunctions::replaceChars($_POST["password"], 11);
			$this->alumni_cpwd = CommonFunctions::replaceChars($_POST["alumni_cpwd"], 11);
		}
					
		$this->reg_no = CommonFunctions::replaceChars(stripslashes($_POST["reg_no"]),11);
		
		$this->prefix = CommonFunctions::replaceChars($_POST["prefix"], 11);
		
		$this->first_name = CommonFunctions::replaceChars($_POST["first_name"], 11);
		
		$this->middle_name = CommonFunctions::replaceChars($_POST["middle_name"], 11);
		
		$this->last_name = CommonFunctions::replaceChars($_POST["last_name"], 11);
		
		if(isset($_POST["first_name"]))
		{
		$this->fullname .=CommonFunctions::replaceChars($_POST["first_name"], 11);
		}
		if(isset($_POST["middle_name"]))
		{
		$this->fullname .=" ".CommonFunctions::replaceChars($_POST["middle_name"], 11);
		}
		if(isset($_POST["last_name"]))
		{
		$this->fullname .=" ".CommonFunctions::replaceChars($_POST["last_name"], 11);
		}
		if(isset($_POST["gender"]))
		{
			$this->gender = CommonFunctions::replaceChars($_POST["gender"], 0);
		}
		else
		{
			$this->gender = "";
		}
		$this->d_day=$_POST["d_day"];
		
		$this->d_mon=$_POST["d_mon"];
		
		$this->d_year=$_POST["d_year"];
		
		$this->date_of_birth = CommonFunctions::replaceChars($_POST["d_year"]."-".$_POST["d_mon"]."-".$_POST["d_day"], 0);
		
		$this->anniversary_day = intval(CommonFunctions::replaceChars($_POST["anniversary_day"], 0));
		
		$this->anniversary_month = intval(CommonFunctions::replaceChars($_POST["anniversary_month"], 0));
		
		$this->family_info = CommonFunctions::replaceChars(stripslashes($_POST["family_info"]), 11);
		
		$this->alumni_photo_edit = CommonFunctions::replaceChars($_POST["alumni_photo_edit"], 0);
		
		$this->dept = CommonFunctions::replaceChars($_POST["dept"], 0);
		
		//$this->stream = CommonFunctions::replaceChars($_POST["stream"], 0);
		
		$this->year_of_passing = CommonFunctions::replaceChars($_POST["year_of_passing"], 0);
		
		$this->qualification = CommonFunctions::replaceChars(stripslashes($_POST["qualification"]), 11);
		
		$this->expertise = CommonFunctions::replaceChars(stripslashes($_POST["expertise"]), 11);
		
		$this->membership = CommonFunctions::replaceChars(stripslashes($_POST["membership"]), 11);
		
		//$this->designation = CommonFunctions::replaceChars(stripslashes($_POST["designation"]), 11);
		
		//$this->salary = CommonFunctions::replaceChars(stripslashes($_POST["salary"]), 11);
		
		//$this->organisation = CommonFunctions::replaceChars(stripslashes($_POST["organisation"]), 11);
		
		$this->street = CommonFunctions::replaceChars(stripslashes($_POST["street"]), 11);
		
		$this->city = CommonFunctions::replaceChars(stripslashes($_POST["city"]), 11);
		
		$this->state = CommonFunctions::replaceChars(stripslashes($_POST["state"]), 11);
		
		$this->country = CommonFunctions::replaceChars(stripslashes($_POST["country"]), 11);
		
		$this->per_address = CommonFunctions::replaceChars(stripslashes($_POST["per_address"]), 11);
		
		$this->office_phone = CommonFunctions::replaceChars($_POST["office_phone"], 11);
		
		$this->res_phone = CommonFunctions::replaceChars($_POST["res_phone"], 11);
		
		$this->mobile_phone = CommonFunctions::replaceChars($_POST["mobile_phone"], 11);
		
		$this->fax = CommonFunctions::replaceChars($_POST["fax"], 11);
		
		$this->email = CommonFunctions::replaceChars($_POST["email"], 11);
		
		$this->link_url = CommonFunctions::replaceChars(stripslashes($_POST["link_url"]), 11);
		
		$this->institute_alumni = CommonFunctions::replaceChars(stripslashes($_POST["institute_alumni"]), 11);
		
		//$this->mark_new = intval(CommonFunctions::replaceChars($_POST["mark_new"], 0));
		if($this->task == 1)
		{	
			if(isset($_POST["organisation"]))
			{
				$i = 0;
				foreach($_POST["organisation"] as $val)
				{	
					$this->organisation_arr[$i] = CommonFunctions::replaceChars($val, 0);	
					$this->designation_arr[$i] = !empty($val)?CommonFunctions::replaceChars($_POST["designation_". $i], 0):"";	
					$this->from_arr[$i] = !empty($val)?CommonFunctions::replaceChars($_POST["from_". $i], 0):"";
					$this->to_arr[$i] = !empty($val)?CommonFunctions::replaceChars($_POST["to_". $i], 0):"";		
					$i++;
				}
			}
		}
		
		if($this->task == 2)
		{
			
			$tSql = "SELECT * FROM tbl_alumni_career WHERE alumni_id = ".$this->alumni_id." ORDER BY career_id ASC";
						
			$rs = $this->link_id->query($tSql);
			
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				
			}
			else
			{
				$i = 1;
				do{
					 $this->arr_career_id[$i] = $rec["career_id"];
					 $this->arr_career_organisation[$i] = $rec["career_organisation"];
					 $this->arr_career_designation[$i] = $rec["career_designation"];
					 $this->arr_career_from[$i] = $rec["career_from"];
					 $this->arr_career_to[$i] = $rec["career_to"];
					 
				 	$i++;
				  }while($rec = $rs->fetch_array());
			}
			
			for($x=1;$x<=count($this->arr_career_id);$x++)
			{
				$this->arr_career_organisation[$x] = $_POST["organisation_".$this->arr_career_id[$x]];
				$this->arr_career_designation[$x] = $_POST["designation_".$this->arr_career_id[$x]];
				$this->arr_career_from[$x] = $_POST["from_".$this->arr_career_id[$x]];
				$this->arr_career_to[$x] = $_POST["to_".$this->arr_career_id[$x]];
			}
			
		}
							
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";		
		
	   if($this->email == "")
		{
			$myerr = "Email ID is not specified";
		}
		//else if(CommonFunctions::isAddressValid($this->email) == false)
		else if(CommonFunctions::isValidEmail($this->email) == false)
		{
			$myerr= "Email ID specified is not valid";
		}
		else if(DatabaseManager::iexistsInTable($this->link_id, "tbl_alumni", "alumni_id <> " . $this->alumni_id . " and email = '" . $this->email . "' ") == 1 && $this->task==1)
		{
			$myerr = "Email ID: " . $this->email . " already exists. Specify a new one...";
		}	
		else if($this->password == "" && $this->task==1)
		{
			$myerr = "Password is not specified";
		}
		else if($this->alumni_cpwd == "" && $this->task==1)
		{
			$myerr = "Retype the Password";
		}
		else if($this->password != $this->alumni_cpwd && $this->task==1)
		{
			$myerr = "Password mismatched";
		}
		/*else if($this->reg_no == "")
		{
			$myerr = "Registration No. is not specified";
		}*/
		else if(DatabaseManager::iexistsInTable($this->link_id, "tbl_alumni", "alumni_id <> " . $this->alumni_id . " and reg_no = '" . $this->reg_no . "' ") == 1 && $this->reg_no != "")
		{
			$myerr = "Registration No.: " . $this->reg_no . " already exists. Specify a new one...";
		}		
		else if($this->first_name == "")
		{
			$myerr = "First Name is not specified";
		}
		else if($this->last_name == "")
		{
			$myerr = "Last Name is not specified";
		}
		else if($this->gender == "")
		{
			$myerr = "Gender is not specified";
		}
		else if($this->d_day == "" || $this->d_mon == "" || $this->d_year == "")
		{
			$myerr = "Date of Birth is not specified";
		}
		/*else if($this->anniversary_day=="" && $this->anniversary_month!="")
		{
			$myerr = "Anniverasary Day is not specified";
		}
		else if($this->anniversary_day!="" && $this->anniversary_month=="")
		{
			$myerr = "Anniverasary Month is not specified";
		}	*/
		else if($this->dept == "")
		{
			$myerr = "Select Stream";
		}	
		/*else if($this->stream == "")
		{
			$myerr = "Degree is not specified";
		}*/
		else if($this->year_of_passing == "")
		{
			$myerr = "Year of passing is not specified";
		}
		else if($this->street == "")
		{
			$myerr = "Street is not specified";
		}
		else if($this->city == "")
		{
			$myerr = "City is not specified";
		}
		else if($this->state == "")
		{
			$myerr = "State is not specified";
		}
		else if($this->country == "")
		{
			$myerr = "Country is not specified";
		}
		else if($this->per_address == "")
		{
			$myerr = "Permanent Address is not specified";
		}
		else if($_FILES['alumni_photo']['name'] != "")
		{				
			if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']), "jpg,gif,jpeg,png") != 1)
			{
				$myerr = "Un-supported image format";
			}			
			else if(($_FILES['alumni_photo']['size'] > $this->maxfilesize))
			{
				$myerr = "Image file size should not be more than 1 MB";
			}
			
		}
		
		
		if($this->task == 1){
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->organisation_arr); $x++)
				{
					if($this->organisation_arr[$x] == "")
					{
						$myerr = "Please specify organisation -".$x;
						break;
					}
				}
				
			}
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->designation_arr); $x++)
				{
					if($this->designation_arr[$x] == "")
					{
						$myerr = "Please specify designation -".$x;
						break;
					}
				}
				
			}
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->from_arr); $x++)
				{
					if($this->from_arr[$x] == "")
					{
						$myerr = "Please specify from -".$x;
						break;
					}
				}
				
			}
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->to_arr); $x++)
				{
					if($this->to_arr[$x] == "")
					{
						$myerr = "Please specify to -".$x;
						break;
					}
				}
				
			}
			
	    }
		
		
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	
	}
	
	public function uploadImage($recid)
	{			
		$myerr = "";
		
		//Upload Image*******************
		if(trim($_FILES['alumni_photo']['name']) != "")
		{
			$item_image_flname = "alumni" . "_" . $recid . "." . CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']);
			$item_small_image_new = "../alumni_photo/" . $item_image_flname;
			if(!move_uploaded_file($_FILES['alumni_photo']['tmp_name'], $item_small_image_new))
			{
				$myerr = "Image couldn't be uploaded.";
			}
			else
			{			
				$source = $item_small_image_new;
				
				$width = "200";
				//$height = "153"; 
				$height = "200"; 
							
				// Get new dimensions
				list($width_orig, $height_orig) = getimagesize($source);
								
				// Resample
				$image_p = imagecreatetruecolor($width, $height);

				if(CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']) == "gif")
				{
					$image = imagecreatefromgif($source);
				}
				else if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']), "jpg,jpeg") == 1 )
				{
					$image = imagecreatefromjpeg($source);
				}
				else if(CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']) == "png")
				{
					$image = imagecreatefrompng($source);
					imagealphablending( $image_p, false );
					imagesavealpha( $image_p, true );
				}
				//echo $image;die();
								
				imagecopyresampled($image_p, $image, 0, 0, 0, 0, $width, $height,$width_orig, $height_orig);
				
				$target = $item_small_image_new;
				
				if(CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']) == "gif")
				{
					if(!imagegif($image_p, $target))
					{
						$myerr  = "Problem occurred while uploading Photo";
					}
				}
				else if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']), "jpg,jpeg") == 1 )
				{
					if(!imagejpeg($image_p, $target, 90))
					{
						$myerr  = "Problem occurred while uploading Photo";
					}
				}
				else if(CommonFunctions::getFileExtension($_FILES['alumni_photo']['name']) ==  "png")
				{
					if(!imagepng($image_p, $target))
					{
						$myerr  = "Problem occurred while uploading Photo";
					}
				}
				
				$this->alumni_photo_edit = $item_image_flname;
			
			}
		}

		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		
		return 1;
	}
	
	
	public function insertCareerData($recid)
	{			
		$myerr = "";
		
		//echo $recid."-AAAAA";
		//print_r($this->organisation_arr); die('ee');
			for($i=1; $i<count($this->organisation_arr); $i++)
			{
				/*print_r($this->arr_video_link) . "<br/>";
				die('ee');*/
				$tmpSql = "INSERT INTO tbl_alumni_career(career_id, alumni_id, career_organisation, career_designation, career_from, career_to)" 
						. " VALUES(null, '" . $recid . "'" 
						. ", '" . addslashes($this->organisation_arr[$i])."'"
						. ", '" . addslashes($this->designation_arr[$i])."'"
						. ", '" . addslashes($this->from_arr[$i])."'"
						. ", '" . addslashes($this->to_arr[$i])."')";
				//echo $tmpSql; die('ss');
				$rs = $this->link_id->query($tmpSql);	
			}
			
			/*if($myerr != "")
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();				
			}			
			else
			{
				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}*/

		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		
		return 1;
	}
	
	public function updateCareerData($recid)
	{			
		$myerr = "";
		
		//echo $recid."-AAAAA";
		//print_r($this->arr_career_id); die('ee');
			
		for($x=1;$x<=count($this->arr_career_id);$x++)
				{										
					if($_POST["organisation_".$this->arr_career_id[$x]])
					{
						
						//$sqlUpdt = "UPDATE tbl_alumni_career SET career_organisation = '".$this->arr_career_organisation[$x]."' WHERE career_id = ".$this->arr_career_id[$x];
						
						//--- Update Data
						$sqlUpdt = "UPDATE tbl_alumni_career SET "
						. "career_organisation= '" . addslashes($this->arr_career_organisation[$x]) . "'"
						. ",career_designation= '" . addslashes($this->arr_career_designation[$x]) . "'"
						. ",career_from= '" . $this->arr_career_from[$x] . "'"
						. ",career_to= '" . $this->arr_career_to[$x] . "'"
                        . " WHERE alumni_id = '" . $this->alumni_id . "'"
						. "AND career_id = '" . $this->arr_career_id[$x] . "'";
						//echo $sqlUpdt; die();
						
						$rs2 = $this->link_id->query($sqlUpdt);
					}
				}
				
				/*$this->link_id->commit();		
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();*/		

		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		
		return 1;
	}
	
	public function sendMail()
	{
			
	  if($this->fullname != "")
	  {
								
	  $to = $this->email;
	  $from = DatabaseManager::igetDataFromTable($this->link_id, "admin_details", "user_email" , "", "");
		
		
		$headers = "MIME-Version: 1.0\r\n";
		$headers .= "Content-type: text/html; charset=utf-8\r\n";		
		
		
		//$message = '';
		//$message .= "";
		//$body = '';
		
		$body = '<table width="600" border="0" align="center" cellpadding="2" cellspacing="1"  style="font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px">
				  <tr>
					<td colspan="3"><b>Dear</b> '.$this->fullname.',</td>
				  </tr>
				  <tr>
				  <td colspan="3">you have been registered as Alumni member of UGIE.</td>
				  </tr>	
				   <tr>
				   	<td colspan="3">Your login details are as follows:</td>
				  </tr>
                  
                   <tr>
					  <td colspan="2">Want to Visit the website: <a href="https://ugierkl.ac.in/alumni_login.php"  target = "_blank">Click here to login</a></td> 
  <tr>
				  	  <td colspan="2">&nbsp;</td>			 
					  </tr>';
				 
		 if($this->email != "" )
		 {  
			$body .= '<tr>
					<td width="124" align="left" valign="top"><b>Email Id  : </b></td>
					<td width="465" align="left">'.$this->email.' </td>
  					</tr>';
		 }
		if($this->password!="")
		{
			$body .= '<tr>
						<td align="left" valign="top"><b>Password : </b></td>
						<td align="left">'.$this->password.'</td>
					  </tr>';   
	    } 
	
		$body .= '<tr>
						<td colspan="3">Advantages provided by us are as follows:</td>
				  	</tr>
                    
                    <tr>
						<td colspan="3">&nbsp;</td>
				  	</tr>
				    <tr>
                    <tr>
						<td colspan="3">
                        <ul>
                        <li>Update your profile</li>
                        <li>Change your password</li>
                        <li>Job search</li>
                        </ul>
						</td>
				  	</tr>
				    <tr>
                    <tr>
						<td colspan="3">&nbsp;</td>
				  	</tr>
				    <tr>
						<td colspan="3">From,</td>
				  	</tr>	
				  	<tr>
						<td colspan="3">WebMaster Team</td>
				  	</tr>	
				  	<tr>
						<td colspan="3">&nbsp;</td>
				  	</tr>
                    <tr>
						<td colspan="3">Administrator</td>
				  	</tr>                    
				  </table>';
				  //$message .= $body;
				  $subject = "Login Information";	
				  
				  	
		try 
		{
			$mail = new PHPMailer();
			//$body = $message;
			$body = str_replace("[\]",'',$body);			
			//$mail->IsSMTP(); // telling the class to use SMTP
			//$mail->Host       = "mail.yourdomain.com"; // SMTP server
			//$mail->SMTPDebug  = 0;                     // enables SMTP debug information (for testing)
													   // 1 = errors and messages
													   // 2 = messages only
			$mail->SMTPDebug = false;										   
			//$mail->SMTPAuth   = $this->app_config["smtp_auth"];                  // enable SMTP authentication
			//$mail->SMTPSecure = $this->app_config["smtp_secure"];                 // sets the prefix to the servier
			//$mail->Host       = $this->app_config["smtp_host"];      // sets GMAIL as the SMTP server
			//$mail->Port       = $this->app_config["smtp_port"];                   // set the SMTP port for the GMAIL server
			//$mail->Username   = $this->app_config["mail_from"];  // GMAIL username
			//$mail->Password   = $this->app_config["mail_pass"];            // GMAIL password
			
			//$mail->SMTPAuth = true;  // authentication enabled
			
			$mail->SetFrom($from, "JES Alumni Admin");					
			$mail->AddReplyTo($this->email, "");					
			$mail->Subject    = $subject;					
			$mail->AltBody    = "To view the message, please use an HTML compatible email viewer!"; // optional, comment out and test					
			$mail->MsgHTML($body);					
			$address = $this->email;
			$mail->AddAddress($address, "");
			//$mail->AddCC("<EMAIL>", 'xyz');			
			//$mail->AddAttachment($File);				
		/*echo ($headers . "<br/>" . $subject . "<br/>" . $message . "<br>" . $this->email) . "<br />"; 
		die();*/
			/*if($_SERVER['SERVER_NAME'] != "*************")
			{*/
				$mail->Send();
			//}
		} 
		catch (phpmailerException $e) 
		{				  
		  $e->errorMessage();
		  echo "Mailer Error: " . $mail->ErrorInfo;
		}
		return 1; 

	  }
   }
	//==== Save Data ====
	public function saveData()
	{
	
		$myerr = "";
		
		//--- Begin Transaction
		$this->link_id->autocommit(FALSE);
	
	if($this->task == 1)
	{
		//--- Get New Id
		$this->alumni_id = DatabaseManager::igetNewId($this->link_id, "tbl_alumni", "alumni_id", "");
					//--- Upload Image
		$this->uploadImage($this->alumni_id);
		
		$this->insertCareerData($this->alumni_id);
	
			//--- Insert Data
			$tmpSql = "INSERT INTO tbl_alumni(alumni_id, password, show_pwd, reg_no, prefix, first_name, middle_name, last_name
			,gender,date_of_birth,anniversary_day,anniversary_month,family_info,alumni_photo,dept,year_of_passing,qualification,
			expertise,membership,street,city,state,country,per_address,office_phone,res_phone,
			mobile_phone,fax,email,link_url,institute_alumni,status,is_show)" 
			. " VALUES('" . $this->alumni_id . "'"
			. ", md5('".addslashes($this->password)."')"
			. ", '".addslashes($this->password)."'"				
			. ", '" . addslashes($this->reg_no) . "'"
			. ", '" . $this->prefix . "'"
			. ", '" . $this->first_name . "'"
			. ", '" . $this->middle_name . "'"
			. ", '" . $this->last_name . "'"
			. ", '" . $this->gender . "'"
			. ", '" . $this->date_of_birth . "'"
			. ", '" . $this->anniversary_day . "'"
			. ", '" . $this->anniversary_month . "'"
			. ", '" . addslashes($this->family_info) . "'"
			. ", '" . $this->alumni_photo_edit . "'"
			. ", '" . $this->dept . "'"
			//. ", '" . $this->stream . "'"
			. ", '" . $this->year_of_passing . "'"
			. ", '" . addslashes($this->qualification) . "'"
			. ", '" . addslashes($this->expertise) . "'"
			. ", '" . addslashes($this->membership) . "'"
			//. ", '" . addslashes($this->designation) . "'"
			//. ", '" . addslashes($this->salary) . "'"
			//. ", '" . addslashes($this->organisation) . "'"
			. ", '" . addslashes($this->street) . "'"
			. ", '" . addslashes($this->city) . "'"
			. ", '" . addslashes($this->state) . "'"
			. ", '" . addslashes($this->country) . "'"
			. ", '" . addslashes($this->per_address) . "'"
			. ", '" . $this->office_phone . "'"
			. ", '" . $this->res_phone . "'"
			. ", '" . $this->mobile_phone . "'"
			. ", '" . $this->fax . "'"
			. ", '" . $this->email . "'"
			. ", '" . addslashes($this->link_url) . "'"
			. ", '" . addslashes($this->institute_alumni) . "'"
			. ", '1'"
			. ", '0'"
			. ")";

		//echo $tmpSql; die();
			$rs = $this->link_id->query($tmpSql);		
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";
				$this->alumni_id = 0;
			}
			else
			{
			
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully saved.";
				$this->sendMail();
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
		}
		else if($this->task == 2)
		{
			
			 $this->updateCareerData($this->alumni_id);
			
			 if(trim($_FILES['alumni_photo']['name']) != "")
			 {
					if ($this->alumni_photo =="")
					{
						$this->alumni_photo=$this->alumni_photo_edit;
					
						if(file_exists("./alumni_photo/".$this->alumni_photo))
						{
							unlink("./alumni_photo/".$this->alumni_photo);
						}
					}
					$this->uploadImage($this->alumni_id);
			 }			
			//--- Update Data
			$tmpSql = "UPDATE tbl_alumni SET "
			. "reg_no= '" . $this->reg_no . "'"
			. ",prefix= '" . $this->prefix . "'"
			. ",first_name= '" . $this->first_name . "'"
			. ",middle_name= '" . $this->middle_name . "'"
			. ",last_name= '" . $this->last_name . "'"
			. ",gender= '" . $this->gender . "'"
			. ",date_of_birth= '" . $this->date_of_birth . "'"
			. ",anniversary_day= '" . $this->anniversary_day . "'"
			. ",anniversary_month= '" . $this->anniversary_month . "'"
			. ",family_info= '" . addslashes($this->family_info) . "'"
			. ",alumni_photo='" . $this->alumni_photo_edit . "'"
			. ",dept= '" . $this->dept . "'"
			//. ",stream= '" . $this->stream . "'"
			. ",year_of_passing= '" . $this->year_of_passing . "'"
			. ",qualification= '" . addslashes($this->qualification) . "'"
			. ",expertise= '" . addslashes($this->expertise) . "'"
			. ",membership= '" . addslashes($this->membership) . "'"
			//. ",designation= '" . addslashes($this->designation) . "'"
			//. ",salary= '" . addslashes($this->salary) . "'"
			//. ",organisation= '" . addslashes($this->organisation) . "'"
			. ",street= '" . addslashes($this->street) . "'"
			. ",city= '" . addslashes($this->city) . "'"
			. ",state= '" . addslashes($this->state) . "'"
			. ",country= '" . addslashes($this->country) . "'"
			. ",per_address= '" . addslashes($this->per_address) . "'"
			. ",office_phone='" . $this->office_phone . "'"
			. ",res_phone= '" . $this->res_phone . "'"
			. ",mobile_phone= '" . $this->mobile_phone . "'"
			. ",fax= '" . $this->fax . "'"
			. ",email= '" . $this->email . "'"
			. ",link_url= '" . addslashes($this->link_url) . "'"
			. ",institute_alumni= '" . addslashes($this->institute_alumni) . "'"
			//. ",is_show = '" . $this->mark_new . "'"
			. " WHERE alumni_id = '" . $this->alumni_id . "'";
			#echo $tmpSql; die();
			
			$rs = $this->link_id->query($tmpSql);		
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";
			}
			else
			{			
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated.";
				//$this->sendMail();
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
			}
			
			
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}

$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>

<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script type="text/javascript" src="js/jquery-1.10.2.js"></script>
<script type="text/javascript" src="js/jquery-ui.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />

<link href="css/colorbox.css" rel="stylesheet" />
<link href="css/popbox.css" rel="stylesheet" type="text/css" />
<link href="css/accrodiantree.css" rel="stylesheet" />
<!--<script type="text/javascript" src="../js/jquery-1.10.1.min.js"></script>-->
<script type="text/javascript" src="js/jquery-ui-1.10.2.custom.min.js"></script>
<script type="text/javascript" src="js/webwidget_vertical_menu.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<script type="text/javascript" src="js/jquery.colorbox.js"></script>

<!--<link href="../admin_css/tcal.css"rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../adminjs/tcal.js"></script>-->

<style>
text_input{ padding-bottom:30px;
 margin-top:3px;

}
</style>

<script language="javascript" type="text/javascript">
$(document).ready(function(){
	<?php if($objCurPage->task==1) {?>
	  displayGridItem();
	<?php } ?>
	
	return false;
});
</script>

<script language="javascript" type="text/javascript">

function displayGridItem()
{
	
	//add_item 1
	$('.addVidButton').click(function() {
		//alert("sss");
		if($('.vidItem').size() >= 3){
			$(".addVidButton").hide();
		}
		var oval, otxt;
		$('#vidContainer').append('<div class="vidItem" style=" margin-bottom:10px;">' + $(".vidItemSample").html() + '</div>');	
			
			//this code is to set focus on first textbox	
			//========================================================	
			var new_div = $('#vidContainer .vidItem').last();
			//===rrt(new_div.size());
			if(new_div.size() > 0){
				
				/*new_div.find("input").each(function(n) {
					if(n == 0){
						$(this).focus();
					}
				});*/
				
				new_div.find("input.designation[type='text']").each(function() {
					$(this).attr('name', 'designation_' + $('#vidContainer .vidItem').size() );
					$(this).attr('idx', $('#vidContainer .vidItem').size() );
				});
				new_div.find("input.from[type='text']").each(function() {
					$(this).attr('name', 'from_' + $('#vidContainer .vidItem').size() );
					$(this).attr('idx', $('#vidContainer .vidItem').size() );
				});
				new_div.find("input.to[type='text']").each(function() {
					$(this).attr('name', 'to_' + $('#vidContainer .vidItem').size() );
					$(this).attr('idx', $('#vidContainer .vidItem').size() );
				});
				
				//this to find all a tag element
				new_div.find("a").each(function() {
				//alert("add more clicked");
					$(this).click(function(){	
					//alert("delete link was clicked");				
						if($('#vidContainer .vidItem').size() > 1){		
						
							$(".addVidButton").show();
							var objVidItm = $(this);
							//var curIdx = $($(this).parent().parent().find("input[type='file']")[0]).attr('idx');
							//curIdx = parseInt(curIdx);
							//alert(curIdx); 
							//alert($($(this).parent().parent().find("input[type='file']")[0]).attr('idx'));
							
							var curnewyIdx = $($(this).parent().parent().parent().find("input.designation[type='text']")[0]).attr('idx');
							curnewyIdx = parseInt(curnewyIdx);
							
							/*var curnewfrmIdx = $($(this).parent().parent().parent().find("input.from[type='text']")[0]).attr('idx');
							curnewfrmIdx = parseInt(curnewfrmIdx);*/
							
							$(this).parent().parent().parent().parent().parent().siblings(".vidItem").each(function(){
							//console.log($(this));
								cnewy = $(this).find("input.designation[type='text']")[0];
								newIdxy = $(cnewy).attr('idx');
								newIdxy = parseInt(newIdxy);
								
								cnewfrm = $(this).find("input.from[type='text']")[0];
								cnewto = $(this).find("input.to[type='text']")[0];
								
								if(newIdxy > curnewyIdx) {
									$(cnewy).attr('name', 'designation_' + (newIdxy-1) );
									$(cnewy).attr('idx', (newIdxy-1) );
									
									$(cnewfrm).attr('name', 'from_' + (newIdxy-1) );
									$(cnewfrm).attr('idx', (newIdxy-1) );
									
									$(cnewto).attr('name', 'to_' + (newIdxy-1) );
									$(cnewto).attr('idx', (newIdxy-1) );
								}
								
								/*cnewfrm = $(this).find("input.from[type='text']")[0];
								newIdxfrm = $(cnewfrm).attr('idx');
								newIdxfrm = parseInt(newIdxfrm);
								
								if(newIdxfrm > curnewfrmIdx) {
									$(cnewfrm).attr('name', 'from_' + (newIdxfrm-1) );
									$(cnewfrm).attr('idx', (newIdxfrm-1) );
								}*/
								
							});
										
							$(objVidItm).parent().parent().parent().parent().parent().remove();
							
						}else{
							$(this).parent().parent().find("input:text").each(function(){
								$(this).val("");
							});
						}
	
					});
				});	
			}
			//call to initialize tcal after clickind add more
			//f_tcalInit();	
				
	});
	
	$(".vidItem").find("a").each(function() {

		$(this).click(function(){			
			if($('#vidContainer .vidItem').size() > 1){					
				$(this).parent().parent().parent().parent().parent().remove();
			}else{
				$(this).parent().parent().find("input:text").each(function(){
					$(this).val("");
				});
			}
		});
	});
	
	
	<?php
	  if( $objCurPage->task == 1){
		 if(count($objCurPage->organisation_arr) == 0 ) { ?>
			$('.addVidButton').trigger("click");  //this event trigger the click event of addVidButton
		                                       // for the first time form is loaded
	<?php }
	} ?>
}


</script>

</head>

<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:520px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-9 maincontent" style="border:1px solid #ccc;">
    <!--<p>Date: <input type="text" id="datepicker"></p>-->
        <!--<div id="show_error">< ?php CommonFunctions::displayErrMsg(); ?></div>
        <div id="showError" style="text-align:center; color:#F00;"></div>-->
        <form name="frmList" method="post" id="frmList" action="alumni_details.php"  enctype="multipart/form-data">
        
        <table width="100%" height="438" class="alumni-table">
        <tr><td width="100%" height="20" class="pageHeader"><h1>
              <?php
                    if($objCurPage->task == 1)
                        {echo("Add ");}
                    else
                        {echo("Edit ");}
                    ?>
                Alumni Member</h1></td>
            </tr>
                <tr>
                  <td width="25%" colspan="4" >
                    <!--<div align="center" id="err">
                    < ?php CommonFunctions::displayErrMsg(); ?>
                    </div>-->
                    <div id="show_error" align="center"><?php CommonFunctions::displayErrMsg(); ?></div>
                    </td>
                  </tr>
                <tr>
                  <td><table width="100%" height="375" border="0" align="left" cellpadding="4" cellspacing="0" class="dialogBaseShadow">
                    
                    <!--<tr>
                      <td  colspan="5" valign="top" align="left" class="bodytxt">&nbsp;</td>
                    </tr>-->
                    <tr>
                      <td align="left" class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="5"><b>Login Info</b></td>
                    </tr>
                   
                    <tr>
                      <td align="left" class="bodytxt" width="143">Email:<font color="#FF0000"> *</font></td>
                      <td colspan="3" align="left" class="bodytxt" style="font-weight:bold;font-size:12px;"><input name="email" type="text" id="email" class="form-control" value="<?php echo ($objCurPage->email); ?>" style="width: 40%;"/>  </td>
                    </tr>
                    <?php if ($objCurPage->task==1) { ?>
                    <tr>
                      <td align="left" class="bodytxt" width="143">Password:<font color="#FF0000">*</font></td>
                      <td height="28" align="left"><input type="password" name="password" class="form-control" maxlength="50" value="<?php echo ($objCurPage->password); ?>" />                          </td>
                      <td align="right" width="122" >Confirm Password: <font color="#FF0000">*</font> </td>
                      <td width="283" align="left" ><input type="password" name="alumni_cpwd" class="form-control" maxlength="50" value="<?php echo ($objCurPage->alumni_cpwd); ?>" />                          </td>
                    </tr>
                    <?php } ?>
                    <tr>
                      <td class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="5"><b>Registration Info</b></td>
                    </tr>
    <td align="left" class="bodytxt">Registration No: </td>
    <td height="29" align="left" colspan="3"><span class="bodytxt" style="text-align:left">
    <input type="text" size="20" name="reg_no" class="form-control" maxlength="60" value="<?php echo ($objCurPage->reg_no); ?>" style="width: 40%;"/>
    </span></td>
    <tr> </tr>
    <tr>
    <td align="left" class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="5"><b>Personal Info</b></td>
    </tr>
    <tr>
    <td align="left" valign="top">&nbsp;</td>
    <td align="left" valign="top">&nbsp;</td>
    <td colspan="2" align="left">&nbsp;</td>
    <td width="2" align="left">&nbsp;</td>
    </tr>
    <tr>
    <td align="left" valign="top" class="bodytxt" height="25">Full Name:</td>
    <td  align="left" valign="top" class="bodytxt" style="font-weight:bold" colspan="4">
    <select name="prefix" class="form-control"  tabindex="9" style="width:80px;" >
    <option value="">Select</option>
    <option value="Dr." <?php if($objCurPage->prefix=="Dr.") { echo "selected";} ?>>Dr.</option>
    <option value="Mr." <?php if($objCurPage->prefix=="Mr.") { echo "selected";} ?>>Mr.</option>
    <option value="Miss" <?php if($objCurPage->prefix=="Miss") { echo "selected";} ?>>Miss</option>
    <option value="Mrs." <?php if($objCurPage->prefix=="Mrs.") { echo "selected";} ?>>Mrs.</option>
    </select>
    &nbsp;&nbsp;
    <input name="first_name" class="form-control" type="text" id="first_name" value="<?php echo ($objCurPage->first_name); ?>" style="width: 25%;"/>
    &nbsp;&nbsp;&nbsp;
    <input name="middle_name" class="form-control" type="text" id="middle_name" value="<?php echo ($objCurPage->middle_name); ?>" style="width: 25%;"/>
    &nbsp;&nbsp;&nbsp;
    <input name="last_name" class="form-control" type="text" id="last_name" value="<?php echo ($objCurPage->last_name); ?>" style="width: 25%;"/>    </td>
    </tr>
    <tr>
    <td>&nbsp;</td>
    <td colspan="3"><font color="#FF0000">prefix------------ first name* ------------------ middle name------------------ last name* ----------------</font></td>
    </tr>
    <tr>
    <td  align="left" valign="top" class="bodytxt" height="30" style="padding-top:5px;" width="143">Gender:<font color="#FF0000"> *</font></td>
    <td align="left" valign="top" class="bodytxt" width="279"><input name="gender" type="radio" value="M" class="txtbox"<?php if($objCurPage->gender == "M"){ echo "checked"; } ?> />
    Male
    <input name="gender" type="radio" value="F" class="txtbox"<?php if($objCurPage->gender == "F"){ echo "checked"; } ?> />
    Female </td>
    </tr>
    <tr>
    <td  align="left" valign="top" class="bodytxt" width="143" style="text-align:left">Date of Birth:<font color="#FF0000">*</font></td>
    <td  align="left" valign="top" class="bodytxt" style="text-align:left" colspan="3">
    <select name="d_day" class="form-control" style="width: 20%;">
    <option value="">Date</option>
    <?php for($i=1; $i<=31; $i++){?>
    <option value="<?php echo $i;?>"<? if($objCurPage->d_day==$i) { echo "selected";} ?>>
    <?php echo $i;?>
    </option>
    <?php } ?>
    </select>
    <select name="d_mon" class="form-control" tabindex="10" style="width:20%;">
      <option value="">Month</option>
      <?php for($i=0; $i<=count($objCurPage->monthNames)-1; $i++){?>
      <option value="<?php echo $i+1; ?>" <?php if($objCurPage->d_mon==$i+1) { echo "selected";} ?>>
      <?php echo $objCurPage->monthNames[$i];?>
      </option>
      <?php } ?>
    </select>
    <select name="d_year" class="form-control" tabindex="11" style="width:20%;">
      <option value="">Year</option>
      <?php for($i=1950; $i<=date("Y"); $i++){?>
      <option value="<?php echo $i;?>" <?php if($objCurPage->d_year==$i) { echo "selected";} ?>>
      <?php echo $i; ?>
      </option>
      <?php } ?>
    </select>    </td>
    </tr>
    <tr>
    <td align="left" valign="top" class="bodytxt" style="text-align:left">Marriage Anniversary: </td>
    <td  align="left" valign="top" class="bodytxt" style="text-align:left" colspan="3">
    <select name="anniversary_day" tabindex="9" class="form-control" style="width: 20%;">
    <option value="0">Date</option>
    <?php for($i=1; $i<=31; $i++){?>
    <option value="<?php echo $i; ?>" <?php if($objCurPage->anniversary_day==$i) { echo "selected";} ?>>
    <?php echo $i; ?>
    </option>
    <?php } ?>
    </select>
    <select name="anniversary_month" class="form-control" tabindex="10"  style="width: 20%;">
      <option value="0">Month</option>
       <?php for($i=0; $i<=count($objCurPage->monthNames)-1; $i++){?>
      <option value="<?php echo $i+1; ?>" <?php if($objCurPage->anniversary_month==$i+1) { echo "selected";} ?>>
      <?php echo $objCurPage->monthNames[$i];?>
      </option>
      <?php } ?>
    </select>    </td>
    </tr>
    <tr>
    <td align="left" valign="middle" class="bodytxt" width="143" style="text-align:left">About Family: </td>
    <td align="left" valign="top" class="bodytxt" style="text-align:left" colspan="3"><textarea name="family_info" class="form-control" cols="22" rows="4" style="width:40%;"><?php echo ($objCurPage->family_info); ?></textarea></td>
    </tr>
    <tr>
    <td colspan="4">&nbsp;</td>
    </tr>
    
    <tr>
    <td align="left" class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="4"><b>Education & Work Info</b></td>
    </tr>
    
    <tr>
    <td align="left" class="bodytxt">Stream:<font color="#FF0000"> *</font></td>
    <td align="left">
    <select name="dept" class="form-control"  tabindex="9" >
    <option value="">--Select--</option>
    <option value="Chemical Engineering" <?php if($objCurPage->dept == "Chemical Engineering") { echo "selected";} ?> >Chemical Engineering</option>
    <option value="Civil Engineering" <?php if($objCurPage->dept == "Civil Engineering") { echo "selected";} ?>>Civil Engineering</option>
    <option value="Electrical Engineering" <?php if($objCurPage->dept == "Electrical Engineering") { echo "selected";} ?>>Electrical Engineering</option>
    <option value="Environmental Science and Engineering" <?php if($objCurPage->dept == "Environmental Science and Engineering") { echo "selected";} ?>>Environmental Science & Engineering</option>
    <option value="Industrial Power Control and Drives" <?php if($objCurPage->dept == "Industrial Power Control and Drives") { echo "selected";} ?>>Industrial Power Control & Drives</option>
    <option value="Master in Computer Science and Application" <?php if($objCurPage->dept == "Master in Computer Science and Application") { echo "selected";} ?>>Master in Computer Science & Application</option>
    <option value="Mechanical Engineering" <?php if($objCurPage->dept == "Mechanical Engineering") { echo "selected";} ?>>Mechanical Engineering</option>
    <option value="Metallurgical and Materials Engineering" <?php if($objCurPage->dept == "Metallurgical and Materials Engineering") { echo "selected";} ?>>Metallurgical & Materials Engineering</option>
    
    <option value="General Administration" <?php if($objCurPage->dept == "General Administration") { echo "selected";} ?>>General Administration</option>
    <option value="Electronics and TC Engg" <?php if($objCurPage->dept == "Electronics and TC Engg") { echo "selected";} ?>>Electronics & TC Engg</option>
    <option value="Information Technology" <?php if($objCurPage->dept == "Information Technology") { echo "selected";} ?>>Information Technology</option>
    </select>
    </td>
    <td align="right" class="bodytxt">Year of Passing:<font color="#FF0000">*</font> </td>
    <td align="left"><select name="year_of_passing" class="form-control" tabindex="11" >
    <option value="">Year</option>
    <?php for($i=date("Y"); $i>=2000; $i--){?>
    <option value="<?php echo $i; ?>" <?php if($objCurPage->year_of_passing==$i) { echo "selected";} ?>>
    <?php echo $i; ?>
    </option>
    <?php } ?>
    </select>    </td>
    </tr>
    
    <tr>
    <!--<td align="left" class="bodytxt">Degree:<font color="#FF0000"> *</font></td>
    <td align="left"><select name="stream" class="form-control"  tabindex="9" >
    <option value="">Select your Program</option>
    <option value="B.E/B.Tech" < ? if($objCurPage->stream == "B.E/B.Tech") { echo "selected";} ?>>B.E / B.Tech</option>
    <option value="M.E/M.Tech" < ? if($objCurPage->stream == "M.E/M.Tech") { echo "selected";} ?>>M.E / M.Tech</option>
    <option value="MCA" < ? if($objCurPage->stream == "MCA") { echo "selected";} ?>>MCA</option>
    <option value="MBA" < ? if($objCurPage->stream == "MBA") { echo "selected";} ?>>MBA</option>
    </select></td>-->
    
    </tr>
    <tr>
    <td align="left" class="bodytxt">Additional Qualification:</td>
    <td align="left" valign="top"><textarea name="qualification" class="form-control" cols="22" rows="4">
    <?php echo ($objCurPage->qualification); ?> </textarea>    </td>
    <td align="right" class="bodytxt">Professional Membership:</td>
    <td align="left" valign="top"><textarea name="membership" class="form-control" cols="22" rows="4" >
    <?php echo ($objCurPage->membership); ?></textarea>    </td>
    </tr>
    <tr>
    <td align="left" class="bodytxt">Hobby:</td>
    <td align="left"><input name="expertise" class="form-control" type="text" id="expertise" value="<?php echo ($objCurPage->expertise); ?>" /></td>
    <td align="right" valign="top" class="bodytxt">&nbsp;</td>
    <td align="left">&nbsp;</td>
    </tr>
    <tr>
    </tr>
    
    <tr>
    <td colspan="4">&nbsp;</td>
    </tr>
    
    <tr>
    <td align="left" class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="5"><b>Career Growth</b></td>
    </tr>
    
    <tr>
    <td colspan="4">&nbsp;</td>
    </tr>
    
    <!--<tr>
    <td  align="left" valign="top" class="bodytxt" style="font-weight:bold" colspan="5">
    <input name="first_name" class="form-control" type="text" id="first_name" value="< ?php echo ($objCurPage->first_name); ?>" placeholder="Organisation" style="width: 30%;"/>
    &nbsp;&nbsp;
    <input name="first_name" class="form-control" type="text" id="first_name" value="< ?php echo ($objCurPage->first_name); ?>" placeholder="Designation" style="width: 30%;"/>
    &nbsp;&nbsp;&nbsp;
    <input name="middle_name" class="form-control" type="text" id="middle_name" value="< ?php echo ($objCurPage->middle_name); ?>" placeholder="From" style="width: 16%;"/>
    &nbsp;&nbsp;&nbsp;
    <input name="last_name" class="form-control" type="text" id="last_name" value="< ?php echo ($objCurPage->last_name); ?>" placeholder="To" style="width: 16%;"/>    </td>
    </tr>-->
    <tr>
    <td  align="left" valign="top" class="bodytxt" style="font-weight:bold" colspan="5">
    <?php if($objCurPage->task == 1)
     {?>
      <div id="vidContainer">
        <div>
                <table width="100%" border="0" cellpadding="2" cellspacing="1">
                    <tr valign="top" class="trHeader1"> 
                      <td width="32%" height="15">Organisation</td>
                      <td width="32%" align="left">Designation</td>
                      <td width="17%" align="left">From</td>
                      <td width="13%" align="left">To</td>
                      <td width="2%">&nbsp;</td>
                      <td width="18%" align="right"><div id="qvar_addmore" align="center"><a title="Add" class="addVidButton" style="cursor:pointer;"><img height="16" align="absmiddle" width="16" src="images/b_add.png" border="0" /> </a>&nbsp;&nbsp;</div></td>
                  </tr>
                </table>
            </div>
             <div class="vidItemSample" style="display:none; margin-bottom:10px;">
                <table width="100%" border="0" cellpadding="0" cellspacing="1">
                    <tr valign="top"> 
                      <td width="30%">
                        <input type="text" name="organisation[]" value="" maxlength="100" class="form-control organisation" />
                      </td>
                      
                      <td width="2%">&nbsp;</td>
                      <td width="30%">
                        <input type="text" name="designation[]" value="" maxlength="100" class="form-control designation" />
                      </td>
                      
                      <td width="2%">&nbsp;</td>
                      <td width="15%">
                        <input type="text" name="from[]" value="" maxlength="100" class="form-control from" />
                      </td>
                      
                      <td width="2%">&nbsp;</td>
                      <td width="15%">
                        <input type="text" name="to[]" value="" maxlength="100" class="form-control to" />
                      </td>
                      <td width="2%">&nbsp;</td>
                      <td width="12%" align="right">
                      <a title="Remove" class="delVidButton"><img height="16" align="absmiddle" width="16" src="images/b_drop.png" border="0" /></a></td>
                  </tr>
                </table>
            </div>
        </div>
        
         <?php	
			}
			else
			{?>
			   <!--<div>
                <table width="100%" border="0" cellpadding="2" cellspacing="1">
                    <tr valign="top" class="trHeader1"> 
                      <td width="32%" height="15">Organisation</td>
                      <td width="32%" align="left">Designation</td>
                      <td width="17%" align="left">From</td>
                      <td width="13%" align="left">To</td>
                  </tr>
                </table>
               </div>
            
                 <table width="100%" border="0" cellpadding="0" cellspacing="1">
                    <tr valign="top"> 
                      <td width="34%">
                        <input type="text" name="organisation_edit" value="< ?php echo $objCurPage->cat_name; ?> " class="form-control" />
                      </td>
                      
                      <td width="2%">&nbsp;</td>
                      <td width="34%">
                        <input type="text" name="designation_edit" value="< ?php echo $objCurPage->cat_name; ?> " class="form-control" />
                      </td>
                      
                      <td width="2%">&nbsp;</td>
                      <td width="34%">
                        <input type="text" name="from_edit" value="< ?php echo $objCurPage->cat_name; ?> " class="form-control" />
                      </td>
                      
                      <td width="2%">&nbsp;</td>
                      <td width="34%">
                        <input type="text" name="to_edit" value="< ?php echo $objCurPage->cat_name; ?> " class="form-control" />
                      </td>
                     
                  </tr>
                </table>-->
                
                
                
                
                <div>
					<?php
                                $tmpSql = "SELECT * FROM tbl_alumni_career WHERE alumni_id = " . $objCurPage->alumni_id  . " ORDER BY career_id ASC";	
								//echo $tmpSql;die('aaa');
                                 
                                 $rs = $objCurPage->link_id->query($tmpSql);
                        
                                if( (!$rs) || (!($rec = $rs->fetch_array())) )
                                {
                                   
                                }
                                else
                                {?>
									<div>
									<table width="100%" border="0" cellpadding="2" cellspacing="1">
										<tr valign="top" class="trHeader1"> 
										  <td width="32%" height="15">Organisation</td>
										  <td width="32%" align="left">Designation</td>
										  <td width="20%" align="left">From</td>
										  <td width="13%" align="left">To</td>
									  </tr>
									</table>
								   </div>
                                   
                                  <?php  do
                                    { 
                                    ?>
                                     <table width="100%" border="0" cellpadding="0" cellspacing="1">
                                      <tbody id="gallery_img_body">
                                        <tr id="rowId_<?php echo $rec["career_id"];?>">
                                          <td width="32%" class="frm_tdbdr">
                                          <input type="text" name="organisation_<?php echo $rec["career_id"]; ?>" value="<?php echo $rec["career_organisation"]; ?>" class="form-control" style="width:90%;"/>
                                          </td>
                                          <td width="32%" class="frm_tdbdr">
                                          <input type="text" name="designation_<?php echo $rec["career_id"]; ?>" value="<?php echo $rec["career_designation"]; ?>" class="form-control" style="width:90%;"/>
                                          </td>
                                          <td width="20%" class="frm_tdbdr">
                                          <input type="text" name="from_<?php echo $rec["career_id"]; ?>" value="<?php echo $rec["career_from"]; ?>" class="form-control" style="width:90%;"/>
                                          </td>
                                          <td width="13%" align="left" class="frm_tdbdr">&nbsp;
                                            <input type="text" name="to_<?php echo $rec["career_id"]; ?>" value="<?php echo $rec["career_to"]; ?>" class="form-control" style="width:90%;"/>
                                            
                                            <!--<input type="hidden" id="alumni_id" name="alumni_id" value="< ?php echo $objCurPage->alumni_id; ?>" />
                                            <input type="hidden" id="career_id" name="career_id" value="< ?php echo $rec["career_id"]; ?>" />
                                            <input type="hidden" name="task" id="task" value="< ?php echo $objCurPage->task; ?>" />-->
                                            </td>
                                          
                                        </tr>
                                      </tbody>
                                    </table>
                            <?php 	
                                           }while($rec = $rs->fetch_array());
                                        }                                               
                                        ?>
                            <!--<input type="hidden" id="cat_idx" name="cat_idx" value="" />-->
                          </div>
				
		   <?php }
		?>
        
    </td>
    </tr>                        
                           
    
    <tr>
    <td align="left" valign="top" class="bodytxt">&nbsp;</td>
    <td align="left">&nbsp;</td>
    <td align="right" valign="top" class="bodytxt">&nbsp;</td>
    <td align="left">&nbsp;</td>
    </tr>
    
    
    
    <tr>
    <td align="left" class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="5"><b>Contact Info</b></td>
    </tr>
    <tr>
    <td align="left" valign="top" class="bodytxt">Contact Address:</td>
    <td align="left" valign="top"><input name="street" class="form-control" type="text" id="street" value="<?php echo ($objCurPage->street); ?>" />
    <font color="#FF0000"> street*</font> <br /><br />
    <input name="city" class="form-control" type="text" id="city" value="<?php echo ($objCurPage->city); ?>" /><br />
    <font color="#FF0000"> city*</font> <br /><br />
    <input name="state" class="form-control" type="text" id="state" value="<?php echo ($objCurPage->state); ?>" /><br />
    <font color="#FF0000"> state*</font> <br /><br />
    <input name="country" class="form-control" type="text" id="country" value="<?php echo ($objCurPage->country); ?>" /><br />
    <font color="#FF0000"> country*</font></td>
    <td align="right" valign="top">Permanent Address:<font color="#FF0000">*</font></td>
    <td valign="top"><textarea name="per_address" class="form-control" cols="22" rows="4" >
    <?php echo ($objCurPage->per_address); ?></textarea></td>
    </tr>
    
    <tr>
    <td align="left" valign="top" class="bodytxt">Phone (O):</td>
    <td align="left" valign="top"><input name="office_phone" class="form-control" type="text" id="office_phone" value="<?php echo ($objCurPage->office_phone); ?>" />    </td>
    <td align="right" valign="top" class="bodytxt">Phone (R):</td>
    <td align="left" valign="top"><input name="res_phone" class="form-control" type="text" id="res_phone" value="<?php echo ($objCurPage->res_phone); ?>" />    </td>
    </tr>
    <tr>
    <td align="left" valign="top" class="bodytxt">Mobile:</td>
    <td align="left" valign="top"><input name="mobile_phone" class="form-control" type="text" id="mobile_phone" value="<?php echo ($objCurPage->mobile_phone); ?>" />    </td>
    <td align="right" valign="top" class="bodytxt">Fax:</td>
    <td align="left" valign="top"><input name="fax" class="form-control" type="text" id="fax" value="<?php echo ($objCurPage->fax); ?>" />    </td>
    </tr>
    <tr>
    <td align="left" valign="top" class="bodytxt">WebPage URL:</td>
    <td align="left" valign="top"><input name="link_url" class="form-control" type="text" id="link_url" value="<?php echo ($objCurPage->link_url); ?>" /></td>
    <td align="right" valign="top" class="bodytxt">&nbsp;</td>
    <td align="left" valign="top">&nbsp;</td>
    </tr>
    <tr>
    <td align="left" valign="top" class="bodytxt">&nbsp;</td>
    <td align="left" valign="top">&nbsp;</td>
    <td align="right" valign="top" class="bodytxt">&nbsp;</td>
    <td align="left" valign="top">&nbsp;</td>
    </tr>
    
    <tr>
    <td align="left" class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="5"><b>How can I help the Institute/Alumni Affairs</b></td>
    </tr>
    
    <tr>
    <td align="middle" colspan="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<textarea name="institute_alumni" class="form-control" cols="22" rows="4" style="width: 79%;"><?php echo ($objCurPage->institute_alumni); ?> </textarea></td>
    </tr>
    <tr>
    <td colspan="4" class="btnHolder" align="center">&nbsp;</td>
    </tr>
    <tr>
    <td align="left" class="tblHead_almuni" style="background-color:#ccc;color:#000000;" colspan="5"><b>Upload Alumni Photo</b></td>
    </tr>
    <tr>
    <td align="middle" colspan="2">
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="alumni_photo" type="file"  id="alumni_photo" size="23" class="txtbox" value="<?php echo ($objCurPage->alumni_photo); ?>" tabindex="7" />
    <br />
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    <?php  if($objCurPage->alumni_photo != ""){ ?>
    <b><a href="../alumni_photo/<?php echo $objCurPage->alumni_photo; ?>" target="_blank"> <font color="#990000"><small>View</small></font></a></b>
    <?php } ?>
    
    &nbsp;&nbsp;&nbsp;&nbsp;<font color="#003399"><!--<small>(File type: gif,jpg,jpeg,png,w:120,h:153)</small>--><small>(File type: gif,jpg,jpeg,png,w:200,h:200)</small></font>
    <input name="alumni_photo_edit" type="hidden" id="alumni_photo_edit" value="<?php echo($objCurPage->alumni_photo); ?>" /></td>
    </tr>
    <tr>
    <td colspan="4">&nbsp;</td>
    </tr>
    <!--<tr>
        <td width="37%" valign="top">&nbsp; Show Alumni : </td>
        <td width="63%" align="left" valign="top"><input type="radio" name="mark_new" id="mark_new" value="1" < ?php if($objCurPage->mark_new=="1"){echo"checked";}?> /> YES &nbsp;&nbsp;<input type="radio" name="mark_new" id="mark_new" value="0" < ?php if($objCurPage->mark_new=="0"){echo"checked";}?> /> NO </td>
    </tr> -->
    
     <!--<tr>
      <td align="left" class="bodytxt" width="153">Show Alumni in page:</td>
      <td colspan="3" align="left" class="bodytxt" style="font-weight:bold;font-size:12px;"><input type="radio" name="mark_new" id="mark_new" value="1" < ?php if($objCurPage->mark_new=="1"){echo"checked";}?> /> YES &nbsp;&nbsp;<input type="radio" name="mark_new" id="mark_new" value="0" < ?php if($objCurPage->mark_new=="0"){echo"checked";}?> /> NO </td>
    </tr>-->
                    
    <tr>
    <td colspan="4">&nbsp;</td>
    </tr>
    <tr>
    <td colspan="4" class="btnHolder" align="center">&nbsp;
    <input name="cmdSave" type="submit" class="btn btn-default" id="cmdSave" value="Save" />
    <!--&nbsp;
    <input name="cmdRevert" type="button" class="btn btn-default" id="cmdRevert" value="Revert"  
                onclick="window.location.href='< ?php echo($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec . "&alumni_id=" . $objCurPage->alumni_id . "&task=" . $objCurPage->task); ?>'" />-->
    &nbsp;
    <input name="cmdExit" type="button" class="btn btn-default" id="cmdExit" value="Exit"  onclick="window.location.href='<?php echo($objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&crec=" . $objCurPage->crec); ?>'" />
    <input name="alumni_id" type="hidden" id="alumni_id" value="<?php echo $objCurPage->alumni_id ?>" />
    <input name="task" type="hidden" id="task" value="<?php echo $objCurPage->task ?>" />
    <input name="sortby" type="hidden" id="sortby" value="<?php echo $objCurPage->sortby ?>" />
    <input name="sortmode" type="hidden" id="sortmode" value="<?php echo $objCurPage->sortmode ?>" />
    <input name="postMe" type="hidden" id="postMe" value="Y" />    </td>
    </tr>
    <tr>
    <td width="143" class="bodytxt"><font color="#FF0000">(*) mandatory fields</font></td>
    <td align="left" class="a_text2Bold" colspan="3">&nbsp;</td>
    </tr>
                  </table></td>
                </tr>
                <tr>
                  <td></td>
                  </tr>
              </table>
        </form>

     </div>
  </div>
</section>
<?php include_once('footer.php');?>
</body>
</html>