tinymce.PluginManager.add("insertdatetime",function(e){function t(t,n){function i(e,t){if(e=""+e,e.length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e}return n=n||new Date,t=t.replace("%D","%m/%d/%Y"),t=t.replace("%r","%I:%M:%S %p"),t=t.replace("%Y",""+n.getFullYear()),t=t.replace("%y",""+n.getYear()),t=t.replace("%m",i(n.getMonth()+1,2)),t=t.replace("%d",i(n.getDate(),2)),t=t.replace("%H",""+i(n.getHours(),2)),t=t.replace("%M",""+i(n.getMinutes(),2)),t=t.replace("%S",""+i(n.getSeconds(),2)),t=t.replace("%I",""+((n.getHours()+11)%12+1)),t=t.replace("%p",""+(n.getHours()<12?"AM":"PM")),t=t.replace("%B",""+e.translate(l[n.getMonth()])),t=t.replace("%b",""+e.translate(o[n.getMonth()])),t=t.replace("%A",""+e.translate(r[n.getDay()])),t=t.replace("%a",""+e.translate(a[n.getDay()])),t=t.replace("%%","%")}function n(n){var i=t(n);if(e.settings.insertdatetime_element){var a;a=/%[HMSIp]/.test(n)?t("%Y-%m-%dT%H:%M"):t("%Y-%m-%d"),i='<time datetime="'+a+'">'+i+"</time>";var r=e.dom.getParent(e.selection.getStart(),"time");if(r)return e.dom.setOuterHTML(r,i),void 0}e.insertContent(i)}var i,a="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),r="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),o="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),l="January February March April May June July August September October November December".split(" "),s=[];e.addCommand("mceInsertDate",function(){n(e.getParam("insertdate_dateformat",e.translate("%Y-%m-%d")))}),e.addCommand("mceInsertTime",function(){n(e.getParam("insertdate_timeformat",e.translate("%H:%M:%S")))}),e.addButton("inserttime",{type:"splitbutton",title:"Insert time",onclick:function(){n(i||"%H:%M:%S")},menu:s}),tinymce.each(e.settings.insertdate_formats||["%H:%M:%S","%Y-%m-%d","%I:%M:%S %p","%D"],function(e){s.push({text:t(e),onclick:function(){i=e,n(e)}})}),e.addMenuItem("insertdatetime",{icon:"date",text:"Insert date/time",menu:s,context:"insert"})});