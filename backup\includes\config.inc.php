<?php
error_reporting(E_ALL ^ E_DEPRECATED);

$MYSQL_ERRNO='';
$MYSQL_ERROR='';

$config["SITE_ROOT"] = "http://ugierkl.ac.in/";
$config["APP_FOLDER"] = "";

$config["REMOVE_PREFIX_PATTERN"] = "/^/";

$config["SUPER_ADMIN"] = "administrator";
$config["ADMIN_EMAIL"] = "<EMAIL>";

$config["DB_HOST"] = "localhost";
$config["DB_NAME"] = "ugierkl_db2018";
$config["DB_USER_ID"] = "ugierkl_usr2018";
$config["DB_PASSWORD"] = "ugierklpwd";

$config["APP_TITLE"] = "Utkalmani Gopabandhu Institute Of Engineering ";

$config["APP_TITLE_ADMIN"] = "Utkalmani Gopabandhu Institute Of Engineering - Admin";

$config["SUPPORT_TEAM"] = "";

$config["PAY_MODE"] = "0";

$config["SITE_KEY"] = "6LcsY30rAAAAABHHm3HUZh477EADDPic4p8Zn-Zk";
$config["SECRET_KEY"] = "6LcsY30rAAAAAJyIcjKIS4LM6z6xEPc4eZN-38V9";

define('DB_HOST', 'localhost');
define('DB_USER', 'ugierkl_usr2018');
define('DB_PASS', 'ugierklpwd');
define('DB_NAME', 'ugierkl_db2018');

// Create DB connection
global $db;
$db = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// Check connection
if (!$db) {
    die('Database connection failed: ' . mysqli_connect_error());
}
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
     require_once __DIR__ . '/../formwatchdog.php';
}
?>