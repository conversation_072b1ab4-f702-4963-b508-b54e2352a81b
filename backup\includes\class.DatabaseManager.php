<?php
class DatabaseManager
{

	//----------------------------------------------------------------
	
	//--- Connects to the specified database and returns the Database Handle ---
	public static function iconnectDB($dbName = "", $newLink = false)
	{
		global $config;
		
		global $MYSQL_ERRNO, $MYSQL_ERROR;
		global $obj_mysqli;
		
		if($dbName == "")
		{
			$dbName = $config["DB_NAME"];
		}
		
		/*$obj_mysqli = mysqli_init();
		$obj_mysqli->real_connect($config["DB_HOST"], $config["DB_USER_ID"], $config["DB_PASSWORD"]);*/
				
		$obj_mysqli = new mysqli($config["DB_HOST"], $config["DB_USER_ID"], $config["DB_PASSWORD"]);
		
		//$obj_mysqli = mysqli($config["DB_HOST"], $config["DB_USER_ID"], $config["DB_PASSWORD"]);
		//$dbLinkId=mysql_connect($config["DB_HOST"], $config["DB_USER_ID"], $config["DB_PASSWORD"], $newLink);
				
	
		if(!$obj_mysqli)
		{
			$MYSQL_ERRNO=0;
			$MYSQL_ERROR="Connection failed to the host " . $config["DB_HOST"];
			return 0;
		}
		else if(!$obj_mysqli->select_db($dbName))
		{
			$MYSQL_ERRNO = $obj_mysqli->connect_errno;
			$MYSQL_ERROR = $obj_mysqli->connect_error;
			return 0;
		}
		else
		{
			//mysql_query("SET NAMES utf8 COLLATE utf8_unicode_ci;");
			return $obj_mysqli;
		}
	}

	//--- Dis-Connects the specified database handle ---
	public static function idisconnectDB($obj_mysqli)
	{
		$obj_mysqli->close();
		unset($obj_mysqli);
	}

	//--- Creates a new database with the specified name ---
	public static function icreateDB($dbName, $dbLink)
	{
		global $MYSQL_ERRNO, $MYSQL_ERROR;
		
		//--- Deletes database if already exists
		$query = "DROP DATABASE IF EXISTS " . $dbName ;
		$result = $dbLink->query($query);
		if(!$result)
		{
			//--- Sets error values
			$MYSQL_ERRNO = $dbLink->connect_errno;
			$MYSQL_ERROR = $dbLink->connect_error;
			return 0;
		}
		else
		{
			//--- Create new Database
			$query = "CREATE DATABASE IF NOT EXISTS " . $dbLink ;
			$result = $dbLink->query($query);
			if(!$result)
			{
				//--- Sets error values
				$MYSQL_ERRNO = $dbLink->connect_errno;
				$MYSQL_ERROR = $dbLink->connect_error;
				return 0;
			}
			else
			{
				//--- Success
				return 1;
			}
		}
	}

	//--- Checks and returns error values ---
	public static function isqlError()
	{
		global $MYSQL_ERRNO, $MYSQL_ERROR;
		global $obj_mysqli;
		
		if(empty($MYSQL_ERROR))
		{
			//$MYSQL_ERRNO=mysql_errno();
			//$MYSQL_ERROR=mysql_error();			
			$MYSQL_ERRNO = $obj_mysqli->connect_errno;
			$MYSQL_ERROR = $obj_mysqli->connect_error;
		}
		return "$MYSQL_ERRNO: $MYSQL_ERROR";
	}
	
	
	/* Genrates Max Value (Int) with Condition */
	public static function igetNewId($dbLink, $tblName, $fldName, $condition)
	{
		if($condition != "")
		{
			$strCondition = " WHERE " . $condition . " ";
		}
		else
		{
			$strCondition = "";
		}
		
       
		$rs = $dbLink->query("select max(" . $fldName . ") as newid from " . $tblName . $strCondition);
		//echo "select max(" . $fldName . ") as newid from " . $tblName . $strCondition; die("hello");
		if( (!($rs)) || (!($rec=$rs->fetch_array())) )
		{
			die("couldn't generate Id.");
		}
		else
		{
			if(empty($rec["newid"]))
			{
				return 1;
			}
			else
			{
				return ($rec["newid"] + 1) ;
			}
		}
	}
	
	/*Returns data value if data exists in a table (suitable for integer or string data) */
	public static function igetDataFromTable($dbLink, $tblName, $fldName, $defaultVal = "", $optCondition = "")
	{
		if(trim($optCondition) != "")
		{
			$condition = " where " . $optCondition ;
			//return $condition;
		}
		else
		{
			$condition = "";
		}
		//echo "<br> select  $fldName  from  $tblName  $condition <br> "; //die();
		//echo"select " . $fldName . " from " . $tblName . " " . $condition . " LIMIT 1"; die();
		//return("select " . $fldName . " from " . $tblName . " " . $condition); 
		$rs = $dbLink->query("select " . $fldName . " from " . $tblName . " " . $condition );
		//return $rs;
		if( (!($rs)) || (!($rec=$rs->fetch_array())) )
		{
			//not found
			//echo  "17";
			return $defaultVal;
			/*$v=1;
			return $v;*/
		}
		else if(is_null($rec[0]))
		{
			//found
			//echo  "11";
			return $defaultVal;
			
		}
		else
		{
			//found
			//echo  $rec[0];
			return $rec[0];
		}
	}
	
	
	//get lesson plan according to the branch name
	public static function getLessonPlan($branch_id)
	{
		$result = array();
		$dbLink = DatabaseManager::iconnectDB();
		
		if(!$dbLink)
		{
			die(DatabaseManager::isqlError());
		}
		
		$tmpSql = "  SELECT * "
				 . " FROM tbl_lessonplan "
			     . " WHERE branch_id = '". $branch_id ."' ORDER BY record_id ASC";

		$rs = $dbLink->query($tmpSql);
		if( (!$rs) || (!($rec = $rs->fetch_array())) )
		{
			echo '<tr><td colspan="3">NO RECORDS FOUND...</td></tr>';
		}
		else
		{
			$slno = 1;
			do
			{	
				echo "<tr>";	
				echo "<td>".$slno.".</td>";
				echo "<td>".$rec["heading"]."</td>";	
				
				$ext = CommonFunctions::getFileExtension($rec["attachment"]);
				if($ext == "pdf"){ $icofile = "images/pdf.png";}
				else{ $icofile = "images/word.png";}
				
				echo '<td align="center">';
				echo '<a href="lessonplan_files/lessonplan_branchid-'.$rec["branch_id"]."/".$rec["attachment"].'" target="_blank"><img src="'.$icofile.'" alt="" border="0" title=""/></a>';
				echo '</td>';
				echo "</tr>";
				
				$slno++;
			} while ($rec = $rs->fetch_array());
		}
		
		$allLessonplan = json_encode($result);										
		return $allLessonplan;
	}
	
	
	public static function getHeadline($dbLink)
	{
		$result = array();
		
		$tmpSql = "  SELECT DISTINCT(announcement_headline) "
				 . " FROM tbl_admin_announcement "
			     . " WHERE 1 ORDER BY announcement_id ASC";

		$rs = $dbLink->query($tmpSql);
		if( (!$rs) || (!($rec = $rs->fetch_array())) )
		{
			$result[] = "No announcement available";
		}
		else
		{
			do
			{			
				$result[] = $rec['announcement_headline'];
				
			} while ($rec = $rs->fetch_array());
		}
		
		$autoCompleteCity = json_encode($result);										
		return $autoCompleteCity;
	}
	
	
	
	
	
	/*Checks whether data exists in a table*/
	public static function iexistsInTable($dbLink, $tblName, $condition = "")
	{
		if($condition != "")
		{
			$tmp_condition = " where " . $condition ;
		}
		else
		{
			$tmp_condition = "" ;
		}
		$sql = "select * from " . $tblName . $tmp_condition;
		//echo $sql;
		//die($sql);
		$rs = $dbLink->query($sql);
		//$rs = $dbLink->query(("select * from " . $tblName . $tmp_condition . " LIMIT 1"), $dbLink);
		//$rs = $dbLink->query(("SELECT TOP * FROM " . $tblName . $tmp_condition), $dbLink);
		//echo "select * from " . $tblName . $tmp_condition; echo "<br/>";//die();
		if( (!($rs)) || (!($rec=$rs->fetch_array())) )
		{
			//not found
			return 0;
		}
		else
		{
			//found
			return 1;
		}
	}
	
	//==== This function returns a field from list of record matching condition (suitable for intiger values) ====
	public static function igetFieldList($dbLink, $tblName, $fldName, $aSeparator = ",", $defaultVal = "", $encloseChar="", $optCondition = "", $optAlias = "")
	{
		if(trim($optCondition) != "")
		{
			$condition = " WHERE " . $optCondition;
		}
		else
		{
			$condition = "";
		}
		
		$fieldList = "";
	
		$tmpSql = "SELECT " . $fldName . " FROM " . $tblName . " " . $condition;
		//echo("<br>SQL=>".$tmpSql."<br>");
		$rs = $dbLink->query($tmpSql);
		if( (!($rs)) || (!($rec=$rs->fetch_array())) )
		{
			//not found, do nothing
		}
		else
		{
			do 
			{
				if($fieldList != "")
				{
					$fieldList = $fieldList . $aSeparator;
				}
				$fieldList = $fieldList . $encloseChar . $rec[0] . $encloseChar;
			} while(($rec=$rs->fetch_array()));
		}
		
		if($fieldList == "")
		{
			$fieldList = $defaultVal;
		}

		return $fieldList;
	}
	
	//==== This function returns Description Field of records in use ====
	public static function igetRecordsDescInUse($dbLink, $tblName, $fldName, $defaultVal = "", $optCondition = "")
	{
		if(trim($optCondition) != "")
		{
			$condition = " WHERE " . $optCondition;
		}
		else
		{
			$condition = "";
		}
	
		$usedFieldList = "";
	
		$tmpSql = "SELECT " . $fldName . " FROM " . $tblName . " " . $condition;
		
		$rs = $dbLink->query($tmpSql);
		if( (!($rs)) || (!($rec=$rs->fetch_array())) )
		{
			//not found, do nothing
		}
		else
		{
			do 
			{
				if($usedFieldList != "")
				{
					$usedFieldList = $usedFieldList . "<br>";
				}
				$usedFieldList = $usedFieldList . $rec[0];
			} while(($rec=$rs->fetch_array()));
		}
		return $usedFieldList;
	}
	
	//==== This function checks and returns Ids of records in use (suitable for intiger values) ====
	public static function igetRecordsInUse($dbLink, $tblName, $fldName, $fieldIdList, $defaultVal = "", $optCondition = "")
	{
		if(trim($optCondition) != "")
		{
			$condition = " and " . $optCondition;
		}
		else
		{
			$condition = "";
		}
	
		$usedFieldList = "";
		
		$arrFieldIds = split(",", $fieldIdList);
		foreach ($arrFieldIds as $itm) 
		{ 
		
			if(DatabaseManager::irecordInUse($tblName, $fldName . "=" . $itm . $condition) == 1)
			{
				if($usedFieldList != "")
				{
					$usedFieldList = $usedFieldList . ",";
				}
				$usedFieldList = $usedFieldList . $itm;
			}
		}
		
		if($usedFieldList == "")
		{
			$usedFieldList = $defaultVal;
		}
		return $usedFieldList;
	}
	
	//==== This function checks whether a record in use (checks for Foreign Key) (Returns: 1-In Use / 0-Not In Use) ====
	public static function irecordInUse($tblName, $optCondition = "")
	{
		global $DB_NAME;
		
		$condition = "";
	
		if(trim($optCondition) != "")
		{
			$condition = " WHERE " . $optCondition;
		}
		else
		{
			$condition = "";
		}
	
		//--- Create another Connection to Database ---
		$tmpdbLink = DatabaseManager::iconnectDB();
		if(!$tmpdbLink)
		{
			die(DatabaseManager::sqlError());
		}
						
		//--- Begin Transaction				
		//$tmpdbLink->autocommit(FALSE);
		$tmpdbLink->query('START TRANSACTION');
		
		$tmpSql = "DELETE FROM " . $tblName . $condition;

		$rs = $tmpdbLink->query($tmpSql);
				
		if($tmpdbLink->affected_rows == -1)
		{		
			if($tmpdbLink->connect_errno == 1217 || $tmpdbLink->connect_errno == 1451)
			{
				//--- could not be deleted			
				$tmpdbLink->rollback();
				DatabaseManager::idisconnectDB($tmpdbLink);
				return 1;
			}
			else
			{
				die("Exception occured: " . "(" . $tmpdbLink->connect_errno . ") " . $tmpdbLink->connect_errno);
			}
			
		}
		else
		{
			//--- could not be deleted
			$tmpdbLink->rollback();
			DatabaseManager::idisconnectDB($tmpdbLink);
			return 0;
		}
	}
	
	//==== Set Precedence of other records (Returns 0: Fail; 1: Success) ====
	public static function isetPrecedence($dbLink, $tblName, $fldName, $precedenceNew, $precedenceOld, $optCondition = "")
	{
		if($precedenceNew == $precedenceOld)
		{
			return 1;
		}
		
		if(trim($optCondition) != "")
		{
			$condition = $optCondition . " and ";
		}
		else
		{
			$condition = "";
		}
		
		$tmpSql = "";
		
		if($precedenceOld == 0)
		{
			//--- in case of Add
			$tmpSql = "UPDATE " . $tblName . " SET " . $fldName . " = " . $fldName . " + 1"
					. " WHERE " . $condition . " " . $fldName . " >= " . $precedenceNew;
		}		
		else if($precedenceNew == -1)
		{
			//--- in case of Delete
			$tmpSql = "UPDATE " . $tblName . " SET " . $fldName . " = " . $fldName . " - 1"
					. " WHERE " . $condition . " " . $fldName . " > " . $precedenceOld;
		}		
		else if($precedenceNew < $precedenceOld)
		{
			//--- in case of Update Type 1
			$tmpSql = "UPDATE " . $tblName . " SET " . $fldName . " = " . $fldName . " + 1"
					. " WHERE " . $condition
					. " (" . $fldName . " >= " . $precedenceNew . " and " . $fldName . " < " . $precedenceOld . ")";
		}
		else if($precedenceNew > $precedenceOld)
		{
			//--- in case of Update Type 2
			$tmpSql = "UPDATE " . $tblName . " SET " . $fldName . " = " . $fldName . " - 1"
					. " WHERE " . $condition . " " . $fldName . " > 1 and "
					. " (" . $fldName . " <= " . $precedenceNew . " and " . $fldName . " > " . $precedenceOld . ")";
		}
//die($tmpSql);
		$rs = $dbLink->query($tmpSql);
		if($dbLink->affected_rows == -1)
		{
			//error occured
			return 0;
		}
		else
		{
			//executed properly
			return 1;
		}
	}	
	
	
	//----------------------------------------------------------------
}
?>