<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	
	
	public $app_config;		// Application Configuration Settings
	
	public $id_list;		//--- Selected Id List

	public $user_name, $user_age, $user_email, $user_gen, $user_pwd, $user_con, $user_add,$hobby; 

	
	public $headline; // used for searching purpose
	public $jsonheadline;
		
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "registration";
		$this->cur_page_url = "registration.php";
		$this->list_page_url = "registration.php";
		
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();

		//--- Initialize Special Properties ---
		$this->initSpecialProperties();
		
		$this->jsonheadline=DatabaseManager::getHeadline($this->link_id);

		//--- Execute a Task ---
		switch($this->task)
		{
			case 3:
				//--- Delete Record
				$this->task = 0;
				$this->deleteResults();
				break;
			case 4:
				//--- User Status
				$this->announcementStatus();
				break;			
			default:
				$this->task = 1;
		}
		
	}

	//==== Initialize Special Properties ====
	public function initSpecialProperties()
	{
		return 1;
	}
		
	

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 1));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 1));
		}
		else
		{
			$this->task = 1;
		}
		
		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		//--- do nothing
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{
		//--- do nothing
		return 1;
	}
	
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	public function saveData()
	{
		$myerr = "";					
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}
$objCurPage = new CurrentPage();

?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]."- User Registration" ?></title>
<script type="text/javascript" src="js/jquery-1.10.1.min.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<script language="javascript" type="text/javascript">
$(document).ready(function(){
	$('#cmdSave').click(function()
	{	
	    $("#loadingImage").hide();	
		validateSave();
	});
});
function validateSave()
{
	$("#loaderImg").html('<img src="images/bar-circle.gif" />');
	$("#loaderImg").show();
	$('#frmregistration').submit();
}
$('#frmregistration').ajaxForm({	
	target: '',
	success: function(data){
		$("#loadingImage").hide();
		if(data == 1)
		{	alert("User Created");
			window.location="index.php";
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			<!--location.href='< ?php echo $objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&msg=1"; ?>';-->
			return false;					
		}
		else
		{
			//alert(data);
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			
			$('#showError').html(data);
			return false;
		}
	}			
});

function checkemail()
{
   /*var email=document.getElementById( "user_email" ).value;

   if(email)
   {
	   $.ajax({
		   type: 'post',
		   url: 'checkdata.php',
		   data: {
		   user_email:email,
		   },
		   success: function (response) {
		   $( '#email_status' ).html(response);
		   if(response=="OK")	
		   {
			  return true;	
		   }
		   else
		   {
			  return false;
			  document.getElementById("user_email").focus();
		   }
		 }
	   });
	}
	else
	{
	   $( '#email_status' ).html("");
	   return false;
	}*/
}
</script>
<link href="css/style.css" rel="stylesheet" />
</head>

<body>
<div >
<form method="post" action="adminajax/add_registration.php" name="frmregistration" id="frmregistration">
<table class="tbl_reg">
	<tr>
    	<th>Registration</th>
    </tr>
    <tr><td><div id="showError" class="showError"><span id="email_status"></span></div></td></tr>
    <tr><td style="text-align:center"><span style="color:#F00">(*)</span> fields are mandatory.</td></tr>
    
    <tr>
    	<td><input type="text" placeholder="Name *" name="user_name" id="user_name" /></td>
    </tr>
    <tr>
    	<td><input type="text" placeholder="Age *" maxlength="2" name="user_age" id="user_age" /></td>
    </tr>
    <tr>
    	<td><select name="user_gen" id="user_gen"><option>Gender</option><option selected="selected">Male</option><option>Female</option></select></td>
    </tr>
    <tr>
    	<td><input type="text" placeholder="Email *" name="user_email" id="user_email" onchange="checkemail();" /></td>
    </tr>    
    <tr>
    	<td><input type="password" placeholder="Password *" autocomplete="off" name="user_pwd" id="user_pwd" /></td>
    </tr>
    <tr>
    	<td><input type="password" placeholder="Confirm Password *" name="user_cpwd" id="user_cpwd" /></td>
    </tr>
    <tr>
    	<td><input type="text" placeholder="Contact No *" name="user_con" maxlength="10" id="user_con" /></td>
    </tr>
    <tr>
    	<td><textarea name="user_add" id="user_add" placeholder="Address"></textarea></td>
    </tr>
    <tr>
    	<td><fieldset style=" border:1px solid #257bd4"><legend> Select your hobbies *</legend>
        	<span style="float:left; color:#F00">Select atleast one hobbies</span>
            <div style="clear:both"></div>
        	<div class="hobbies">
                <div class="squaredThree">
                    <input type="checkbox" value="Painting" id="squaredThree1" name="hobby[]" />
                    <label for="squaredThree1"></label><span> Painting</span>
                </div>
                <div class="squaredThree">
                    <input type="checkbox" value="Dancing" id="squaredThree2" name="hobby[]" />
                    <label for="squaredThree2"></label><span> Dancing</span>
                </div>
                
                <div class="squaredThree">
                    <input type="checkbox" value="Singing" id="squaredThree3" name="hobby[]" />
                    <label for="squaredThree3"></label><span> Singing</span>
                </div>
                <div class="squaredThree">
                    <input type="checkbox" value="Swiming" id="squaredThree4" name="hobby[]" />
                    <label for="squaredThree4"></label><span> Swiming</span>
                </div>
            </div>
            <div class="hobbies">
                <div class="squaredThree">
                    <input type="checkbox" value="Shooting" id="squaredThree5" name="hobby[]" />
                    <label for="squaredThree5"></label><span> Shooting</span>
                </div>
                <div class="squaredThree">
                    <input type="checkbox" value="Drawing" id="squaredThree6" name="hobby[]" />
                    <label for="squaredThree6"></label><span> Drawing</span>
                </div>
                <div class="squaredThree">
                    <input type="checkbox" value="Flying" id="squaredThree7" name="hobby[]" />
                    <label for="squaredThree7"></label><span> Flying</span>
                </div>
                <div class="squaredThree">
                    <input type="checkbox" value="Photography" id="squaredThree8" name="hobby[]" />
                    <label for="squaredThree8"></label><span> Photography</span>
                </div>
            </div>
            </fieldset>
        </td>
    </tr>
    <tr>
    	<td>
        <input type="button" name="cmdSave" id="cmdSave" value="Sign Up" />
        <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />
        <input type="hidden" name="postMe" value="Y" /></td>
    </tr>
    <!--<tr>
    	<td><a href="index.php" class="buttons">Login</a></td>
    </tr>-->
</table>
</form>
</div>
</body>
</html>