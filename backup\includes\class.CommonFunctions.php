<?php
class CommonFunctions
{

	//----------------------------------------------------------------
	/*Replace Characters (such as ", ', etc.)*/
	public static function replaceChars($srcStr, $repMode)
	{
		$resStr="";
		
		if(is_null($srcStr))
		{
			$srcStr = "";
		}
			
		if($srcStr=="")
		{
			return "";
		}
		
		switch($repMode)
		{
			case 0:
				$resStr = str_replace(";", "", str_replace("<", "", str_replace(chr(34),"", str_replace(chr(39),"", trim($srcStr)))));
				break;
			case 1:
				$resStr = str_replace("<", "", str_replace(chr(34),"'", trim($srcStr)));
				break;
			case 2:
				$resStr = str_replace(chr(34),"", str_replace(chr(39),"", trim($srcStr)));
				break;
			case 3:
				$resStr = str_replace(chr(34),"'", trim($srcStr));
				break;
			case 4:
				$resStr = str_replace(chr(34),"", trim($srcStr));
				$resStr = preg_replace("[^A-Za-z]", "", $resStr);
				break;
			case 5:
				$resStr = str_replace(chr(34),"", trim($srcStr));
				$resStr = preg_replace("[^A-Za-z ]", "", $resStr);
				break;
			case 6:
				$resStr = str_replace(chr(34),"", trim($srcStr));
				$resStr = preg_replace("[^A-Za-z \.]", "", $resStr);
				break;
			case 7:
				$resStr = str_replace(chr(34),"", trim($srcStr));
				$resStr = preg_replace("[^A-Za-z0-9]", "", $resStr);
				break;
			case 8:
				$resStr = str_replace(chr(34),"", trim($srcStr));
				$resStr = preg_replace("[^A-Za-z 0-9 \.]", "", $resStr);
				break;
			case 9:
				$resStr = str_replace(chr(34),"", trim($srcStr));
				$resStr = preg_replace("[^0-9]", "", $resStr);
				break;
			case 10:
				$resStr = str_replace(chr(34),"", trim($srcStr));
				$resStr = preg_replace("[^0-9\.]", "", $resStr);
				break;
			case 11:
				$resStr = trim($srcStr);
				$resStr = strip_tags($resStr); // Remove HTML + JS
				$resStr = htmlspecialchars($resStr, ENT_QUOTES | ENT_HTML5, 'UTF-8'); // Encode entities

				// Remove SQL keywords (basic filter)
				$resStr = preg_replace('/\b(SELECT|INSERT|UPDATE|DELETE|DROP|TRUNCATE|UNION|ALTER|CREATE|RENAME|REPLACE|--|#)\b/i', '', $resStr);

				// Remove hex values like \x41 or %41
				$resStr = preg_replace('/(\\\\x[0-9A-Fa-f]{2}|%[0-9A-Fa-f]{2})/', '', $resStr);

				// Remove common XSS attack functions
				$resStr = preg_replace('/(alert\s*\(|prompt\s*\(|confirm\s*\(|on\w+=|javascript:|document\.|window\.)/i', '', $resStr);

				// Remove suspicious characters
				$resStr = preg_replace('/[<>\"\']/', '', $resStr);
				break;
			case 12:
				//$resStr = str_replace(chr(8220), chr(34), trim($srcStr));
				$resStr = htmlspecialchars(trim($srcStr), ENT_QUOTES, "UTF-8");
				break;
		}
	
		return $resStr;
	}

	/*This function Reverts back Characters and Line Break*/
	public static function replaceCharsRev($srcStr, $linBreak = 0)
	{
		$resStr="";
		
		if(is_null($srcStr))
		{
			$srcStr = "";
		}
			
		if($srcStr=="")
		{
			return "";
		}
		
		if($linBreak == 1)
		{
		//--- with line break
			$resStr = str_replace(chr(13),"<br>", trim($srcStr));
		}
		else
		{
		//--- without line break
			$resStr = trim($srcStr);
		}
	
		return $resStr;
	}
	
	/*This function  Truncate A String with Full Whole Word After The Given Length*/
	public static function truncateStringAfterWord($str, $length)
	{
		$string = preg_replace('/\s+?(\S+)?$/', '', substr($str, 0, $length));
		
		if (strlen($string) <= $length) {
		$string = $string; //do nothing
		} else {
		   $string = substr($string, 0, strpos(wordwrap($string, $length), "\n"));
		}
		return $string;
	}


	//----------------------------------------------------------------
	/*Replace Characters (such as ", ', etc.)*/
	public static function escapeString($srcStr, $escMode = 0)
	{
		$resStr="";
		
		if(is_null($srcStr))
		{
			$srcStr = "";
		}
			
		if($srcStr=="")
		{
			return "";
		}
		
		//--- Magic Quotes is ON, so Strip extra Slashes
		if(get_magic_quotes_gpc()==1)
		{
			$srcStr = stripslashes($srcStr);
		}
		
		
		//--- Escape String		
		switch($escMode)
		{
			case 0:
				$resStr = mysql_real_escape_string($srcStr);
				break;
			case 1:
				$resStr = mysql_real_escape_string($srcStr);
				break;
		}
	
		return $resStr;
	}


	/*gets intvalue*/
	public static function numVal($anInt)
	{
		if(is_numeric($anInt))
		{
			return intval($anInt,10);
		}
		else
		{
			return -1;
		}
	}

	/*This function checks and displays Application Message or Appliation Error*/
	public static function displayErrMsg($msgBox = 0, $colorErr = "#CC0000", $colorMsg = "#006600")
	{
		if(!isset($_SESSION["app_error"]))
		{
			$_SESSION["app_error"]="";
		}
		if(!isset($_SESSION["app_message"]))
		{
			$_SESSION["app_message"]="";
		}
		
		if($_SESSION["app_error"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_error'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorErr . "'>" . $_SESSION['app_error'] . "</font>");
				echo("<span class='errText'>" . $_SESSION['app_error'] . "</span>");
			}
			$_SESSION["app_error"]="";
		}
		else if($_SESSION["app_message"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_message'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorMsg . "'>" . $_SESSION['app_message'] . "</font>");
				echo("<span class='msgText'>" . $_SESSION['app_message'] . "</span>");
			}
			$_SESSION["app_message"]="";
		}
		else
		{
			echo("&nbsp;");
		}
	}
	
	/*This function checks and displays Application Message or Appliation Error*/
	public static function displayErrMsg4($msgBox = 0, $colorErr = "#CC0000", $colorMsg = "#006600")
	{
		if(!isset($_SESSION["app_error"]))
		{
			$_SESSION["app_error"]="";
		}
		if(!isset($_SESSION["app_message"]))
		{
			$_SESSION["app_message"]="";
		}
		
		if($_SESSION["app_error"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_error'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorErr . "'>" . $_SESSION['app_error'] . "</font>");
				echo("<span class='errText2'>" . $_SESSION['app_error'] . "</span>");
			}
			$_SESSION["app_error"]="";
		}
		else if($_SESSION["app_message"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_message'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorMsg . "'>" . $_SESSION['app_message'] . "</font>");
				echo("<span class='msgText2'>" . $_SESSION['app_message'] . "</span>");
			}
			$_SESSION["app_message"]="";
		}
		else
		{
			echo("&nbsp;");
		}
	}

	/*This function checks and displays Application Message or Appliation Error*/
	public static function displayErrMsg2($msgBox = 0, $colorErr = "#CC0000", $colorMsg = "#006600")
	{
		if(!isset($_SESSION["app_error2"]))
		{
			$_SESSION["app_error2"]="";
		}
		if(!isset($_SESSION["app_message2"]))
		{
			$_SESSION["app_message2"]="";
		}
		
		if($_SESSION["app_error2"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_error2'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorErr . "'>" . $_SESSION['app_error'] . "</font>");
				echo("<span class='errText'>" . $_SESSION['app_error2'] . "</span>");
			}
			$_SESSION["app_error2"]="";
		}
		else if($_SESSION["app_message2"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_message2'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorMsg . "'>" . $_SESSION['app_message'] . "</font>");
				echo("<span class='msgText'>" . $_SESSION['app_message2'] . "</span>");
			}
			$_SESSION["app_message2"]="";
		}
		else
		{
			echo("&nbsp;");
		}
	}
	
	/*This function checks and displays Application Message or Appliation Error*/
	public static function displayErrMsg3($msgBox = 0, $colorErr = "#CC0000", $colorMsg = "#006600")
	{
		if(!isset($_SESSION["app_error3"]))
		{
			$_SESSION["app_error3"]="";
		}
		if(!isset($_SESSION["app_message3"]))
		{
			$_SESSION["app_message3"]="";
		}
		
		if($_SESSION["app_error3"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_error3'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorErr . "'>" . $_SESSION['app_error'] . "</font>");
				echo("<span class='errText'>" . $_SESSION['app_error3'] . "</span>");
			}
			$_SESSION["app_error3"]="";
		}
		else if($_SESSION["app_message3"]!="")
		{
			if($msgBox == 1)
			{
				echo("<script type='text/javascript'>alert(\"" . $_SESSION['app_message3'] . "\")</script>");
			}
			else
			{
				//echo("<font color='" . $colorMsg . "'>" . $_SESSION['app_message'] . "</font>");
				echo("<span class='msgText'>" . $_SESSION['app_message3'] . "</span>");
			}
			$_SESSION["app_message3"]="";
		}
		else
		{
			echo("&nbsp;");
		}
	}


	/*Replace Characters (such as ", ', etc.)*/
	public static function titleCase($srcStr)
	{
		$resStr="";
		
		if(is_null($srcStr))
		{
			$srcStr = "";
		}
			
		if($srcStr=="")
		{
			return "";
		}
		
		//$resStr = preg_replace("/(^(\w*?))/e", "ucfirst('$1')", strtolower($srcStr));
		
		$resStr = ucwords(strtolower($srcStr)); 
		
		return $resStr;
	}
	
	/*Format Date Time to d-M-Y or any other...*/
	public static function formatMyDateTime($a_date, $a_format, $is_time_stamp = 0, $a_default_value = "")
	{
		if(is_null($a_date) || $a_date == "")
		{
			return($a_default_value);
		}
		else
		{
			if($is_time_stamp == 1)
			{
				//--- supplied date time is a TimeStamp, so no conversion required
				$tmpdt_stamp = $a_date;
			}
			else
			{
				//--- supplied date time is not a TimeStamp, but a string
				$tmpdt_stamp = strtotime($a_date);
			}
			return(date($a_format, $tmpdt_stamp));
		}
	}
	
	/*Check Date in Y-m-d format */
	public static function isValidDate($myDate)
	{
		$arrMyDate = split("-", $myDate);
		if ($myDate == "")
		{
			return 0;
		}
		else if (count($arrMyDate) != 3 )
		{
			return -1;
		}
		else if (checkdate($arrMyDate[1], $arrMyDate[2], $arrMyDate[0]) == false)
		{
			return -2;
		}
		else
		{
			return 1;
		}
	}

	/*Encrypt Text */
	public static function encryptText($inString)
	{
		$outString = "";

		if(is_null($inString) == true)
		{
			return "";
		}
		else if($inString == "")
		{
			return "";
		}
		$inString = strtoupper(trim($inString));
		for($i=0; $i < strlen($inString); $i++)
		{
			$ch = substr($inString, $i, 1);
			if($i % 3 == 0)
			{
				$ech = chr(ord($ch) + 13);
			}
			else if($i % 5 == 0)
			{
				$ech = chr(ord($ch) - 3);
			}
			else
			{
				$ech = chr(ord($ch) - 7);
			}
			$outString .= $ech;
		}
		if(strlen($inString) % 2 == 0)
		{
			$outString = chr(rand(65, 90)) . $outString . chr(rand(65, 90));
		}
		else
		{
			$outString = chr(rand(65, 90)) . chr(rand(65, 90)) . $outString . chr(rand(65, 90)) . chr(rand(65, 90));
		}
		
		return($outString);
	}

	/*Decrypt Text */
	public static function decryptText($inString)
	{
		$outString = "";
		
		if(is_null($inString) == true)
		{
			return "";
		}
		else if($inString == "")
		{
			return "";
		}
		$inString = trim($inString);
		if(strlen($inString) % 2 == 0)
		{
			$inString = substr($inString, 1);
			$inString = substr($inString, 0, strlen($inString)-1);
		}
		else
		{
			$inString = substr($inString, 2);
			$inString = substr($inString, 0, strlen($inString)-2);
		}
		
		for($i=0; $i < strlen($inString); $i++)
		{
			$ch = substr($inString, $i, 1);
			if($i % 3 == 0)
			{
				$ech = chr(ord($ch) - 13);
			}
			else if($i % 5 == 0)
			{
				$ech = chr(ord($ch) + 3);
			}
			else
			{
				$ech = chr(ord($ch) + 7);
			}
			$outString .= $ech;
		}
		
		return($outString);
	}

	/*Encode URL*/
	public static function encodeURL($url)
	{
		$url = preg_replace("&","-",$url);
		return $url;
	}

	public static function decodeURL($url)
	{
		$url = preg_replace("-","&",$url);
		return $url;
	}

	public static function rnLine($text)
	{
		$text=str_replace("\n","<br>",$text);
		return $text ;
	}

	public static function pnLine($text)
	{
		$text=str_replace("<br>","\n",$text);
		return $text ;
	}

	public static function html($aText)
	{
		$text=$aText;
		$text=str_replace(">",";&gt",$text);
		$text=str_replace("<",";&gt",$text);
		$text=str_replace("\n","<br>",$text);
		return $text ;
	}


	public static function generatePrevNext($aPageNo,$aRequestURI,$aScript,$theTotalPages)
	{
		$newurl = $aScript."?".preg_replace("!&pageno=[0-9]!is","",$aRequestURI);
		
		$content = "
			<table width=\"85%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\" bgcolor=\"#FFFFFF\">
			<tr>
			<td width=\"50%\" height=\"48\">";
		
		if($aPageNo>1)
		{
			$prevPageNo = $aPageNo - 1;
			$content .= "<div align=\"left\" ><a href=".$newurl."&PageNo=".$prevPageNo."> << Prev </a></div>";
		}
		
		$content .= "
			</td>
			<td width=\"50%\" height=\"48\">
			<div align=\"right\">";
		
		if($aPageNo<$theTotalPages)
		{
			$nextPageNo = $aPageNo + 1;
			$lastPageNo = $theTotalPages;
			$content .= "<a href=\"".$newurl."&PageNo=".$nextPageNo."\"> Next >> </a>";
		}
		$content .= "
			</div>
			</td>
			</tr>
			</table>";
		return $content;
	}
	
	
	//==== Sort Related =========================================================================
	//==== Show Sort Icon ====
	public static function showSortIcon($sortIndexNew, $sortIndexCurrent, $sortDirection)
	{
		global $config;
		
		if($sortIndexNew == $sortIndexCurrent)
		{
			if($sortDirection == "asc")
			{
				echo("<img src='http://" . $config["SITE_ROOT"] . "/images/arrow_up.gif' height='10' width='10' align='absmiddle'>");
			}
			else if($sortDirection == "desc")
			{
				echo("<img src='http://" . $config["SITE_ROOT"] . "/images/arrow_dn.gif' height='10' width='10' align='absmiddle'>");
			}
		}
	}
	
	//==== Get Sort related variables ====
	public static function getSortIndex($defaultSortIndexCurrent = 1)
	{
		//--- Get Current Sort By Column
		$sortIndexCurrent = 0;
		if(isset($_GET["sortby"]))
		{
			$sortIndexCurrent = intval(CommonFunctions::replaceChars($_GET["sortby"], 0));
		}
		if($sortIndexCurrent == 0 && (isset($_POST["sortby"])) )
		{
			$sortIndexCurrent = intval(CommonFunctions::replaceChars($_POST["sortby"], 0));
		}
		if($sortIndexCurrent == 0)
		{
			//--- Default Sort By 1st column
			$sortIndexCurrent = $defaultSortIndexCurrent;
		}
		
		return $sortIndexCurrent;
	}
	
	//==== Get Sort related variables ====
	public static function getSortDirection($defaultSortDirection = "desc")
	{
		//--- Get Current Sort Order
		$sortDirection = "";
		if(isset($_GET["sortmode"]))
		{
			$sortDirection = CommonFunctions::replaceChars($_GET["sortmode"], 0);
		}
		if($sortDirection == "" && (isset($_POST["sortmode"])) )
		{
			$sortDirection = CommonFunctions::replaceChars($_POST["sortmode"], 0);
		}
		if($sortDirection != "asc" && $sortDirection != "desc")
		{
			//--- Default Sort Order Desc (as 1st column is normally an ID field, so display latest on top)
			$sortDirection = $defaultSortDirection;
		}
		
		return $sortDirection;
	}
	
	
	//=====================================================================================================

	//----------------------------------------------------------------
	/*Get File Extension (jpg/gif/jpeg/etc...)*/
	public static function getFileExtension($fileName)
	{
		$fileExt="";
		
		if(is_null($fileName))
		{
			$fileExt = "";
		}
		
		$tmpText = strrchr($fileName, ".");
		
		if($tmpText === false)
		{
			$fileExt = "";
		}
		else
		{
			$fileExt = substr($tmpText, 1);
		}
		
		$fileExt = strtolower($fileExt);
		
		return $fileExt;
	}

	// ====Get New Image Size in the Available Area====//
	public static function getNewImageSize($image_file, $avail_width, $avail_height, $orig_width=0, $orig_height=0)
	{
		if($orig_width == 0 && $orig_height == 0)
		{
			list($orig_width, $orig_height) = getimagesize($image_file);
		}
		//echo($orig_width . "_" . $orig_height);exit;
		$targ_width = 0;
		$targ_height = 0;
		
		if($avail_width > 0 && $avail_height > 0)
		{
			if($orig_width <= $avail_width && $orig_height <= $avail_height)
			{
				if($orig_width > $orig_height)
				{
					$targ_height =  intval($orig_height * (($avail_width * 1.0) / ($orig_width * 1.0)));
					$targ_width = $avail_width;
				}
				else
				{
				//echo $avail_height;exit;
					$targ_width =  intval($orig_width * (($avail_height * 1.0) / ($orig_height * 1.0)));
					$targ_height = $avail_height;
				}
				
			}
			else
			{
				if($orig_width > $avail_width && $orig_height > $avail_height)
				{
					$targ_width =  intval($orig_width * (($avail_height * 1.0) / ($orig_height * 1.0)));
					$targ_height = $avail_height;
				}
				else
				{
					$targ_height = $orig_height;
					$targ_width = $orig_width;
				}
			}
				
			if($targ_width > $avail_width && $targ_height <= $avail_height)
			{
				$targ_height =  intval($targ_height * (($avail_width * 1.0) / ($targ_width * 1.0)));
				$targ_width = $avail_width;
			}
			else if($targ_height > $avail_height && $targ_width <= $avail_width)
			{
				$targ_width =  intval($targ_width * (($avail_height * 1.0) / ($targ_height * 1.0)));
				$targ_height = $avail_height;
			}
		}
		else if($avail_width > 0 && $avail_height == 0)
		{
			$targ_height =  intval($orig_height * (($avail_width * 1.0) / ($orig_width * 1.0)));
			$targ_width = $avail_width;
		}
		else if($avail_height > 0 && $avail_width == 0)
		{
			$targ_width =  intval($orig_width * (($avail_height * 1.0) / ($orig_height * 1.0)));
			$targ_height = $avail_height;
		}
		
		$arr_targ_wh = array();
		$arr_targ_wh[0] = $targ_width;
		$arr_targ_wh[1] = $targ_height;
		//echo("<br>" . $arr_targ_wh[0] . "_" . $arr_targ_wh[1] . "<br>");
		
		return($arr_targ_wh);
	}
	

	
	//----------------------------------------------------------------
	/*Matches a String with a List of Comma separated Strings. Suitable for File Extension check */
	/*Returns 1: Success, 0: No Match)*/
	public static function strInList($strNiddle, $strList)
	{
		$fileExt="";
		
		if(is_null($strNiddle) || is_null($strList)|| $strNiddle == "" || $strList == "")
		{
			return 0;
		}
		
		if(stristr("," . $strList . ",", "," . $strNiddle . "," ) === FALSE)
		{
			return 0;
		}
		else
		{
			return 1;
		}
	}
		
	
	/*This function saves a photo in Passport size (Returns 1: Success, 0: Failure) */
	public static function savePassportImage($srcImage, $trgImage)
	{
		$resStr="";
		
		if(is_null($srcImage))
		{
			return 0;
		}
			
		if($srcImage=="")
		{
			return 0;
		}
		
		// File and new size
		//$srcImage = 'FacultyPhotoGallery/' . $_GET["img"];
		//$trgImage = 'FacultyPhotoGallery/' . 'pp_' . $_GET["img"];
		//$w = 240;
		//$h = 315;
		$filename = $srcImage;
		$filenameTarget = $trgImage;
		
		// Get new sizes
		list($width, $height, $itype) = getimagesize($filename);
		$newwidth = 240;
		$newheight = 315;
		
		// Load
		$thumb = imagecreatetruecolor($newwidth, $newheight);
		
		switch($itype)
		{
			//--- jpeg
			case 2:
				$source = imagecreatefromjpeg($filename);
				break;
			default:
				return 0;
		}
		
		// Resize
		if(!(imagecopyresized($thumb, $source, 0, 0, 0, 0, $newwidth, $newheight, $width, $height)))
		{
			return 0;
		}
		
		// Save to Disk
		switch($itype)
		{
			//--- jpeg
			case 2:
				if(!(imagejpeg($thumb, $filenameTarget)))
				{
					return 0;
				}
				break;
			default:
				return 0;
		}
		
		// Output
		//imagejpeg($thumb);

		return 1;
	}


	/* This function used to Download a Document without showing the path */
	public static function downloadAFile($file)
	{
		//First, see if the file exists
		if (!is_file($file)) 
		{ 
			die("<b>404 File not found!</b>"); 
		}
	
		//Gather relevent info about file
		$len = filesize($file);
		$filename = basename($file);
		$file_extension = strtolower(substr(strrchr($filename,"."),1));
		
		//This will set the Content-Type to the appropriate setting for the file
		switch( $file_extension ) 
		{
			case "pdf": $ctype="application/pdf"; break;
			case "exe": $ctype="application/octet-stream"; break;
			case "zip": $ctype="application/zip"; break;
			case "doc": $ctype="application/msword"; break;
			case "docx": $ctype="application/vnd.openxmlformats-officedocument.wordprocessingml.document";break;
			case "xls": $ctype="application/vnd.ms-excel"; break;
			case "ppt": $ctype="application/vnd.ms-powerpoint"; break;
			case "gif": $ctype="image/gif"; break;
			case "png": $ctype="image/png"; break;
			case "jpeg":
			case "jpg": $ctype="image/jpg"; break;
			case "mp3": $ctype="audio/mpeg"; break;
			case "wav": $ctype="audio/x-wav"; break;
			case "mpeg":
			case "mpg":
			case "mpe": $ctype="video/mpeg"; break;
			case "mov": $ctype="video/quicktime"; break;
			case "avi": $ctype="video/x-msvideo"; break;
			
			//The following are for extensions that shouldn't be downloaded (sensitive stuff, like php files)
			case "php":
			case "htm":
			case "html":
			case "txt": die("<b>Cannot be used for ". $file_extension ." files!</b>"); break;
	
			default: $ctype="application/force-download";
		}
	
		//Begin writing headers
		header("Pragma: public");
		header("Expires: 0");
		header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
		header("Cache-Control: public"); 
		header("Content-Description: File Transfer");
		
		//Use the switch-generated Content-Type
		header("Content-Type: $ctype");
		
		//Force the download
		$header="Content-Disposition: attachment; filename=".$filename.";";
		header($header );
		header("Content-Transfer-Encoding: binary");
		header("Content-Length: ".$len);
		@readfile($file);
		exit;
	}
	
	/* URL Validation */
	public static function validateURL($url)   
	{   
		$pattern = '/^(([\w]+:)?\/\/)?(([\d\w]|%[a-fA-f\d]{2,2})+(:([\d\w]|%[a-fA-f\d]{2,2})+)?@)?([\d\w][-\d\w]{0,253}[\d\w]\.)+[\w]{2,4}(:[\d]+)?(\/([-+_~.\d\w]|%[a-fA-f\d]{2,2})*)*(\?(&amp;?([-+_~.\d\w]|%[a-fA-f\d]{2,2})=?)*)?(#([-+_~.\d\w]|%[a-fA-f\d]{2,2})*)?$/';   
		return preg_match($pattern, $url);   
	}   

	/* Email Validation */
	public static function validateEmail($email)   
	{   
		$pattern = '{^[a-zA-Z][\w\.-]*[a-zA-Z0-9]@[a-zA-Z0-9][\w\.-]*[a-zA-Z0-9]\.[a-zA-Z][a-zA-Z\.]*[a-zA-Z]$}';   
		return preg_match($pattern, $email);   
	}   

	/* US State Validation */
	public static function validateStateUS($stateUS)   
	{   
		$pattern = '{^[a-zA-Z][a-zA-Z]$}';   
		return preg_match($pattern, $stateUS);   
	}	
	
	/* US Zip code Validation */
	public static function validateZipUS($zipUS)   
	{   
		$pattern = '/(^\d{5}$)|(^\d{5}-\d{4}$)/';   
		return preg_match($pattern, $zipUS);   
	}	
	
	

	public static function isAddressValid($anEmailAddress)
	{
		//if (ereg("^[^@ ]+@([a-zA-Z0-9\-]+\.)+([a-zA-Z0-9\-]{2}|net|com|gov|mil|org|edu|int)\$",$anEmailAddress))
		if (preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$/",$anEmailAddress))
		{
			return true;
		}
		else
		{
			return false;
		}
	}

    public static function isValidEmail($anEmailAddress)
	{
		if (!filter_var($anEmailAddress, FILTER_VALIDATE_EMAIL)) {
		    return false;
		}else{
		    return true;
		}
	}

	public static function getPartMultiByteString($srcString, $strLength, $useEllipsis = 1, $breakAtWord = 1)
	{
		$actlen = mb_strlen($srcString);
			
		$outString = "";
		if($actlen <= $strLength)
		{
			return $srcString;
		}
		else
		{
			$outString = mb_substr($srcString, 0, $strLength);
			if($breakAtWord != 1)
			{
				//--- Breaks at the end of the length
				if($useEllipsis == 1)
				{
					$outString .= "...";
				}
				return $outString;
			}
			else
			{
				//--- Breaks where the last word ends within the length
				if(mb_substr($outString, mb_strlen($outString)-1, 1) == " " )
				{
					if($useEllipsis == 1)
					{
						$outString .= "...";
					}
				}
				else
				{
					$pos = mb_strrpos($outString, " ");
					if ($pos === false) 
					{
						// no change
					}
					else
					{
						$outString = mb_substr($outString, 0, $pos);
						if($useEllipsis == 1 && $actlen > $strLength)
						{
							$outString .= "...";
						}
					}
				}
			}
		}
		
		return $outString;
	}
	
	
	public static function getWeekDayName($aWeekDayNum)
	{
		$arrMyWeekDays = array(0=>"Sunday", 1=>"Monday", 2=>"Tuesday", 3=>"Wednesday", 4=>"Thursday", 5=>"Friday", 6=>"Saturday" );
		
		$aWeekDayNum = intval($aWeekDayNum);
		if($aWeekDayNum < 0 || $aWeekDayNum > 6)
		{
			return "";
		}
		else
		{
			return $arrMyWeekDays[$aWeekDayNum];
		}
	}

	//--- Get Age
	public static function getAge($bMonth, $bDay, $bYear, $cMonth, $cDay, $cYear) 
	{
		$cMonth = date('n');
		$cDay = date('j');
		$cYear = date('Y');
	
		if(($cMonth >= $bMonth && $cDay >= $bDay) || ($cMonth > $bMonth)) 
		{
			return ($cYear - $bYear);
		} 
		else 
		{
			return ($cYear - $bYear - 1);
		}
	}
	
	//Generate Random Character
	public static function generateRndChrs($length) 
	{
		$out_string = "";
		for($i=0; $i<$length; $i++)
		{
			$out_string .= chr(rand(65,90));
		}
		return $out_string;
	}
	
	//Calculate Age
	public static function getAgeAtTime($birthday)
	{
		list($year,$month,$day) = explode("-",$birthday);
		$year_diff = date("Y") - $year;
		$month_diff = date("m") - $month;
		$day_diff = date("d") - $day;
	
		if ($month_diff < 0) 
		{
			$year_diff--;
		}
		elseif(($month_diff==0) && ($day_diff < 0))
		{
			$year_diff--;
		}
		return $year_diff;	
	}
	
	//==== Current Page Name ====
	public static function curPageName() 
	{
		return substr($_SERVER["SCRIPT_NAME"],strrpos($_SERVER["SCRIPT_NAME"],"/")+1);
	}


	//==== Show/Write Meta Tags in Page ====
	public static function showMetaContent($dbLink, $cur_module_name)
	{
		global $config;		// Global Config Settings
		
		if(trim($cur_module_name) == "")
		{
			echo("");
			return(1);
		}
		
		$tmpSql = "SELECT meta_id,meta_title,meta_keywords,meta_description"
			. " FROM tbl_metatag_modules WHERE meta_module_name = '" . $cur_module_name . "'";
		$rs_tmp = mysql_query($tmpSql, $dbLink);
			
		if( (!$rs_tmp) || (!($rec_tmp=mysql_fetch_array($rs_tmp))) )
		{
			echo("<title>" . $config["APP_TITLE"] . "</title> \n");
			return(1);
		}
		else
		{
			echo("<title>" . $config["APP_TITLE"] . ": " . $rec_tmp["meta_title"] . "</title> \n");
			echo("<meta name=\"keywords\" content=\"" . $rec_tmp["meta_keywords"] . "\" /> \n");
			echo("<meta name=\"description\" content=\"" . $rec_tmp["meta_description"] . "\" /> \n");
			return(1);
		}			
	}	
	
	public static function check_login()
	{
		//@session_start();
		$expiration_time = intval($_SESSION['CREATED']);

		if (!isset($_SESSION['CREATED']))
	    {
			$_SESSION['CREATED'] = time();
		} else if ((time() - $_SESSION['CREATED']) >3600)
	  
	   {
			// session started more than 30 minates ago
			//session_regenerate_id(true);     change session ID for the current session an invalidate old session ID
			//$_SESSION['CREATED'] = time();   update creation time
			session_destroy(); header("Location: expired.php");
		}
	}
	
	public static function secondsToTime($seconds)
	{
		// extract hours
		$hours = floor($seconds / (60 * 60));
	 
		// extract minutes
		$divisor_for_minutes = $seconds % (60 * 60);
		$minutes = floor($divisor_for_minutes / 60);
	 
		// extract the remaining seconds
		$divisor_for_seconds = $divisor_for_minutes % 60;
		$seconds = ceil($divisor_for_seconds);
	 
		// return the final array
		$obj = array(
			"h" => (int) $hours,
			"m" => (int) $minutes,
			"s" => (int) $seconds,
		);
		return $obj;
	}
	
	public static function generate_productcode($prefix, $pmax, $pad_length, $string_pad)
	{
		$pro =  intval($pmax) + 1;		
		$product_code =  $prefix. str_pad($pro, $pad_length, $string_pad, STR_PAD_LEFT); 
		return $product_code;
	}
	
	public static function removeFiles($file_path, $file_name)
	{	
		$file_path_name = "";
		$file_path_name = $file_path.$file_name;
					
		if($file_name != "" && file_exists($file_path_name))
		{
			@unlink($file_path_name);
		}
	}
	
	public static function renameFiles($file_path, $file_old_name, $file_new_name)
	{	
		$file_path_old_name = "";
		$file_path_new_name = "";
		
		$file_path_old_name = $file_path.$file_old_name;
		$file_path_new_name = $file_path.$file_new_name;
					
		if($file_old_name != "" && file_exists($file_path_old_name))
		{
			@rename($file_path_old_name, $file_path_new_name);
		}
	}
	
	public static function is_leap_year($yearX)
	{
		return ((($yearX % 4) == 0) && ((($yearX % 100) != 0) || (($yearX %400) == 0)));
	}
}
?>