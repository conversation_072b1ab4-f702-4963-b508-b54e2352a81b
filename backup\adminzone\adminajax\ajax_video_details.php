<?php
//print_r($_POST); die();
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $video_id;
	public $video_link;
	public $video_title;
	public $arr_video_link, $arr_video_title, $video_link_edit, $video_title_edit;
			
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		//--- Set Current Module Name ---
		$this->module_name = "Ajax Video Details";
		$this->cur_page_url = "ajax_video_details.php";
		$this->list_page_url = "ajax_video_details.php";
				
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
			
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createVideo();
				break;
			case 2:
				//--- Edit Record
				$this->editVideo();
				break;
			default:
				$this->task = 0;
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
			
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->video_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->video_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->video_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->video_id = 0;
			}
		}
		//echo $this->video_id;die("tendetid");
		
		return 1;
	}


	//==== Create New Record ====
	public function createVideo()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//==== Edit Existing Record ====
	public function editVideo()
	{
		//--- Get Record Id ---
		$this->getRecordId();
         	
		if ($this->video_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		$this->arr_video_title = array();
		$this->arr_video_link = array();
		$this->video_id = "";	
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{ 	
		if(isset($_POST["video_title"]))
		{
			$i = 0;
			foreach($_POST["video_title"] as $val)
			{	
				$this->arr_video_title[$i] = CommonFunctions::replaceChars($val, 0);	
				//$this->arr_video_title[$i] = !empty($val)?CommonFunctions::replaceChars($_POST["video_title_". $i], 0):"";
				$this->arr_video_link[$i] = !empty($val)?CommonFunctions::replaceChars($_POST["video_link_". $i], 0):"";			
				$i++;
			}
		}
		
		/*if(isset($_POST["video_link"]))
		{
			$i = 0;
			foreach($_POST["video_link"] as $val)
			{	
				//$this->arr_video_link[$i] = CommonFunctions::replaceChars($val, 0);
				$this->arr_video_link[$i] = !empty($val)?CommonFunctions::replaceChars($_POST["video_link_". $i], 0):"";
				$i++;
			}
		}*/
		
		if($this->task == 2){
			$this->video_title_edit = CommonFunctions::replaceChars($_POST["video_title_edit"], 0);
			$this->video_link_edit = CommonFunctions::replaceChars($_POST["video_link_edit"], 0);
		}
							
		return 1;
	}	

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";
		
		if($this->task == 1){
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->arr_video_title); $x++)
				{
					if($this->arr_video_title[$x] == "")
					{
						$myerr = "Please specify video title -".$x;
						break;
					}
				}
				
			}
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->arr_video_link); $x++)
				{
					if($this->arr_video_link[$x] == "")
					{
						$myerr = "Please specify youtube link -".$x;
						break;
					}
				}
				
			}
			
	    }
		
		if($this->task == 2){
			
			if($myerr == "")	
			{
				if($this->video_title_edit == "")
				{
					$myerr = "Please specify video title";
				}
				
			}	
			if($myerr == "")	
			{
				if($this->video_link_edit == "")
				{
					$myerr = "Please specify video link";
				}
				
			}	
			
	    }

		if($myerr != "")
		{
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction 
		$this->link_id->autocommit(FALSE);
			
		if($this->task == 1)
		{
			//print_r($this->arr_video_title); die('ee');
			for($i=1; $i<count($this->arr_video_title); $i++)
			{
				/*print_r($this->arr_video_link) . "<br/>";
				die('ee');*/
				$tmpSql = "INSERT INTO tbl_video(video_id, video_title, video_link)" 
						. " VALUES(null, '" . addslashes($this->arr_video_title[$i]) . "'" 
						. ", '" . addslashes($this->arr_video_link[$i])."')";
				 //echo $tmpSql; die('ss');
				$rs = $this->link_id->query($tmpSql);	
			}
			
			if($myerr != "")
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();				
			}			
			else
			{
				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
			
		}
		else if($this->task == 2)
		{
			$tmpSql = "UPDATE tbl_video SET " 					
					. "  video_title = '" . addslashes($this->video_title_edit) ."'"
					. ", video_link = '" . addslashes($this->video_link_edit) ."'"
					. "  WHERE video_id = " . $this->video_id;
		   //echo $tmpSql	;die("ddd");	
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();
			}
				
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			echo '2';
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
}

$objCurPage = new CurrentPage();
?>