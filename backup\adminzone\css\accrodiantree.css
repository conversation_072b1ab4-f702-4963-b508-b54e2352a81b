@charset "utf-8";
/* CSS Document */
/* root element for accordion. decorated with rounded borders and gradient background image */
.accordiontree {
    width: 100%;
    border:1px solid #616161;
}

/* accordion header */
.accordiontree h2 {
	background:#979797 url(../images/plus.gif) no-repeat 5px 7px;
	height:30px;
	line-height:30px;
    margin:0;
    padding:0 25px;
    font-size:16px;
    font-weight:normal;
    border-bottom:1px solid #616161;
    cursor:pointer;
    color: #fff;
	text-align:left;
}

/* currently active header */
.accordiontree h2.current {
    cursor:pointer;
    background:#782d2d url(../images/minus.gif) no-repeat 5px 7px;
	
}

/* accordion pane */
.accordiontree .pane {
    display:none;
    margin:10px;
    color:#fff;
    font-size:12px;
}

/* a title inside pane */
.accordiontree .pane h3 {
    font-weight:normal;
    margin:0 0 -5px 0;
    font-size:16px;
    color:#999;
}


.accDisplay{
	display:block;
}
.accNoDisplay{
	display:none;
}