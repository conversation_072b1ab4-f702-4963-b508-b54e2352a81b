<?php
error_reporting(E_ALL);
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $cat_id;
	public $image_id;
	public $image_title;
	public $upload_image;
	public $path_gallery_photo;
	public $upload_image_arr;
	public $arr_image_title;


	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Edit Category";
		$this->cur_page_url = "edit_galleryimg.php";
		$this->list_page_url = "managephotogallery_list.php";

		//--- Check User Access permissions	
		UserAccess::checkUserLogin("", "admin");
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(2);/**/
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		$this->path_gallery_photo = "../../gallery_images/";
		

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		$this->maxfilesize = 2*1024*1024;
		
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createGalleryPhoto();
				break;
			case 2:
				//--- Edit Record
				$this->editGalleryPhoto();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode);
				die("_");
		}

	}


	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->cat_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->cat_id = 0;
			}
		}
		
		return 1;
	}


//==== Create New Record ====
	public function createGalleryPhoto()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}		

		return 1;
	}


	//==== Edit Existing Record ====
	public function editGalleryPhoto()
	{
		//--- Get Record Id ---
		$this->getRecordId();
         	
		if ($this->cat_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
			die("_");
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		
		return 1;
	}
	
	//=== Initialize Form Data ====
	public function initFormData()
	{
		if($this->task == 1)
		{
			//--- Add Mode. Initialize field values to blank or zero or null
			
			$this->cat_id = 0;	
			$this->image_title = "";
			$this->upload_image_arr = array();
		}
		else if($this->task == 2)
		{			
			//--- Edit Mode. Initialize fields by getting values from Database
			$tmpSql = "SELECT image_id,cat_id, image_title, upload_image "
				. " FROM tbl_photo_gallery "
				. " WHERE cat_id = " . $this->cat_id;
										
							
			$rs = $this->link_id->query($tmpSql);
						
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found";
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode );
				die("_");
			}
			else
			{
				$this->image_id = $rec["image_id"];
				$this->upload_image = $rec["upload_image"];
				$this->upload_image_edit = $this->upload_image; 
				$this->image_title = stripslashes($rec["image_title"]);			
				
			}
		
		}
		
		return 1;
	
	}



	//=== Read Form Data ====
	public function readFormData()
	{	
		return 1;
	}
	
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{			
		$myerr = "";
											
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	

	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
						
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			//--- If no error and the user is available the page is already redirected to another page ---
			return 1;
		}
	}
}

$objCurPage = new CurrentPage();

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="images/logo.png" rel="icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<script language="javascript" type="text/javascript">
$(document).ready(function(){

	$("#loadingImage").hide();	
	displayGrid();
	
	$('#cmdSave').click(function(){  
		$("#loadingImage").hide();	
		validateSaveGallery();		
	});
	
	$('#cmdUpdate').click(function()
	{		
		validateUpdate();
	});

});
</script>
<script language="javascript" type="text/javascript">

function displayGrid()
{
	//add_item 1
	$('.addVidButton').click(function() {
			var oval, otxt;
		
		$('#vidContainer').append('<div class="vidItem" style="margin-bottom:5px;">' + $(".vidItemSample").html() + '</div>');	
					
			var new_div = $('#vidContainer .vidItem').last();
			if(new_div.size() > 0){
				new_div.find("input").each(function(n) {
					if(n == 0){
						$(this).focus();
					}
				});
				
				
				new_div.find("input[type='file']").each(function() {
					$(this).attr('name', 'gallery_photo_' + $('#vidContainer .vidItem').size() );
					$(this).attr('idx', $('#vidContainer .vidItem').size() );
				});
				
				new_div.find("a").each(function() {
				
					$(this).click(function(){					
						if($('#vidContainer .vidItem').size() > 1){		
							
							var objVidItm = $(this);
							var curIdx = $($(this).parent().parent().find("input[type='file']")[0]).attr('idx');
							curIdx = parseInt(curIdx);
							//alert($($(this).parent().parent().find("input[type='file']")[0]).attr('idx'));
							$(this).parent().parent().parent().parent().parent().siblings(".vidItem").each(function(){
							console.log($(this));
								cfile = $(this).find("input[type='file']")[0];
								anIdx = $(cfile).attr('idx');
								anIdx = parseInt(anIdx);
								if(anIdx > curIdx) {
									$(cfile).attr('name', 'gallery_photo_' + (anIdx-1) );
									$(cfile).attr('idx', (anIdx-1) );
								}
							});
										
							$(objVidItm).parent().parent().parent().parent().parent().remove();
							
							
						}else{
							$(this).parent().parent().find("input:text").each(function(){
								$(this).val("");
							});
							$(this).parent().parent().find("input:checkbox").each(function(){
								$(this).prop("checked", false);
							});
							$(this).parent().parent().find("select").each(function(){
								$(this).children('option:eq(0)').attr('selected', true);
							});
						}
	
					});
				});	
			}		
	});
	
	$(".vidItem").find("a").each(function() {

		$(this).click(function(){			
			if($('#vidContainer .vidItem').size() > 1){					
				$(this).parent().parent().parent().parent().parent().remove();
			}else{
				$(this).parent().parent().find("input:text").each(function(){
					$(this).val("");
				});
				$(this).parent().parent().find("input:checkbox").each(function(){
					$(this).prop("checked", false);
				});
				$(this).parent().parent().find("select").each(function(){
					$(this).children('option:eq(0)').attr('selected', true);
				});
			}
		});
	});
	
	/*< ?php if(count($objCurPage->upload_image_arr) == 0 ) { ?>
		$('.addVidButton').trigger("click");
	< ?php } ?>*/
	
	<?php if(count($objCurPage->upload_image_arr) == 0 ) { ?>
		$('.addVidButton').trigger("click");  //this event trigger the click event of addVidButton
		                                       // for the first time form is loaded
	<?php } ?>
}


function validateSaveGallery()
{
	$('#frmAddGalleryDetail').submit();
	
	$("#loaderImg").html('<img src="images/progress-bar.gif" />');
	$("#loaderImg").show();
}

function validateUpdate()
{
	$('#frmEditGalleryDetails').submit();
}

$('#frmAddGalleryDetail').ajaxForm({		
	target: '',
	success: function(data){
		if(data){
			$("#loadingImage").hide();	
			if(data == 1)
			{	
				$("#loaderImg").empty();
				$("#loaderImg").hide();
				
				location.href='<?php echo $objCurPage->list_page_url; ?>';
				return false;					
			}
			else
			{
				$("#loaderImg").empty();
				$("#loaderImg").hide();
				
				$('#showError').html(data);
				return false;
			}
		}
	}			
});

$('#frmEditGalleryDetails').ajaxForm({	
	target: '',
	success: function(data){	
		
		if(data == 1)
		{	
			location.href='<?php echo $objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&msg=1"; ?>';
			return false;					
		}
		else
		{


			$('#showError').html(data);
			return false;
		}
	}			
});

// for image delete
function deleteImage(image_id,cat_id)
{
	$.get('adminajax/ajax_edit_gallery_details.php?task=3&cat_id='+cat_id+'&image_id='+image_id, function(data) {
							
		if(data)
		{	
			if(data == "1")
			{				
				$('#rowId_'+image_id).remove();	
				var count_row = $('#gallery_img_body').children().length;
				if(count_row == 0)
				{ 
					$('#update').hide();  
				}
				return false;
			}
			else
			{
				
				return false;	
			}
		}
		
	});
	
}

</script>
</head>

<body>
<div id="header">
  <div class="container">
    <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px; min-height:515px; margin-bottom:10px;">
  <div id="row">
    <aside class="col-sm-3 sidebar sidebar-right"> <!--left_menu start here-->
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-8 maincontent" style="border:1px solid #ccc;">
      <div id="show_error">
        <?php CommonFunctions::displayErrMsg(); ?>
      </div>
      <div id="showError" style="text-align:center; color:#F00;"></div>
      <table width="100%" cellpadding="0" cellspacing="0" border="0" class="dialogBase">
        <tr>
          <td class="dialogHeader"><h1>
              <?php if($objCurPage->task == 1){ echo("Add "); }else{ echo("Edit "); } ?>
              Gallery </h1></td>
        </tr>
        <tr>
          <td align="center"><div class="tableRow" align="center">
              <div class="imgLoader" id="loaderImg" style="display:none;"></div>
            </div></td>
        </tr>
        <tr>
          <td><table width="100%" cellpadding="5" cellspacing="0" border="0" class="formTextWithBorder">
              <tr>
                <td><form name="frmAddGalleryDetail" id="frmAddGalleryDetail" method="post" action="adminajax/ajax_edit_gallery_details.php"  enctype="multipart/form-data">
                    <table  width="100%" cellpadding="5" cellspacing="0" border="0" class="formTextWithBorder">
                      <tr>
                        <td valign="top">Upload Photo:<sup class="reqd" style="color:red;">*</sup></td>
                      </tr>
                      <tr>
                        <td><div id="vidContainer" style="margin-bottom:5px;">
                            <div>
                              <table width="100%" border="0" cellpadding="2" cellspacing="1">
                                <tr valign="top" class="trHeader1">
                                  <td width="38%" height="15">Photo Title</td>
                                  <td width="35%" align="left">Gallery Photo<br />
                                    <font size="1" color="#666666"> Gallery (File Types .jpg,.jpeg,.gif   Max Size: 2MB)</font></td>
                                  <td width="21%" align="center"><div id="qvar_addmore" align="center"><a title="Add" style="cursor:pointer;" class="addVidButton"><img height="16" align="absmiddle" width="16" src="images/b_add.png" border="0" /> Add more</a>&nbsp;&nbsp;</div></td>
                                  <td width="6%" align="left">&nbsp;</td>
                                </tr>
                              </table>
                            </div>
                            <div class="vidItemSample" style="display:none;">
                              <table width="100%" border="0" cellpadding="0" cellspacing="1">
                                <tr valign="top">
                                  <td width="38%" align="left"><input type="text" name="image_title[]" value=""  placeholder="Photo Name" maxlength="100" class="form-control" style="width:90%;" /></td>
                                  <td width="35%" align="left"><input type="file" name="gallery_photo_0" value=""  />
                                    <input type="hidden" name="gallery_photo_HX[]" value="1" /></td>
                                  <td width="21%" align="center"><a title="Remove" class="delVidButton" style="cursor:pointer;" ><img height="16" align="absmiddle" width="16" src="images/b_drop.png" border="0" /></a></td>
                                  <td width="6%">&nbsp;</td>
                                </tr>
                              </table>
                            </div>
                          </div></td>
                        <td>&nbsp;</td>
                      </tr>
                      <tr>
                        <td colspan="2" align="right"><div class="imgLoader" id="loaderImg" style="display:none;"></div>
                          <input type="hidden" name="recid" id="recid" value="<?php echo $objCurPage->cat_id; ?>" />
                          <input type="hidden" name="task" id="task" value="1" />
                          <input name="sortby" type="hidden" id="sortby" value="<?php echo $objCurPage->sortby; ?>" />
                          <input name="sortmode" type="hidden" id="sortmode" value="<?php echo $objCurPage->sortmode; ?>" />
                          <input type="hidden" name="postMe" value="Y" />
                          <input type="button" name="cmdSave" id="cmdSave" value="Save" class="btn btn-default" />
                          <input type="button" name="cmdSave" id="cmdSave1" value="Save" class="btn btn-default" style="display:none;" />
                          <input name="cmdRevert" type="button" class="btn btn-default" id="cmdRevert" value="Revert" onClick="window.location.href='<?php echo($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&recid=" . $objCurPage->cat_id . "&task=". $objCurPage->task ); ?>'" />
                          <input name="cmdExit" type="button" tabindex="9" class="btn btn-default" id="cmdExit" onClick="window.location.href='<?php echo($objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode ); ?>'" value="Exit" /></td>
                      </tr>
                    </table>
                  </form></td>
              </tr>
              <tr>
                <td colspan="2">&nbsp;</td>
              </tr>
              <tr>
                <td colspan="2" align="center" id="ref_link"></td>
              </tr>
              <tr>
                <td><form name="frmEditGalleryDetails" id="frmEditGalleryDetails" method="post" action="adminajax/ajax_edit_gallery_details.php"  enctype="multipart/form-data">
                    <table  width="100%" cellpadding="5" cellspacing="0" border="1">
                      <tr>
                        <td colspan="2"><div id="docRecord" name="docRecord">
                            <?php
                                        $tmpSql = "SELECT * FROM tbl_photo_gallery WHERE 1 AND cat_id = " . $objCurPage->cat_id  . " ORDER BY image_id DESC";	
										 
										 $rs = $objCurPage->link_id->query($tmpSql);
                                
										if( (!$rs) || (!($rec = $rs->fetch_array())) )
                                        {
                                           
                                        }
                                        else
                                        {
                                            do
                                            { 
                                            ?>
                            <table width="100%" border="0" cellspacing="2" cellpadding="2">
                              <tbody id="gallery_img_body">
                                <tr id="rowId_<?php echo $rec["image_id"];?>">
                                  <td width="38%" class="frm_tdbdr"><input type="text" name="image_title_<?php echo $rec["image_id"]; ?>" value="<?php echo $rec["image_title"]; ?>" placeholder="Photo Name" maxlength="100" class="form-control" style="width:90%;"/></td>
                                  <td width="35%" align="left" class="frm_tdbdr">&nbsp;
                                    <input type="file" name="upload_img_<?php echo $rec["image_id"]; ?>" value="" />
                                    <input type="hidden" id="cat_id" name="cat_id" value="<?php echo $objCurPage->cat_id; ?>" />
                                    <input type="hidden" id="image_id" name="image_id" value="<?php echo $rec["image_id"]; ?>" />
                                    <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" /></td>
                                  <td width="17%" align="center" class="frm_tdbdr"><img src="<?php echo '../gallery_images/thumb_'.$rec["upload_image"] ; ?>"  width="100" alt="NOT FOUND"/></td>
                                  <td width="16%" align="center" class="frm_tdbdr"><a onclick="deleteImage(<?php echo $rec["image_id"];?>,<?php echo $rec["cat_id"];?>);" id="delAttach" style="cursor:pointer;"><img src="images/b_drop.png" width="16" height="16" border="0" /></a></td>
                                </tr>
                              </tbody>
                            </table>
                            <?php 	
                                           }while($rec = $rs->fetch_array());
                                        }                                               
                                        ?>
                            <input type="hidden" id="cat_idx" name="cat_idx" value="" />
                          </div></td>
                      </tr>
                      <tr id="update">
                        <td colspan="2" align="right" class="frm_tdbdr"><div class="imgLoader" id="loaderImg" style="display:none;"></div>
                          <input type="hidden" name="recid" id="recid" value="<?php echo $objCurPage->cat_id; ?>" />
                          <input type="hidden" name="task" id="task" value="<?php echo $objCurPage->task; ?>" />
                          <input name="sortby" type="hidden" id="sortby" value="<?php echo $objCurPage->sortby; ?>" />
                          <input name="sortmode" type="hidden" id="sortmode" value="<?php echo $objCurPage->sortmode; ?>" />
                          <input type="hidden" name="postMe" value="Y" />
                          <input type="button" name="cmdUpdate" id="cmdUpdate" value="Update" class="btn btn-default" />
                          <input name="cmdRevert" type="button" class="btn btn-default" id="cmdRevert" value="Revert" onClick="window.location.href='<?php echo($objCurPage->cur_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode . "&recid=" . $objCurPage->cat_id . "&task=". $objCurPage->task ); ?>'" />
                          <input name="cmdExit" type="button" tabindex="9" class="btn btn-default" id="cmdExit" onClick="window.location.href='<?php echo($objCurPage->list_page_url . "?sortby=" . $objCurPage->sortby . "&sortmode=" . $objCurPage->sortmode ); ?>'" value="Exit" /></td>
                      </tr>
                    </table>
                  </form></td>
              </tr>
            </table></td>
        </tr>
      </table>
    </div>
    <div class="col-sm-1">&nbsp;</div>
  </div>
  <script type="text/javascript">
$(document).ready(function(e) {
	//ExpandSelect("cat_id");
	/*$("#cat_id").on('click', function(){
       var s = $("#cat_id option").size();	
       $("#cat_id").attr('size', s);
   })*/
   
});

</script> 
</section>
<?php include_once('footer.php');?>
</body>
</html>