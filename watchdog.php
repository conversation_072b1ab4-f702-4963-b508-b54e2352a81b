<?php
// === CONFIG ===
$watchDir = __DIR__; // Adjust if needed
$siteName = 'UGIE'; // 🔁 << UNIQUE NAME for this site
$hashFile = __DIR__ . '/../private/.file_hashes.json';

// === Telegram Bot Config (shared across all servers) ===
$secrets = include(__DIR__ . '/../private/secrets.env.php');
$telegramBotToken = $secrets['bot_token'];
$telegramChatId = $secrets['chat_id2'];

// === SCAN FILES ===
function scanFiles($dir) {
    $extensions = [
        'php', 'php5', 'phtml', 'html', 'htm', 'js','css','khtml',
        'txt', 'log', 'json', 'xml', 'bak', 'zip', 'tar', 'gz',
        'ini', 'conf', 'env', 'sh', 'pl', 'py', 'cgi', 'exe', 'dll', 'so', 'ico'
    ];
    $ignoreFiles = [
    '.file_hashes.json',
    'telegram_error.log',
    //'formwatchdog.php',
    '.form_hash.json'
    ];
    $files = [];
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $fileName = $file->getFilename();
            if (in_array($fileName, $ignoreFiles)) {
                continue; // Skip ignored files
            }
            $ext = strtolower($file->getExtension());
            if (in_array($ext, $extensions)) {
                $realPath = $file->getRealPath();
                $files[$realPath] = hash_file('sha256', $realPath);
            }
        }
    }
    return $files;
}


$current = scanFiles($watchDir);
$previous = file_exists($hashFile) ? json_decode(file_get_contents($hashFile), true) : [];

$added = array_diff_key($current, $previous);
$removed = array_diff_key($previous, $current);
$modified = [];

foreach ($current as $file => $hash) {
    if (isset($previous[$file]) && $previous[$file] !== $hash) {
        $modified[$file] = $hash;
    }
}

if ($added || $removed || $modified) {
    $msg = "🚨 *{$siteName} File Alert*\n";
    if ($added) $msg .= "🟢 *Added:*\n" . implode("\n", array_keys($added)) . "\n\n";
    if ($removed) $msg .= "🔴 *Removed:*\n" . implode("\n", array_keys($removed)) . "\n\n";
    if ($modified) $msg .= "🟠 *Modified:*\n" . implode("\n", array_keys($modified)) . "\n\n";
    sendTelegramAlert($telegramBotToken, $telegramChatId, $msg);
} else {
    #$msg = "✅ *{$siteName}*: All Clear. No file changes detected.";
    #sendTelegramAlert($telegramBotToken, $telegramChatId, $msg);
}


// Save latest snapshot
file_put_contents($hashFile, json_encode($current, JSON_PRETTY_PRINT));

// === TELEGRAM FUNCTION ===
// function sendTelegramAlert($token, $chat_id, $message) {
//     $url = "https://api.telegram.org/bot{$token}/sendMessage";
//     $data = [
//         'chat_id' => $chat_id,
//         'text' => $message,
//         //'parse_mode' => 'Markdown'
//     ];

//     $ch = curl_init($url);
//     curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
//     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//     $result = curl_exec($ch);
//     $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
//     curl_close($ch);
//     if ($httpCode !== 200) {
//         file_put_contents(__DIR__ . '/telegram_error.log', "Error sending Telegram message:\nResult: $result\nHTTP Code: $httpCode\nError: $error\n\n", FILE_APPEND);
//     }
// }
function sendTelegramAlert($botToken, $chatIds, $message)
{
    foreach ($chatIds as $chatId) {
        $url = "https://api.telegram.org/bot{$botToken}/sendMessage";
        $data = [
            'chat_id' => $chatId,
            'text'    => $message,
            // 'parse_mode' => 'Markdown', // Uncomment if using Markdown safely
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($httpCode !== 200 || !$result) {
            $logMessage = "Error sending Telegram message:\nResult: $result\nHTTP Code: $httpCode\nError: $error\n\n";
            file_put_contents(__DIR__ . '/telegram_error.log', $logMessage, FILE_APPEND);
        }
    }
}

