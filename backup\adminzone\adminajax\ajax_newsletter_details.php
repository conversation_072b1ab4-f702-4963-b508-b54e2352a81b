<?php
//print_r($_POST); die();
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $newsletter_id;
	public $newsletter_title;
	public $newsletter_date;
	public $upload_file;
	public $path_upload_file;
	public $arr_newsletter_file;
	public $arr_newsletter_title, $newsletter_title_edit,$mark_new,$arr_mark_new, $mark_new_edit, $maxfilesize, $pdffile_edit;
	
			
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		
		//--- Set Current Module Name ---
		$this->module_name = "Ajax Newsletter Details";
		$this->cur_page_url = "ajax_newsletter_details.php";
		$this->list_page_url = "ajax_newsletter_details.php";
				
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
			
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		$this->path_upload_file = "../../newsletter_files/";
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		$this->maxfilesize = 4*1024*1024;
								
		//--- Execute a Task ---
		
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createnewsletter();
				break;
			case 2:
				//--- Edit Record
				
				$this->editnewsletter();
				break;
			default:
				$this->task = 0;
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
			
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->newsletter_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->newsletter_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->newsletter_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->newsletter_id = 0;
			}
		}
		
		//echo $this->newsletter_id;die("tendetid");
		
		return 1;
	}


	//==== Create New Record ====
	public function createnewsletter()
	{	
		//--- Get Record Id ---
		$this->getRecordId();
		
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editnewsletter()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		if ($this->newsletter_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			
			//--- Form is posted
			$this->readFormData();
			$i = $this->validateFormData();
			if($i == 0)
			{
				//--- do nothing
			}
			else
			{
				
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		$this->arr_newsletter_title = "";
		$this->arr_newsletter_date = "";	
		$this->arr_newsletter_file = "";
		$this->newsletter_id = "";	
		$this->arr_mark_new = "";	
		return 1;
	}

	//=== Read Form Data ====
	
	public function readFormData()
	{ 	
	   if(isset($_POST["newsletter_title"]))
		{
			$this->arr_newsletter_title = CommonFunctions::replaceChars($_POST["newsletter_title"], 0);		
			
			$val_cal = $_POST["newsletter_date"];
			$arr_date = explode("-",$val_cal);
			
			$this->arr_newsletter_date = $arr_date[2]."-".$arr_date[0]."-".$arr_date[1];
			
			$file = "";
			if(isset($_FILES['pdffile']['name'])){
				 $file = basename($_FILES["pdffile"]["name"]);
			}
			$this->arr_newsletter_file = $file;
			$this->arr_mark_new = $_POST['mark_new'];
			//$this->task =  $_POST['task'];
		
		}
		//print_r($this->arr_newsletter_date);exit();					
		return 1;
	}	

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		//$file_ext = "jpg,jpeg,pdf,doc,docx,xsl,xlsx";
		$file_ext = "pdf";
		
		if($this->task == 1){
		    
			if($myerr == "")	
			{
				if($this->arr_newsletter_title == "")
				{
					$myerr = "Please specify title.";
				}
				else if($this->arr_newsletter_date == "")
				{
					$myerr = "Please select date.";
				}
				else if(empty($_FILES['pdffile']) || empty($_FILES['pdffile']['name']))
				{
					$myerr = "Please Browse a file.";
				}
				else if(!empty($_FILES['pdffile']))
				{
				    //echo $_FILES['pdffile']['size'];echo "<br>";
				    //echo $this->maxfilesize;
				    //die("ss");
					if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['pdffile']['name']), $file_ext) == 0)
					{
						$myerr = "Un-supported file format , please upload files type - " . $file_ext;
					}
					
					if(($_FILES['pdffile']['size'] > $this->maxfilesize))
					{
					    die("ss");
						$myerr = "File size should not be more than 4MB";
					}
				}
				/*else if(($_FILES['pdffile']['size'] > $this->maxfilesize))
				{
					$myerr = "File size should not be more than 4MB";
				}*/
			}
		}
		if($this->task == 2){
			if($myerr == "")	
			{
				if($this->arr_newsletter_title == "")
				{
					$myerr = "Please specify title.";
				}
				else if(!empty($_FILES['pdffile']))
				{
					if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['pdffile']['name']), $file_ext) == 0)
					{
						$myerr = "Un-supported file format , please upload files type - " . $file_ext;
					}
					
					if(($_FILES['pdffile']['size'] > $this->maxfilesize))
					{
						$myerr = "File size should not be more than 4MB";
					}
				}
			}			
		}
			
		if($myerr != "")
		{
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			//$myerr = "";
			return 1;
		}
	}

	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction 
		$this->link_id->autocommit(FALSE);
			
		if($this->task == 1)
		{
			
				if(isset($_FILES["pdffile"]["name"]) && $_FILES["pdffile"]["name"] !="")
				{  
				
					$file_name="";
					$ext = strtolower(substr(strrchr($_FILES["pdffile"]["name"], "."), 1));
					$name = strtolower(substr($_FILES["pdffile"]["name"], 0, strpos($_FILES["pdffile"]["name"], ".")));
					$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
					$path = $this->path_upload_file . $file_name;
									
					move_uploaded_file($_FILES["pdffile"]["tmp_name"], $path);
					
					//--- Insert Data
					$tmpSql = "INSERT INTO tbl_newsletter(rec_id, heading, attachmemt, rec_date, is_new)" 
							. " VALUES(null, '" . addslashes($this->arr_newsletter_title) . "'" 
							. ", '" . $file_name . "'"
							. ", '" . $this->arr_newsletter_date . "'"
							. ", '". $this->arr_mark_new . "')";
							
					//echo $tmpSql;die();					
									
					$rs = $this->link_id->query($tmpSql);

					if($this->link_id->affected_rows == -1)
					{								
                        $myerr = "Data could not be saved";				
                        $this->link_id->rollback();			
                        echo $myerr;
                        exit();
					}
					else
			        {
        				$this->link_id->commit();
        				$_SESSION["app_message"] = "Data saved successfully";
        				echo "1";
        				exit();				
			        }
				}
		}
		else if($this->task == 2)
		{
			$upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "tbl_newsletter", "attachmemt", "","rec_id=".$this->newsletter_id);
			
			$this->path_upload_file.$upload_old_file;
			
			
			
			if(isset($_FILES['pdffile']['name']))
			{
				if(file_exists($this->path_upload_file.$upload_old_file))
				{
					@unlink($this->path_upload_file.$upload_old_file);
				} 
				
				$file = $this->file_upload();
			}
			else
			{
				$this->pdffile_edit = $upload_old_file;
			}
			
			 $tmpSql = "UPDATE tbl_newsletter SET " 					
					. "  heading = '" . addslashes($this->arr_newsletter_title) ."'"
					//. ", details = '" . addslashes($this->newsletter_details_edit) ."'"
					. ", rec_date = '" . $this->arr_newsletter_date ."'"
					. ", attachmemt = '" . $this->pdffile_edit ."'"
					. ", is_new = '" . $this->arr_mark_new . "'"
					. "  WHERE rec_id = " . $this->newsletter_id;
					
		   //echo $tmpSql;
		   //die("in to edit else");	
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();
			}
				
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			echo '2';
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
		
  //Upload File
   public function file_upload()
   {
		if(isset($_FILES['pdffile']['name']))
		{	
			$file_name="";
			
			$ext = strtolower(substr(strrchr($_FILES["pdffile"]["name"], "."), 1));
			$name = strtolower(substr($_FILES["pdffile"]["name"], 0, strpos($_FILES["pdffile"]["name"], ".")));
			$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
			$path = $this->path_upload_file . $file_name;
				
		   //echo"aaa";die();
			if(move_uploaded_file($_FILES['pdffile']['tmp_name'], $path))
			{
				$this->pdffile_edit = $file_name;
			}
			else
			{
				$myerr = "File couldn't be uploaded";
			}
		}
	}
}

$objCurPage = new CurrentPage();
?>