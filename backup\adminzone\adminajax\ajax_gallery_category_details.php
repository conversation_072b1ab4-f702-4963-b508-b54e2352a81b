<?php
//print_r($_POST); die();

require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../includes/resize-class.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;		// Database Link
	
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $sortby;			//--- sortby: 1/2/3/4...    
	public $sortmode;		//--- sortmode: asc/desc
	public $crec;			//--- current record set position
	public $app_config;		// Application Configuration Settings
	
	public $cat_id;
	public $cat_name;
	public $cat_photo;
	public $file_path;
	public $arr_cat_file;
	public $arr_cat_name, $cat_name_edit, $maxfilesize;
	
			
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		$this->app_config=$config;
		
		//--- Set Current Module Name ---
		$this->module_name = "Ajax Category Details";
		$this->cur_page_url = "ajax_gallery_category_details.php";
		$this->list_page_url = "gallery_category_details.php";
				
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");
			
		//--- Get Sort Index and Sort Order
		$this->sortby = CommonFunctions::getSortIndex(1);
		$this->sortmode = CommonFunctions::getSortDirection("asc");
		
		$this->maxfilesize = 2*1024*1024;
		$this->path_cat_photo = "../../gallery_cat_images/";
				
		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
								
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createPart();
				break;
			case 2:
				//--- Edit Record
				$this->editPart();
				break;
			default:
				$this->task = 0;
				exit();
		}

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
			
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		if($this->task == 1)
		{
			$this->cat_id = 0;
		}
		else if($this->task == 2)
		{
			if(isset($_GET["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_GET["recid"], 0);
			}
			else if(isset($_POST["recid"]))
			{
				$this->cat_id = CommonFunctions::replaceChars($_POST["recid"], 0);
			
			}
			else
			{
				$this->cat_id = 0;
			}
		}
		
		//echo $this->cat_id;die("tendetid");
		
		return 1;
	}


	//==== Create New Record ====
	public function createPart()
	{
	
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}


	//==== Edit Existing Record ====
	public function editPart()
	{
		//--- Get Record Id ---
		$this->getRecordId();
         	
		if ($this->cat_id == 0)
		{
			$_SESSION['app_error'] = "Record not found...";
			exit();
		}

		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]) && $_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->saveData();
			}
		}

		return 1;
	}

	//=== Initialize Form Data ====
	public function initFormData()
	{
		$this->arr_cat_name = array();
	    $this->arr_cat_file = array();
		$this->cat_id = "";		
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{ 	
	   //echo "aaa";die();
	   if(isset($_POST["cat_name"]))
		{
			$i = 0;
			foreach($_POST["cat_name"] as $val)
			{				
				$this->arr_cat_name[$i] = $val;				
				$i++;
			}
		}
		
		if($this->task == 1)
		{
			$i = 0;
			foreach($_POST["cat_file_HX"] as $val)
			{
				if(!empty($val))
				{
					if($i > 0)
					{
						$this->arr_cat_file[$i] = $val;
					}
				}
				$i++;
			}		
		}
		
		//$this->cat_id= intval(CommonFunctions::replaceChars($_POST["cat_id"], 0));
			
		
		if($this->task == 2)
		{
			$this->cat_name_edit = CommonFunctions::replaceChars($_POST["cat_name_edit"], 0);
			if(isset($_POST["cat_file_edit"]))
			{
				$this->cat_file_edit = $_POST["cat_file_edit"];			
			}	
			
		}
							
		return 1;
	}	

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		//die("aaaa");			
		$myerr = "";
		$file_ext = "jpg,jpeg,png";
		
		if($this->task == 1){
			
			if($myerr == "")	
			{
				for($x = 1; $x<count($this->arr_cat_name); $x++)
				{
					if($this->arr_cat_name[$x] == "")
					{
						$myerr = "Please specify category name -".$x;
						break;
					}
					else if(DatabaseManager::iexistsInTable($this->link_id, "tbl_photo_category", "cat_id <> " . $this->cat_id . " and cat_name = '" . $this->arr_cat_name[$x] . "' ") == 1)
					{
						$myerr = "Category Name: ".$this->arr_cat_name[$x]. " already exists.";
					}
				}
				
			}
			
					
			if($myerr == "")	
			{								
				for($i=1; $i<=count($this->arr_cat_file); $i++)
				{			  
				   if(empty($_FILES['cat_file_' . $i]['name']) && $this->task == 1)
					{
						$myerr = "Please upload file -".$i;
					}			   
					 if(!empty($_FILES['cat_file_' . $i]['name']))
					 {
						if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['cat_file_' . $i]['name']), $file_ext) == 0)
						{
							$myerr = "Un-supported file format , please upload files type - " . $file_ext;
						}
						else if(($_FILES['cat_file_' . $i]['size'] > $this->maxfilesize))
						{
							$myerr = "File size should not be more than 2MB";
							break;
						}
					}	
					
				}
			}
			
	    }
		
		if($this->task == 2){
			
			if($myerr == "")	
			{
					if($this->cat_name_edit == "")
					{
						$myerr = "Please specify category name";
					}
				
			}	
			if($myerr == "")
			{
				if(!empty($_FILES['cat_file']['name']))
				{
					if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['cat_file']['name']), $file_ext) == 0)
					{
						$myerr = "Un-supported file format, please upload files type - " . $file_ext;
					}
					
					else if(($_FILES['cat_file']['size'] > $this->maxfilesize))
					{
						$myerr = "File size should not be more than 2MB";
					}
				}		
			}
		
	    }

		if($myerr != "")
		{
			echo $myerr;
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	//==== Save Data ====
	public function saveData()
	{
		$myerr = "";
		
		//--- Begin Transaction 
		$this->link_id->autocommit(FALSE);
			
		if($this->task == 1)
		{
			for($i=1; $i<=count($this->arr_cat_name); $i++)
			{
				if(isset($_FILES["cat_file_" . $i]["name"]) && $_FILES["cat_file_" . $i]["name"] !="")
				{   
					$file_name="";
					$ext = strtolower(substr(strrchr($_FILES["cat_file_" . $i]["name"], "."), 1));
					$name = strtolower(substr($_FILES["cat_file_" . $i]["name"], 0, strpos($_FILES["cat_file_" . $i]["name"], ".")));
					
					$path = $this->path_cat_photo . str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
					$medium_path = $this->path_cat_photo ."medium_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
					$small_path = $this->path_cat_photo ."thumb_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
										
					$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
										
					move_uploaded_file($_FILES["cat_file_" . $i]["tmp_name"], $path);
					
					if($ext="jpg" || $ext="jpeg" || $ext="png"){
					
					$resizeObj = new resize($path);
					
					$resizeObj -> resizeImage(600, 450 ,'auto');
					$resizeObj -> saveImage($path, 100);
					
					//$resizeObj -> resizeImage(151, 130 ,'crop');
					$resizeObj -> resizeImage(228, 151 ,'crop');
					$resizeObj -> saveImage($medium_path, 100);	
									
					$resizeObj -> resizeImage(120, 90 ,'crop');
					$resizeObj -> saveImage($small_path, 100);
					
					}
					
					//--- Insert Data
					$tmpSql = "INSERT INTO tbl_photo_category(cat_id, cat_name, cat_photo)" 
							. " VALUES(null, '" . $this->arr_cat_name[$i] . "'" 
							. ", '" . $file_name . "'"
							. ")";
					//echo $tmpSql;die();										
					$rs = $this->link_id->query($tmpSql);
					
					if($this->link_id->affected_rows == -1)
					{								
						break;
					}			
				}
			
			}//exit;
			
			if($myerr != "")
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();				
			}			
			else
			{
				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data saved successfully";
				echo '1';
				exit();				
			}
			
		}
		else if($this->task == 2)
		{
			$upload_old_file = DatabaseManager::igetDataFromTable($this->link_id, "tbl_photo_category", "cat_photo", "","cat_id=".$this->cat_id);
		
			if(isset($_FILES['cat_file']['name']))
			{
				/*if($upload_old_file != "")
				{
					CommonFunctions::removeFiles($this->path_cat_photo, $upload_old_file);	
					CommonFunctions::removeFiles($this->path_cat_photo, "medium_".$upload_old_file);			
					CommonFunctions::removeFiles($this->path_cat_photo, "thumb_".$upload_old_file);
				}*/
				
				if($upload_old_file != "")
				{
					if(file_exists($this->path_cat_photo.$upload_old_file))
					{
						unlink($this->path_cat_photo.$upload_old_file);
						unlink($this->path_cat_photo."thumb_". $upload_old_file);
						unlink($this->path_cat_photo."medium_". $upload_old_file);
					}
				}
				
				$this->file_upload();
			}
			
			$tmpSql = "UPDATE tbl_photo_category SET " 					
					. "  cat_name = '" . addslashes($this->cat_name_edit) ."'"
					. ", cat_photo = '" . $this->cat_file_edit ."'"
					. "  WHERE cat_id = " . $this->cat_id;
					
		   //echo $tmpSql	;die("ddd");	
		
			$rs = $this->link_id->query($tmpSql);
			
			if($this->link_id->affected_rows == -1)
			{
				$myerr = "Data could not be saved";				
				$this->link_id->rollback();			
				echo $myerr;
				exit();
			}	
			else
			{				
				$this->link_id->commit();
				$_SESSION["app_message"] = "Data successfully updated";
				echo '1';
				exit();
			}
				
		}
		
		//--- In case of any error set the Session variable ---
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			echo '2';
			exit();
			return 0;
		}
		else
		{
			return 1;
		}
	}
		
  //Upload File
   public function file_upload()
   {
		if(isset($_FILES['cat_file']['name']))
		{	
		   //echo"aaa";die();
		    $file_name="";
			$ext = strtolower(substr(strrchr($_FILES["cat_file"]["name"], "."), 1));
			$name = strtolower(substr($_FILES["cat_file"]["name"], 0, strpos($_FILES["cat_file"]["name"], ".")));
			$path = $this->path_cat_photo . str_replace(" ", "_", $name) . "_" .strtotime("now") . "." . $ext;
			$medium_path = $this->path_cat_photo ."medium_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
			$small_path = $this->path_cat_photo ."thumb_" . str_replace(" ","_", $name) . "_" . strtotime("now") . "." . $ext;
										
			$file_name = str_replace(" ", "_", $name) . "_" . strtotime("now") . "." . $ext;
			
			if(move_uploaded_file($_FILES['cat_file']['tmp_name'], $path))
			{
				$this->cat_file_edit = $file_name;
				
				$resizeObj = new resize($path);
				$resizeObj -> resizeImage(600, 450 ,'auto');
				$resizeObj -> saveImage($path, 100);
				
				//$resizeObj -> resizeImage(151, 130 ,'crop');
				$resizeObj -> resizeImage(228, 151 ,'crop');
				$resizeObj -> saveImage($medium_path, 100);
				
				$resizeObj -> resizeImage(120, 90 ,'crop');
				$resizeObj -> saveImage($small_path, 100);
				
			}
			else
			{
				$myerr = "File couldn't be uploaded";
			}
		}
		
	}
	
}

$objCurPage = new CurrentPage();
?>