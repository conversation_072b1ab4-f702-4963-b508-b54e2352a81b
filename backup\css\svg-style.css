@font-face {
    font-family: 'icomoon';
    src:    url('../fonts/icomoondaa1.eot?kqr4pl');
    src:    url('../fonts/icomoondaa1.eot?kqr4pl#iefix') format('embedded-opentype'),
        url('../fonts/icomoondaa1.ttf?kqr4pl') format('truetype'),
        url('../fonts/icomoondaa1.woff?kqr4pl') format('woff'),
        url('../fonts/icomoondaa1.svg?kqr4pl#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-avatar:before {
    content: "\e900";
}
.icon-command:before {
    content: "\e901";
}
.icon-logo:before {
    content: "\e902";
}
.icon-open-book:before {
    content: "\e903";
}
.icon-pulse:before {
    content: "\e904";
}

