<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
require_once("../php_mailer/class.phpmailer.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link

	public $app_config;		// Application Configuration Settings

	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $search_condition;
	public $email, $rid;
	public $idz;
	public $mail_subject;
	public $mail_body;
	public $maxfilesize;
	public $alumni_email;
	public $alumni_id;
	public $mail_attach;
	public $mail_attach_new;
	public $saved;
	public $mail_all;
	public $admin_mail;
	public $status;
	public $uploaded_document;
	public $uploaded_doc_edit;

	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module Name ---
		$this->module_name = "Alumni Mail";
		$this->cur_page_url = "alumni_mail_all.php";
		$this->list_page_url = "alumni.php";
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		UserAccess::checkUserLogin('', "admin");
		
		$this->maxfilesize = 1*1024*1024;
		//--- Get Record Position (crec) passed to this page ----
		$this->getRecordPostion();

		//--- Get Task Type (Add/Edit) and Current Record Id ---
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createMails();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				die("_");
		}	
	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	
		//==== Get Record Position (crec) passed to this page ====
	public function getRecordPostion()
	{
		if(isset($_POST["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_POST["crec"], 0));}
		else if(isset($_GET["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_GET["crec"], 0));}
		else
			{$this->crec=0;}
		
		return 1;
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		
		return 1;
	}

	//==== Get Current Record Id ====
	public function getRecordId()
	{
	
		if(isset($_GET["search_condition"]))
		{
			$this->search_condition = $_GET["search_condition"];
		}
		else if(isset($_POST["search_condition"]))
		{
			$this->search_condition = $_POST["search_condition"];
		}
		
		if(isset($_GET["mailall"]))
		{
			$this->mail_all = $_GET["mailall"];
		}
		
		//echo $this->search_condition;			
		$tmpSql = "SELECT alumni_id, email FROM tbl_alumni WHERE 1 > 0 " . $this->search_condition;	
		//echo $tmpSql;
		
		$rs = $this->link_id->query($tmpSql);
					
		if((!$rs) || (!($rec = $rs->fetch_array())))
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			do
			{
				$this->alumni_id = $rec["alumni_id"];
				//$this->alumni_email = $rec["email"];
				if($rec["email"] != "")
				{
					if($this->alumni_email != "")
					{
						$this->alumni_email = $this->alumni_email . ",";
					}
						
						$this->alumni_email = $this->alumni_email . $rec["email"];
						
				}
				
			}
			while($rec = $rs->fetch_array());
		}
	
		return 1;
	}

	//==== Create New Record ====
	public function createMails()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->sendMail();
			}
		}

		return 1;
	}
	
	//=== Initialize Form Data ====
	public function initFormData()
	{
		
		$tmpSql = "SELECT alumni_id, email FROM tbl_alumni WHERE 1 > 0 " . $this->search_condition;	
		//echo $tmpSql;
		
		$rs = $this->link_id->query($tmpSql);
					
		if((!$rs) || (!($rec = $rs->fetch_array())))
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			do
			{
				$this->alumni_id = $rec["alumni_id"];
				//$this->alumni_email = $rec["email"];
				if($rec["email"] != "")
				{
					if($this->alumni_email != "")
					{
						$this->alumni_email = $this->alumni_email . ",";
					}
						
						$this->alumni_email = $this->alumni_email . $rec["email"];
				}
				
			}
			while($rec = $rs->fetch_array());
		}
		
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{
		if(isset($_POST["mail_subject"]))
		{
			$this->mail_subject = $_POST["mail_subject"];
		}
		
		if(isset($_POST['mail_body']))
		{
			$this->mail_body = $_POST['mail_body'];
		}
				
		$path = "../mail_attachment/"; 
			
			if(isset($_FILES['mail_attach']) && trim(($_FILES['mail_attach']['name']) != ""))
			{
				
				$tmp_name = str_replace(" ", "_", CommonFunctions::replaceChars($_FILES['mail_attach']['name'], 12));
				
				$pos = strrpos($tmp_name, ".");
			
				$tmp_name = substr($tmp_name, 0, $pos); 
				
				$this->uploaded_document = $path .$tmp_name."_". ".".substr(strrchr($_FILES['mail_attach']['name'], '.'), 1);
				
								
				if(move_uploaded_file($_FILES['mail_attach']['tmp_name'], $this->uploaded_document))
				{
				}
				else
				{
					$myerr="Document couldn't be uploaded";
				}
				
								
				$this->uploaded_doc_edit = $tmp_name . "_" . ".".substr(strrchr($_FILES['mail_attach']['name'], '.'), 1);
			}
			
			$this->saved = false;
				
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";

		if($this->mail_subject == "")
		{
			$myerr = "Subject of Mail is not specified";
		}
		else if($this->mail_body == "")
		{
			$myerr = "Mail Body is not specified";
		}
		
		if($myerr == "")
		{
			if($_FILES['mail_attach']['name'] != "")
			{
				if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['mail_attach']['name']), "doc,docx,txt,pdf,gif,jpg,jpeg,png") != 1)
				{
					$myerr = "Un-supported image format";
				}
				else if(($_FILES['mail_attach']['size'] > $this->maxfilesize))
				{
					$myerr = "File size should not be more than 1 MB";
				}
			}
		}
		
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}
	
	public function sendMail()
	{	
				
		/*$tmpSql = "SELECT alumni_id, email FROM tbl_alumni WHERE 1 > 0 " . $this->search_condition;	
		
		
		$rs = $this->link_id->query($tmpSql);
					
		if((!$rs) || (!($rec = $rs->fetch_array())))
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			do
			{
				$this->alumni_id = $rec["alumni_id"];
				if($rec["email"] != "")
				{
					if($this->alumni_email != "")
					{
						$this->alumni_email = $this->alumni_email . ",";
					}
						
						$this->alumni_email = $this->alumni_email . $rec["email"];
				}
				
			}
			while($rec = $rs->fetch_array());
		}*/
		
		$this->status = DatabaseManager::igetDataFromTable($this->link_id, "tbl_alumni", "status", "","alumni_id=".$this->alumni_id);
		//echo $this->status;die();

		$this->admin_mail = DatabaseManager::igetDataFromTable($this->link_id, "admin_details", "user_email", "", "user_id = 1");
		
		//echo $this->admin_mail;die();
		$headers = "MIME-Version: 1.0\r\n";
		$headers .= "Content-type: text/html; charset=utf-8\r\n";		
		
		$message = '';
		$body = '';
		
		$body .= "<p>" . stripslashes($this->mail_body). "</p>";
		
		$subject = $this->mail_subject;
		
		$message .= $body;	
		//echo $this->alumni_email;die();
		
		try 
		{
			$mail = new PHPMailer();
			$body = $message;
			$body = str_replace("[\]",'',$body);			
			$mail->SMTPDebug  = 0;                     // enables SMTP debug information (for testing)
			
			
			$emailsExploded = array();
			$emailsExploded = explode(",", $this->alumni_email);
			
			//print_r($emailsExploded);exit;
			//If the array isn't empty, loop through
			if(!empty($emailsExploded)){				
				foreach($emailsExploded as $emailAddress){			
					$mail->AddAddress(trim($emailAddress));
				}
			} else{
				//This should not be the case.
				//throw new Exception('No emails found!');
			}
						
			$mail->SetFrom($this->admin_mail, "Admin");					
			$mail->AddReplyTo($this->admin_mail, "");					
			$mail->Subject    = $subject;					
			$mail->AltBody    = "To view the message, please use an HTML compatible email viewer!"; // optional, comment out and test					
			$mail->MsgHTML($body);					
			//$address = $this->alumni_email;
			//echo $address;die();
			//$mail->AddAddress($address, "");
			/*$File = $this->mail_attach;*/
			if($this->uploaded_document !="")
			{
				$File = $this->uploaded_document;
				
				$mail->AddAttachment($File);	
			}	
			
									
		//echo ($headers . "<br/>" . $subject . "<br/>" . $message . "<br>" . $address. "<br>" . $this->admin_mail . "<br>" ) . "<br />" . $File; 
		//die();
			if($this->alumni_email != "" && $this->status==1)
			         {
				       $mail->Send();
				       $_SESSION['app_message'] = "Mail sent successfully";
					   header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
					   exit();
			          }
					  else
			         {
				      $_SESSION['app_error'] = "Mail couldn't be sent";
			         }
				   
		} 
		catch (phpmailerException $e) 
		{				  
		  $e->errorMessage();
		  echo "Mailer Error: " . $mail->ErrorInfo;
		}
		return 1; 
	  }
		//end of class
	
}

$objCurPage = new CurrentPage();

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<link href="css/admin.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script type="text/javascript" src="js/jquery-1.10.2.js"></script>
<script type="text/javascript" src="js/jquery-ui.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->
<link href="css/bootstrap.min.css" rel="stylesheet" />

<link href="css/colorbox.css" rel="stylesheet" />
<link href="css/popbox.css" rel="stylesheet" type="text/css" />
<link href="css/accrodiantree.css" rel="stylesheet" />
<!--<script type="text/javascript" src="../js/jquery-1.10.1.min.js"></script>-->
<script type="text/javascript" src="js/jquery-ui-1.10.2.custom.min.js"></script>
<script type="text/javascript" src="js/webwidget_vertical_menu.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<script type="text/javascript" src="js/jquery.colorbox.js"></script>

<script language="javascript" type="text/javascript">

function validateSendMail()
{
	$('#err').html('');
	if($('#mail_subject').val() == "")
	{
		$('#err').html('');
		$('#err').text('Please specify Subject');	
		return false;
	}
	else if($('#mail_body').val() == "")
	{
		$('#err').html('');
		$('#err').html('Please specify Mail body');	
		return false;
	}
	else{
		$('#err').html('Mail sent successfully');
		return true;
	}
}

</script>
<style>
.emailBox{
	border: 1px solid #666666;
    color: #333333;
    font-size: 16px;
    height: 20px;
    outline: medium none;
    padding: 2px;
    width: 220px;
}
.msgBox{
	border: 1px solid #666666;
    color: #333333;
    font-size: 16px;
    height: 300px;
    outline: medium none;
    padding: 2px;
    width: 500px;
}
</style>

</head>

<body>
<form action="alumni_mail_all.php" method="post" enctype="multipart/form-data" name="form1" id="form1" onsubmit="return validateSendMail();">
  <table width="600" border="0" align="left" cellpadding="2" cellspacing="0" class="dialogBaseShadow">
    <tr>
      <td><table width="100%" align="center" cellspacing="0" class="dialogBaseShadow">
        <tr>
          <td><table width="100%" cellspacing="0" class="dialogBase">
            <tr>
              <td class="dialogHeader"> Alumni Mail </td>
            </tr>
            <tr>
              <td align="right" valign="top" ></td>
            </tr>
            <tr>
              <td align="right"></td>
            </tr>
            <tr>
              <td valign="top"><table width="100%" border="0" cellspacing="0" cellpadding="3" class="formTextWithBorder">
                <tr>
                  <td valign="bottom">&nbsp;</td>
                  <td align="left" id="err" style="color:#C92C23; font-weight:bold; font-size:14;"><?php CommonFunctions::displayErrMsg(); ?></td>
                </tr>
                <tr>
                  <td valign="bottom">To:</td>
                  <td align="left" id="err" style="color:#C92C23; font-weight:bold; font-size:14;">All</td>
                </tr>
                <tr>
                  <td height="20" valign="bottom">Subject :</td>
                  <td height="20" align="left">
                     <input type="text" class="emailBox" name="mail_subject" id="mail_subject" size="50" value="<?php echo $objCurPage->mail_subject; ?>" />
                     <input type="hidden" name="idz" id="idz" value="<?=$objCurPage->idz;?>"/>                  </td>
                </tr>
                
                <tr>
                  <td height="20" align="left">Attachment :</td>
                  <td height="20">
                            <input type="file" name="mail_attach" id="mail_attach" size="40" value="" />
                            <br />
                            <small style="font-family:Verdana, Arial, Helvetica, sans-serif; font-size:11px;">(File Type: .doc, .docx, .txt, .pdf, .jpg, .jpeg, Size: max 1MB)                          </small></td>
                </tr>
                
                <tr>
                  <td height="20" align="left" valign="top">Message : </td>
                  <td height="20" align="left"><textarea name="mail_body" id="mail_body" class="msgBox" cols="50" rows="20"><?php echo $objCurPage->mail_body; ?></textarea></td>
                </tr>
                <tr>
                  <td align="left" valign="top">&nbsp;</td>
                  <td align="left"><input type="submit" name="cmdSend" id="cmdSend" value="send" class="btn btn-default" />
                            <input name="postMe" type="hidden" id="postMe" value="Y" />
                            <input type="hidden" name="search_condition" id="search_condition" value="<?php echo $objCurPage->search_condition;?>" /></td>
                </tr>
              </table></td>
            </tr>
            
          </table></td>
        </tr>
      </table></td>
    </tr>
  </table>
</form>
</body>
</html>
