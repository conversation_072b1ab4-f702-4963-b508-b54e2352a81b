<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
require_once('../includes/class.phpmailer.php');
@session_start();
/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link
	public $module_name;	// Current Module Name
	public $app_config;		// Application Configuration Settings
	public $no_menu;
	public $cha_user_type;
	public $email_id;
	public $user_name;
	public $password;
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		//--- Set Current Module Name ---
		$this->module_name = "Forgot Password";
		$this->cur_page_url = "forgotpassword.php";
		$this->list_page_url = "index.php";
		//--- Application Configuration Settings ---
		$this->app_config = $config;

		//--- Don't show menu
		$this->no_menu = "Y";
		
		//====If Login button is not pressed=====
		if(!(isset($_POST["cmdLogin"])))
		{
			$this->initFormData();
		}
		else{
			$this->readFormData();
			//====Validates user's input
			if($this->validateFormData()==0){
				//==Do nothing
			}
			else
			{
				//====Proceeds to login verification
				//$this->checkUserType();
				$this->updatePassword();
			}
		}
	}
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	//=== Initialize Form Data ====
	public function initFormData()
	{
		$this->email_id = "";
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{
		$this->email_id = CommonFunctions::replaceChars($_POST["mail_id"], 0);
		return 1;
	}
	
	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		if($this->email_id == "")
		{
			$myerr = "Please specify Email Id.";
		}
		
		if($this->email_id !=""){
			
			if(!filter_var($this->email_id, FILTER_VALIDATE_EMAIL) === false){
				if(DatabaseManager::iexistsInTable($this->link_id,"admin_details","user_email = '" . $this->email_id . "'")==0){
					$myerr = "Please specify registered Email Id.";
				}
			}else{
				$myerr = "Please specify valid email";	
				
			}
		}
		
		
		if($myerr != "")
		{

			$_SESSION['app_error'] = $myerr;
			
			//echo $_SESSION['app_error'];exit;
			//header("Location: " . $this->app_config["APP_INFO_MSG_FILE"]);
			//die("");
			return 0;
		}
		else
		{
			return 1;
		}
		
	}
	
	public function updatePassword()
	{
		$myerr = "";
		
		$sql="SELECT user_id, user_name, user_email, user_password 
			  FROM admin_details WHERE user_email = '". $this->email_id ."'";
		
		$rec= $this->link_id->query($sql);
		if(!($rec)||(!($rs= $rec->fetch_array())))
		{
			$myerr = "Email Id does not exists in database. Try again.";
		}
		else
		{	
		
			$this->user_name = $rs["user_name"];
						
			$this->password = CommonFunctions::generateRndChrs(8);
			
			$sql = "update admin_details SET user_password = '" . md5($this->password) . "' WHERE user_email = '". $this->email_id ."'";
			
			$this->link_id->query($sql);	
			
			if($this->link_id->affected_rows == -1){
				$myerr = "Failed to reset password";	
			}else{
				$this->sendMail();
				$_SESSION["app_message"] = "New password has been succefully sent to your email id.";
				header('location: ' . $this->list_page_url);
				die("");
			}
		}
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
		
	}
	
	
	public function sendMail()
	{		
	  	
			$message = '<table width="70%" border="0" align="center" cellpadding="2" cellspacing="1"  
		style="background-color:#DFD7C0;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px">
			  <tr><td>&nbsp;</td></tr>
			  <tr><td align="center"><table width="95%" border="0" align="center" cellpadding="2" cellspacing="1"  
		style="background-color:#FFF;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px" >
			  
			   <tr>
				<td colspan="4">Please find your updted login details:</td>
			   </tr>
			   <tr>
					<td colspan="4">&nbsp;</td>
				 </tr>				  
			  <tr>
				<td width="18%" align="left" valign="top">User Name</td>
				<td width="2%" align="center">:</td>
				<td width="80%" align="left">'.$this->user_name .'</td>
			  </tr>
			  <tr>
				<td align="left" valign="top">Email</td>
				<td align="center">:</td>
				<td align="left">'.$this->email_id.' </td>
			 </tr>';	
			
			 if($this->password!="")
			 {
			   $message .='<tr>
				<td align="left" valign="top">Password</td>
				<td align="center" valign="top">:</td>
				<td align="left" valign="top">'.$this->password.'</td>
				</tr> ';
			 } 
			 $message .= '
			 </table>
			  </td>
			  </tr>
			   <tr><td>&nbsp;</td></tr>
			  </table>';
			
			$to = $this->email_id;
			$subject = "Forgot Password";										
			
			$mail = new PHPMailer(true); // the true param means it will throw exceptions on errors, which we need to catch
			try {
				
						$mail->CharSet = 'utf-8';
						$mail->SMTPDebug = false; // Enables SMTP debug information - SHOULD NOT be active on production servers!
						$mail->SetFrom($this->email_id,'');
						$mail->AddReplyTo($this->email_id, '');
						$mail->AddAddress($to, '');
		
						$mail->Subject = $subject;
						$mail->AltBody = 'To view the message, please use an HTML compatible email viewer!'; // optional - MsgHTML will create an alternate automatically
						$mail->MsgHTML($message);
			
																
						$mail->Send();
				}			
				catch (phpmailerException $e)
				{
				  echo $e->errorMessage(); //Pretty error messages from PHPMailer
				  exit();
				  return 0;
				} 
				catch (Exception $e)
				{
				  echo $e->getMessage(); //Boring error messages from anything else!
				  exit();
				  return 0;
				}
							
				return 1;
    }	
	

}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>User Login</title>
<link href="css/style.css" rel="stylesheet" />
</head>
<body>
<div class="main_container"><!--main_container start here-->
  <div class="login_container"> <!--login_container start here-->
    <div class="admin_logo" style="height:165px;"> 
      <!--<img src="images/logo.png" alt="" style="margin-left:125px;" />
<h1 class="h_txt">
<span class="head_text">Gandhamardan </span>
Industrial Training Institute, Bolangir 
</h1>--> 
    </div>
    <div class="login_frm">
      <form id="form1" action="" method="post">
        <fieldset >
          <legend >Forgot Password</legend>
          <table width="100%">
            <tr>
              <td style="text-align:center; color:#F00; font-size:12px;"><?php CommonFunctions::displayErrMsg(); ?></td>
            </tr>
            <tr>
              <td align="center">Email Id:</td>
            </tr>
            <tr>
              <td>
          <input type="text" id="mail_id" autofocus="autofocus" autocomplete="off"  name="mail_id" placeholder="Your registered Email Id" /></td>
            </tr>
            <tr>
              <td><input name="logMe" type="hidden" id="logMe" value="Y" />
               <input name="cmdLogin" type="button" class="login_button goback" id="cmdLogin" value="GO TO LOGIN" onclick="window.location.href='index.php'" />
                <input name="cmdLogin" type="submit" class="login_button" id="cmdLogin" value="GET PASSWORD" /></td>
            </tr>
            <!--<tr>
              <td><a href="registration.php"  class="lnk">Sign up</a></td>
            </tr>-->
          </table>
        </fieldset>
      </form>
    </div>
  </div>
  <!--login_container ends here--> 
</div>
<!--main_container ends here-->
</body>
</html>