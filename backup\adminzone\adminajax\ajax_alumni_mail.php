<?php
require_once("../../includes/config.inc.php");
require_once("../../includes/class.CommonFunctions.php");
require_once("../../includes/class.DatabaseManager.php");
require_once("../../includes/class.LogonUser.php");
require_once("../../includes/class.UserAccess.php");
require_once("../../includes/interface.AppPage.php");
require_once("../../php_mailer/class.phpmailer.php");
@session_start();

class CurrentPage
{
	public $link_id;	// Database Link

	public $app_config;		// Application Configuration Settings
	public $module_name;	// Current Module Name
	public $task;			// Type of Task (1: Add, 2: Edit, 0: Nothing....)
	public $cur_page_url;	// Current Page URL
	public $list_page_url;	// List Page URL
	public $search_condition;
	public $email, $rid;
	public $idz;
	public $mail_subject;
	public $mail_body;
	public $maxfilesize;
	public $alumni_email;
	public $alumni_id;
	public $mail_attach;
	public $mail_attach_new;
	public $saved;
	public $status;
	public $admin_mail;
	public $uploaded_document;
	public $uploaded_doc_edit;
	public $first_name;
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		
		//--- Set Current Module name ---
		$this->module_name = "Request";
		$this->cur_page_url = "ajax_alumni_mail.php";
		$this->list_page_url = "ajax_alumni_mail.php";
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		$this->getTaskType();
		
		//--- Execute a Task ---
		switch($this->task)
		{
			case 1:
				//--- Add Record
				$this->createMails();
				break;
			default:
				$this->task = 0;
				header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
				exit();
		}	
	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
		//==== Get Record Position (crec) passed to this page ====
	public function getRecordPostion()
	{
		if(isset($_POST["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_POST["crec"], 0));}
		else if(isset($_GET["crec"]))
			{$this->crec = intval(CommonFunctions::replaceChars($_GET["crec"], 0));}
		else
			{$this->crec=0;}
		
		return 1;
	}

	//==== Get Task Type (1:Add, 2:Edit) ====
	public function getTaskType()
	{
		if(isset($_POST["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_POST["task"], 0));
		}
		else if(isset($_GET["task"]))
		{
			$this->task = intval(CommonFunctions::replaceChars($_GET["task"], 0));
		}
		else
		{
			$this->task = 0;
		}
		if($this->task == 0)
		{
			$this->task = 1;
		}
		return 1;
	}


	//==== Get Current Record Id ====
	public function getRecordId()
	{
		
		if(isset($_GET["rid"]))
		{
			$this->idz= $_GET["rid"];
		}
		else if(isset($_POST["idz"]))
		{
			$this->idz = $_POST["idz"];
		}
		if(isset($_GET["search_condition"]))
		{
			$this->search_condition = $_GET["search_condition"];
		}
		else if(isset($_POST["search_condition"]))
		{
			$this->search_condition= $_POST["search_condition"];
		}
		if(isset($_GET["mailall"]))
		{
			$this->mail_all = $_GET["mailall"];
		}
		
		
		$tmpSql = "SELECT alumni_id,  first_name, email FROM tbl_alumni WHERE alumni_id in (" . $this->idz  . ")";	
		//echo $tmpSql;exit;
		$rs = $this->link_id->query($tmpSql);
					
		if((!$rs) || (!($rec = $rs->fetch_array())))
		{
			$_SESSION['app_error'] = "Record not found...";
			header("Location: " . $this->list_page_url . "?sortby=" . $this->sortby . "&sortmode=" . $this->sortmode . "&crec=" . $this->crec );
			die("_");
		}
		else
		{
			do
			{
				$this->alumni_id = $rec["alumni_id"];
				//echo $this->alumni_id;die();
				//$this->status = $rec["status"];
				//echo $this->status;die();
				//$this->email = $rec["email"];
				//echo $this->email;die();
				
				if($rec["email"] != "")
				{
					
					if($this->alumni_email != "")
					{
						$this->alumni_email = $this->alumni_email . ",";
					}
						
						$this->alumni_email = $this->alumni_email . $rec["email"];
						//echo $this->alumni_email;die();
				}
				
				if($rec["first_name"] != "")
				{
					if($this->first_name != "")
					{
						$this->first_name = $this->first_name . ",";
					}
						
						$this->first_name = $this->first_name . $rec["first_name"];
				}
				
			}
			while($rec = $rs->fetch_array());
		}
				
		return 1;
	}
	

	//==== Create New Record ====
	public function createMails()
	{
		//--- Get Record Id ---
		$this->getRecordId();
		
		//---- Check if Form is submitted ----
		if(!isset($_POST["postMe"]))
		{
			//--- Form is not posted
			$this->initFormData();
		}
		else if($_POST["postMe"] == "Y")
		{
			//--- Form is posted
			$this->readFormData();
			if($this->validateFormData() == 0)
			{
				//--- do nothing
			}
			else
			{
				$this->sendMail();
			
			}
		}

		return 1;
	}
	
	//=== Initialize Form Data ====
	public function initFormData()
	{
			//--- Add Mode. Initialize field values to blank or zero or null
			if($this->search_condition!="")
			{
				if(DatabaseManager::iexistsInTable($this->link_id, "tbl_alumni", "alumni_id != 0 ". $this->search_condition." AND email != ''")==0)
				{
					die("No Email address found.");
		
				}
			}
			else if($this->idz !="")
			{
				if(DatabaseManager::iexistsInTable($this->link_id, "tbl_alumni", "alumni_id IN (".$this->idz.") AND email != ''")==0)
				{
					die("No Email address found.");
				}
			}
			
			$this->saved = false;
		return 1;
	}

	//=== Read Form Data ====
	public function readFormData()
	{
		if(isset($_POST["mail_subject"]))
		{
			$this->mail_subject = $_POST["mail_subject"];
		}
		
		if(isset($_POST['mail_body']))
		{
			$this->mail_body = $_POST['mail_body'];
		}
		
			$path = "../mail_attachment/"; 
			
			if(isset($_FILES['mail_attach']) && trim(($_FILES['mail_attach']['name']) != ""))
			{
				
				$tmp_name = str_replace(" ", "_", CommonFunctions::replaceChars($_FILES['mail_attach']['name'], 12));
				
				$pos = strrpos($tmp_name, ".");
			
				$tmp_name = substr($tmp_name, 0, $pos); 
				
				$this->uploaded_document = $path .$tmp_name."_". ".".substr(strrchr($_FILES['mail_attach']['name'], '.'), 1);
				
								
				if(move_uploaded_file($_FILES['mail_attach']['tmp_name'], $this->uploaded_document))
				{
				}
				else
				{
					$myerr="Document couldn't be uploaded";
				}
				
								
				$this->uploaded_doc_edit = $tmp_name . "_" . ".".substr(strrchr($_FILES['mail_attach']['name'], '.'), 1);
		
			
			}
		
			$this->saved = false;
				
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";

		
		if($this->mail_subject == "")
		{
			//$myerr = "Subject of Mail is not specified";
			alert("Subject of Mail is not specified");
		}
		else if($this->mail_body == "")
		{
			//$myerr = "Mail Body is not specified";
			alert("Mail Body is not specified");
		}
		
		if($myerr == "")
		{
			if($_FILES['mail_attach']['name'] != "")
			{
				if(CommonFunctions::strInList(CommonFunctions::getFileExtension($_FILES['mail_attach']['name']), "doc,docx,txt,pdf,gif,jpg,jpeg,png") != 1)
				{
					//$myerr = "Un-supported image format";
					alert("Un-supported image format");
				}
				else if(($_FILES['mail_attach']['size'] > $this->maxfilesize))
				{
					//$myerr = "File size should not be more than 1 MB";
					alert("File size should not be more than 1 MB");
				}
			}
		}
				
		if($myerr != "")
		{
			$_SESSION['app_error'] = $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	/*public function validateFormData()
	{
		$myerr = "";
		if($this->captcha_val != $this->captcha){
			$myerr = "Invalid Captcha Code. Please try again";
		}
	    if($myerr!="")
		{			
			echo $myerr;
			return 0;
		}
		else
		{
			return 1;
		}
	}*/
	
	public function sendMail()
	{
	  $this->status = DatabaseManager::igetDataFromTable($this->link_id, "tbl_alumni", "status", "","alumni_id=".$this->alumni_id);
		echo $this->status;die();
		$this->admin_mail = DatabaseManager::igetDataFromTable($this->link_id, "admin_details", "user_email", "", "user_id = 1");
		//echo $this->admin_mail;
		$headers = "MIME-Version: 1.0\r\n";
		$headers .= "Content-type: text/html; charset=utf-8\r\n";
		
		$message = '';
		$body = '';
		
		$body .= "<p>" . stripslashes($this->mail_body) . "</p>";
		$subject = $this->mail_subject;
		$message .= $body;	
		
		try 
		{
			
			$mail = new PHPMailer();
			$body = $message;
			$body = str_replace("[\]",'',$body);			
			//$mail->IsSMTP(); // telling the class to use SMTP
			//$mail->Host       = "mail.yourdomain.com"; // SMTP server
			$mail->SMTPDebug  = 0;                     // enables SMTP debug information (for testing)
													   // 1 = errors and messages
													   // 2 = messages only
			//$mail->SMTPAuth   = $this->app_config["smtp_auth"];                  // enable SMTP authentication
			//$mail->SMTPSecure = $this->app_config["smtp_secure"];                 // sets the prefix to the servier
			//$mail->Host       = $this->app_config["smtp_host"];      // sets GMAIL as the SMTP server
			//$mail->Port       = $this->app_config["smtp_port"];                   // set the SMTP port for the GMAIL server
			//$mail->Username   = $this->app_config["mail_from"];  // GMAIL username
			//$mail->Password   = $this->app_config["mail_pass"];            // GMAIL password
			
			//$mail->SMTPAuth = true;  // authentication enabled
			
			$mail->SetFrom($this->admin_mail, "Admin");					
			$mail->AddReplyTo($this->admin_mail, "");					
			$mail->Subject    = $subject;					
			$mail->AltBody    = "To view the message, please use an HTML compatible email viewer!"; // optional, comment out and test					
			$mail->MsgHTML($body);					
			$address = $this->alumni_email;
			//echo $address;die();
			$mail->AddAddress($address, "");
			//$File = $this->mail_attach;
			//$mail->AddAddress("<EMAIL>", "");												
			//$mail->AddCC("<EMAIL>", 'xyz');			
					
			if($this->uploaded_document !="")
			{
				$File = $this->uploaded_document;
				$mail->AddAttachment($File);	
			}	
										
		  //echo ($headers . "<br/>" . $subject . "<br/>" . $message . "<br>" . $address) . "<br />" . $File ."<br/>";
		  //die();
			
		    if($this->alumni_email != "" && $this->status==1)
			 {
			   $mail->Send();
			   //$_SESSION['app_message'] = "Mail sent successfully";
			   alert("Mail sent successfully");
			
			  }
			  else
			  {
			   //$_SESSION['app_error'] = "Mail couldn't be sent";
			   alert("Mail couldn't be sent");
			  }
				   
		  } 
		/*catch (phpmailerException $e) 
		{				  
		  $e->errorMessage();
		  echo "Mailer Error: " . $mail->ErrorInfo;
		}
		return 1; */
		
		catch (phpmailerException $e)
		{
			echo $e->errorMessage(); //Pretty error messages from PHPMailer
			exit();
			return 0;
		} 
		catch (Exception $e)
		{
			echo $e->getMessage(); //Boring error messages from anything else!
			exit();
			return 0;
		}
		echo "1";
		exit();
				
	  }
		//end of class
  }

$objCurPage = new CurrentPage();
?>