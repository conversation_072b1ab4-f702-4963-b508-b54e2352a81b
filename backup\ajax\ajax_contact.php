<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once('../includes/class.phpmailer.php');
@session_start();

class CurrentPage
{
	public $link_id;	// Database Link

	public $module_name;	// Current Module name
	
	public $app_config;		// Application Configuration Settings
	
	
	public $no_menu;
	public $name, $email, $message;	
	
	
	//=== Class Constructor ===/
	function __construct()
	{
	   
		global $config;		// Global Config Settings
		
		//--- Set Current Module name ---
		$this->module_name = "Request";
		$this->cur_page_url = "ajax_contact";
		$this->list_page_url = "ajax_contact";
		
		//--- Application Configuration Settings ---
		$this->app_config = $config;
		
		
		
		if(!isset($_POST["postMe"]))
		{
			$this->initFormData();
		}
		else if(isset($_POST["postMe"]))
		{
			
			$this->readFormData();
			
			if($this->validateFormData() == 0)
			{
				
			}
			else
			{				
				$this->sendToAdmin();							
			}			
		}	

	}

	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		//DatabaseManager::disconnectDB($this->link_id);
	}
	    
	public function initFormData()
	{
		$this->name = "";
		$this->email = "";
		$this->message = "";
	    
		return 1;
	}
	//=== Read Form Data ====
	public function readFormData()
	{	 
	    $this->name = CommonFunctions::replaceChars($_POST["cname"], 11);	
		$this->email = $_POST["cmail"];
		$this->message = CommonFunctions::replaceChars($_POST["cmessage"], 11);
		
		return 1;
	}

	//==== Validate Form Data (Returns: 1 / 0) ====
	public function validateFormData()
	{
		$myerr = "";
		
	    if($myerr!="")
		{			
			echo $myerr;
			return 0;
		}
		else
		{
		return 1;
		}
	}
	
	public function sendToAdmin()
	{	
	    $secretKey = $this->app_config['SECRET_KEY'];
		$token = $_POST['footer_token'];	

		//$url = 'https://www.google.com/recaptcha/api/siteverify';
		$data = [
			'secret' => $secretKey,
			'response' => $token,
			'remoteip' => $_SERVER['REMOTE_ADDR']
		];
		
		$ch = curl_init('https://www.google.com/recaptcha/api/siteverify');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
		
		// Execute the POST request
		$response = curl_exec($ch);
		
		// Check for cURL errors
		if (curl_errno($ch)) {
			echo 'cURL error: ' . curl_error($ch);
			exit();
			return 0;
		}
		
		curl_close($ch);
		
		// Decode JSON response
		$captchaResponse = json_decode($response);
		// print_r($captchaResponse); //die();
		
		// Check if score is acceptable (adjust threshold as needed)
		if ($captchaResponse->success && $captchaResponse->score >= 0.5) {
            if($this->name != "")
            {
                $message = '<table width="50%" border="0" align="center" cellpadding="2" cellspacing="1"  
                style="background-color:#DFD7C0;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px">
                  <tr><td>&nbsp;</td></tr>
                  <tr><td align="center"><table width="95%" border="0" align="center" cellpadding="2" cellspacing="1"  
                style="background-color:#FFF;font-family:Microsoft Sans Serif,Verdana, Arial, Helvetica, sans-serif; font-size:14px" >
                  
                   <tr>
                   	<td colspan="4">Please find my contact details bellow:</td>
                   </tr>
                   <tr>
                		<td colspan="4">&nbsp;</td>
                  	 </tr>				  
                  <tr>
                	<td align="left" valign="top">Name</td>
                	<td align="center">:</td>
                	<td align="left">'.$this->name.'</td>
                  </tr>';
                 
                 $message .='<tr>
                	<td align="left" valign="top">E-Mail ID</td>
                	<td align="center">:</td>
                	<td align="left">'.$this->email.' </td>
                 </tr>';
                 if($this->message!="")
                 {
                 $message .= '<tr>
                	<td align="left" valign="top">Message</td>
                	<td align="center">:</td>
                	<td align="left">'.nl2br($this->message).'</td></tr>';
                 }
                 
                 $message .='</table>
                  </td>
                  </tr>
                   <tr><td>&nbsp;</td></tr>
                  </table>';
    				  
    				
				$to = "<EMAIL>";
								
				$subject = "Enquiry - " . $this->name;										
				
				$mail = new PHPMailer(true); // the true param means it will throw exceptions on errors, which we need to catch
    				
    				
				//$mail->IsSMTP(); // telling the class to use SMTP
	
				try {
                        $mail->CharSet = 'utf-8';
                        $mail->SMTPDebug = false; // Enables SMTP debug information - SHOULD NOT be active on production servers!
                        											
                        $mail->SetFrom("<EMAIL>", $this->name);
                        
                        $mail->AddReplyTo($this->email, $this->name);
                        $mail->AddAddress($to, '');
                        							
                        $mail->Subject = $subject;
                        $mail->AltBody = 'To view the message, please use an HTML compatible email viewer!'; // optional - MsgHTML will create an alternate automatically
                        $mail->MsgHTML($message);
                        											
                        $mail->Send();
					}			
					catch (phpmailerException $e)
					{
					  echo $e->errorMessage(); //Pretty error messages from PHPMailer
					  exit();
					  return 0;
					} 
					catch (Exception $e)
					{
					  echo $e->getMessage(); //Boring error messages from anything else!
					  exit();
					  return 0;
					}
								
				echo "1";
				exit();
            }
		}
		else{
            echo "CAPTCHA failed or suspicious activity detected.";
            exit();
            return 0;
		}
	}
}

$objCurPage = new CurrentPage();

?>