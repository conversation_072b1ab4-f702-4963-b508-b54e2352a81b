<?php
require_once("../includes/config.inc.php");
require_once("../includes/class.CommonFunctions.php");
require_once("../includes/class.DatabaseManager.php");
require_once("../includes/class.LogonUser.php");
require_once("../includes/class.UserAccess.php");
require_once("../includes/interface.AppPage.php");
@session_start();

/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
/* Create a Class for this page to store some variables 
to be globally used by many functions in this Page */
class CurrentPage implements AppPage
{
	public $link_id;	// Database Link
	public $module_name;	// Current Module Name
	public $app_config;		// Application Configuration Settings
	public $userid;
	public $usrpwd;
	public $currentDate;
	public $user_name;
	public $user_gen;
	public $th_content;
	public $by_;
	public $th_id;
	
	//=== Class Constructor ===/
	function __construct()
	{
		global $config;		// Global Config Settings
		
		//--- Connect TO Database ---
		$this->link_id = DatabaseManager::iconnectDB();
		if(!$this->link_id)
		{
			die(DatabaseManager::isqlError());
		}
		//--- Set Current Module Name ---
		$this->module_name = "Admin Home";
		$this->cur_page_url = "home.php";
		$this->list_page_url = "home.php";
		$this->currentDate =date("F j, Y g:i a");
		$this->currentDate=date("y-m-d h:i:s A", strtotime($this->currentDate));
		//echo($this->currentDate); exit();
		//--- Check User Access permissions
		UserAccess::checkUserLogin("", "admin");		
				
		$this->getUserName();
		
	}
	
	//=== Class Destructor ===/
	function __destruct()
	{
		//--- Disconnect from Database ---
		DatabaseManager::idisconnectDB($this->link_id);
	}
	function getUserName()
	{
		if($_SESSION["currentLogonUser"]->cur_user_login_name != "")
		{
			//--- Edit Mode. Initialize fields by getting values from Database
			$tmpSql = "SELECT * from admin_details WHERE user_name = '" . $_SESSION["currentLogonUser"]->cur_user_login_name  . "'";
			//echo $tmpSql;
			$rs = $this->link_id->query($tmpSql);
			if( (!$rs) || (!($rec = $rs->fetch_array())) )
			{
				$_SESSION['app_error'] = "Record not found...";
				die("_");
			}
			else
			{
				//$this->user_id = $rec["user_id"];
				//echo $this->user_id;
				$this->user_name = $rec["user_name"];
				$this->user_gen = $rec["user_gender"];
				if(!$this->user_name="")
				{
					$tmpsql_new="SELECT * from thought";
					//echo $tmpsql_new; exit("--");
					$rs1 = $this->link_id->query($tmpsql_new);
					
					if( (!$rs1) || (!($rec1 = $rs1->fetch_array())) )
					{}
					else
					{
						$this->th_content = $rec1["content"];
						$this->by_= $rec1["by_"];
						$this->th_id = $rec1["th_id"];
					}
				}
			}
		}
	}
}
$objCurPage = new CurrentPage();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $config["APP_TITLE_ADMIN"]; ?></title>
<link href="css/admin.css" rel="stylesheet" />
<script type="text/javascript" src="js/jquery-1.10.1.min.js"></script>
<script type="text/javascript" src="js/jquery.form.3.10.js"></script>
<link href="css/font-awesome.min.css" rel="stylesheet" />
<link href="css/bootstrap.min.css" rel="stylesheet" />
<!--<link href="images/logo.png" rel="icon" type="image/x-icon" />-->
<script language="javascript" type="text/javascript">
$(document).ready(function(){
	$("#loadingImage").hide();	
	$('#cmdSave').click(function()
	{	
	    $("#loadingImage").hide();	
		validateSave();
	});
});
function validateSave()
{
	$("#loaderImg").html('<img src="images/bar-circle.gif" />');
	$("#loaderImg").show();
	$('#frmthrought').submit();
}
$('#frmthrought').ajaxForm({	
	target: '',
	success: function(data){
		$("#loadingImage").hide();
		if(data == 1)
		{	alert("Update Successfully");
			window.location="home.php";
			$("#loaderImg").empty();
			$("#loaderImg").hide();
			return false;					
		}
		else
		{
			$("#loaderImg").empty();
			$("#loaderImg").hide();	
			$('#showError').html(data);
			return false;
		}
	}			
});
</script>
</head>
<body>
<div id="header">
	<div class="container">
  <?php include_once("header.php") ?>
  </div>
</div>
<section class="container" style="margin-top:18px;">
  <div id="row">
    <aside class="col-md-3 sidebar sidebar-right">
      <?php include_once("lftmenu.php"); ?>
    </aside>
    <!--left_menu ends here-->
    <div class="col-md-8 maincontent" style="min-height:520px;">
    	<div class="alert alert-success">
  				<strong>Success!</strong> Welcome to Dashboard.
		</div>
    </div>
  </div>
  <div class="col-sm-1"></div>
</section>
<?php include_once('footer.php');?>
</body>
</html>